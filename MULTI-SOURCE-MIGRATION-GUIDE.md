# Multi-Source Business & Review Tracking Migration Guide

This guide covers the migration from a single-source (Google Maps only) system to a multi-source system that supports businesses and reviews from multiple platforms (Google Maps, Yelp, TripAdvisor, etc.).

## 🎯 What Changed

### Database Schema
- **New Tables**: `sources`, `business_sources`, `scraper_runs`, `business_scraper_runs`, `user_businesses`
- **Updated Tables**: `businesses` (removed Google-specific fields, added versioning), `reviews` (generalized for multi-source)
- **Business Deduplication**: Automatic detection and merging of same businesses from different sources
- **Version Tracking**: Each business update increments a version counter
- **Source Mapping**: Each business can have multiple source entries (Google Place ID, Yelp ID, etc.)

### Code Changes
- **New Service**: `MultiSourceDatabaseService` replaces `DatabaseService`
- **Updated API**: `/api/fetch-business` now uses multi-source logic
- **Updated Types**: TypeScript types updated for new schema
- **Business Logic**: Automatic deduplication and version management

## 🚀 Migration Steps

### 1. Create New Database
Since you're creating a fresh database, simply run the new schema:

```sql
-- Run this in your Supabase SQL Editor
-- File: db/schema.sql
```

### 2. Update Environment Variables
No changes needed - same Supabase and Apify credentials.

### 3. Test the New System
The application should work immediately with the new schema.

## 📊 New Features

### Business Deduplication
- Automatically detects duplicate businesses based on name, address, and location
- Merges businesses from different sources under one main entity
- Configurable similarity thresholds

### Version Tracking
- Each business update increments a version counter
- Track which scraper runs updated which businesses
- Audit trail of all changes

### Multi-Source Support
- Ready for Yelp, TripAdvisor, Foursquare integration
- Source-specific metadata storage
- Unified review system across all sources

### Enhanced Analytics
- Statistics by source
- Business update history
- Cross-source review aggregation

## 🔧 API Changes

### POST `/api/fetch-business`
**Before:**
```json
{
  "sessionId": "uuid",
  "savedBusinesses": 5,
  "savedReviews": 50
}
```

**After:**
```json
{
  "scraperRunId": "uuid",
  "savedBusinesses": 5,
  "savedReviews": 50,
  "newBusinesses": 3,
  "updatedBusinesses": 2
}
```

### GET `/api/fetch-business`
**Before:**
- `?sessionId=uuid` - Get session results

**After:**
- `?scraperRunId=uuid` - Get scraper run results
- Returns businesses with source information and reviews

## 🗄️ Database Schema Overview

### Core Tables
1. **`sources`** - Available data sources (Google, Yelp, etc.)
2. **`businesses`** - Main business entities (deduplicated)
3. **`business_sources`** - Maps businesses to their source-specific IDs
4. **`reviews`** - All reviews linked to businesses, tagged by source
5. **`scraper_runs`** - Tracks each scraping operation
6. **`business_scraper_runs`** - Version tracking for business updates
7. **`user_businesses`** - Future user-to-business relationships

### Key Features
- **UUID Primary Keys** for all tables
- **Foreign Key Constraints** with CASCADE delete
- **Indexes** on frequently queried columns
- **RLS (Row Level Security)** enabled
- **Triggers** for auto-updating timestamps
- **Views** for common queries
- **Functions** for business deduplication

## 🧪 Testing Checklist

### Basic Functionality
- [ ] Application starts without errors
- [ ] Can scrape Google Maps URLs
- [ ] Businesses are saved to new schema
- [ ] Reviews are linked correctly
- [ ] API returns expected format

### Multi-Source Logic
- [ ] Business deduplication works
- [ ] Version tracking increments
- [ ] Source mapping is created
- [ ] Scraper runs are tracked

### Data Integrity
- [ ] No duplicate businesses for same source
- [ ] Reviews are linked to correct businesses
- [ ] Foreign key constraints work
- [ ] Timestamps are updated correctly

### Performance
- [ ] Queries execute efficiently
- [ ] Indexes are being used
- [ ] Batch operations work for large datasets

## 🔍 Troubleshooting

### Common Issues

1. **"Source 'google' not found"**
   - Run the schema setup script to insert default sources
   - Check that sources table has data

2. **"Cannot find potential duplicates"**
   - Ensure the `find_potential_duplicates` function exists
   - Check function permissions

3. **Foreign key constraint errors**
   - Verify all referenced IDs exist
   - Check data types match

4. **Performance issues**
   - Verify indexes are created
   - Check query execution plans
   - Consider batch size adjustments

### Debug Queries

```sql
-- Check sources
SELECT * FROM sources;

-- Check business deduplication
SELECT * FROM find_potential_duplicates('Test Business', '123 Main St', 40.7128, -74.0060);

-- Check business with sources
SELECT * FROM businesses_with_sources LIMIT 5;

-- Check scraper runs
SELECT * FROM scraper_runs ORDER BY created_at DESC LIMIT 10;
```

## 🚀 Next Steps

### Immediate
1. Test with existing Google Maps URLs
2. Verify data integrity
3. Check API responses

### Future Enhancements
1. Add Yelp scraper integration
2. Add TripAdvisor scraper integration
3. Implement user authentication
4. Add business claiming functionality
5. Build analytics dashboard

## 📝 Notes

- The old `DatabaseService` is still available for reference
- All new development should use `MultiSourceDatabaseService`
- The schema supports future user management
- Business deduplication can be fine-tuned with similarity thresholds
- Version tracking provides full audit trail of changes

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section
2. Verify schema setup is complete
3. Check application logs for detailed errors
4. Test with simple single-business URLs first
