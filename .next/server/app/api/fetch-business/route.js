"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/fetch-business/route";
exports.ids = ["app/api/fetch-business/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:os");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffetch-business%2Froute&page=%2Fapi%2Ffetch-business%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffetch-business%2Froute.ts&appDir=%2FUsers%2F302720%2FRepositories%2Fapify%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2F302720%2FRepositories%2Fapify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffetch-business%2Froute&page=%2Fapi%2Ffetch-business%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffetch-business%2Froute.ts&appDir=%2FUsers%2F302720%2FRepositories%2Fapify%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2F302720%2FRepositories%2Fapify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_302720_Repositories_apify_src_app_api_fetch_business_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/fetch-business/route.ts */ \"(rsc)/./src/app/api/fetch-business/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/fetch-business/route\",\n        pathname: \"/api/fetch-business\",\n        filename: \"route\",\n        bundlePath: \"app/api/fetch-business/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Repositories/apify/src/app/api/fetch-business/route.ts\",\n    nextConfigOutput,\n    userland: _Users_302720_Repositories_apify_src_app_api_fetch_business_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/fetch-business/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffetch-business%2Froute&page=%2Fapi%2Ffetch-business%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffetch-business%2Froute.ts&appDir=%2FUsers%2F302720%2FRepositories%2Fapify%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2F302720%2FRepositories%2Fapify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/fetch-business/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/fetch-business/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _services_apify_scrapers_unified_scraper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/apify/scrapers/unified-scraper */ \"(rsc)/./src/services/apify/scrapers/unified-scraper.ts\");\n/* harmony import */ var _services_database_multi_source_database_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/database/multi-source-database-service */ \"(rsc)/./src/services/database/multi-source-database-service.ts\");\n/* harmony import */ var _services_apify_apify_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apify/apify-service */ \"(rsc)/./src/services/apify/apify-service.ts\");\n\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate request body\n        if (!body.urls || !Array.isArray(body.urls) || body.urls.length === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"URLs array is required and must not be empty\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate URLs\n        const validUrls = body.urls.filter((url)=>{\n            try {\n                const urlObj = new URL(url);\n                return urlObj.hostname.includes(\"google.com\") && urlObj.pathname.includes(\"/maps/\");\n            } catch  {\n                return false;\n            }\n        });\n        if (validUrls.length === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"No valid Google Maps URLs provided\"\n            }, {\n                status: 400\n            });\n        }\n        if (validUrls.length !== body.urls.length) {\n            console.warn(`⚠️ Filtered out ${body.urls.length - validUrls.length} invalid URLs`);\n        }\n        // Prepare scraper input\n        const scraperInput = {\n            urls: validUrls,\n            maxReviews: Math.min(body.maxReviews || 10, 100),\n            reviewsSort: body.reviewsSort || \"newest\",\n            language: body.language || \"en\",\n            extractContacts: body.extractContacts !== false,\n            extractHours: body.extractHours !== false,\n            extractPhotos: body.extractPhotos !== false,\n            maxPhotos: Math.min(body.maxPhotos || 10, 20)\n        };\n        console.log(\"\\uD83D\\uDE80 Starting unified business + reviews scraping...\");\n        console.log(`📍 URLs: ${validUrls.length}`);\n        console.log(`📝 Max reviews per business: ${scraperInput.maxReviews}`);\n        console.log(`🔄 Review sort: ${scraperInput.reviewsSort}`);\n        // Initialize services\n        const apifyToken = process.env.APIFY_TOKEN;\n        if (!apifyToken) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Apify token not configured\"\n            }, {\n                status: 500\n            });\n        }\n        const apifyService = new _services_apify_apify_service__WEBPACK_IMPORTED_MODULE_3__.ApifyService({\n            token: apifyToken\n        });\n        const unifiedScraper = new _services_apify_scrapers_unified_scraper__WEBPACK_IMPORTED_MODULE_1__.UnifiedScraper(apifyService.getConfig());\n        const databaseService = new _services_database_multi_source_database_service__WEBPACK_IMPORTED_MODULE_2__.MultiSourceDatabaseService();\n        // Get Google source (assuming we're scraping Google Maps)\n        const googleSource = await databaseService.getSourceByName(\"google\");\n        if (!googleSource) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Google source not found in database. Please run the schema setup first.\"\n            }, {\n                status: 500\n            });\n        }\n        // Create scraper run\n        const scraperRunId = await databaseService.createScraperRun({\n            sourceId: googleSource.id,\n            actorName: \"compass/crawler-google-places\",\n            parameters: {\n                maxReviews: scraperInput.maxReviews,\n                reviewsSort: scraperInput.reviewsSort,\n                language: scraperInput.language,\n                extractContacts: scraperInput.extractContacts,\n                extractHours: scraperInput.extractHours,\n                extractPhotos: scraperInput.extractPhotos,\n                maxPhotos: scraperInput.maxPhotos,\n                urls: validUrls\n            },\n            metadata: {\n                urlsCount: validUrls.length,\n                requestTimestamp: new Date().toISOString()\n            }\n        });\n        // Update scraper run status to running\n        await databaseService.updateScraperRun(scraperRunId, {\n            status: \"running\"\n        });\n        try {\n            // Execute scraping\n            const result = await unifiedScraper.fetchBusiness(scraperInput);\n            if (!result.success) {\n                await databaseService.updateScraperRun(scraperRunId, {\n                    status: \"failed\",\n                    completedAt: new Date().toISOString(),\n                    metadata: {\n                        error: result.error,\n                        executionTime: result.metadata?.executionTime\n                    }\n                });\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Scraping failed\",\n                    details: result.error,\n                    scraperRunId: scraperRunId\n                }, {\n                    status: 500\n                });\n            }\n            console.log(`✅ Scraping completed: ${result.data?.length || 0} businesses found`);\n            // Save data to database using multi-source logic\n            const saveResult = await databaseService.saveUnifiedData(\"google\", result.data || [], {\n                scraperRunId: scraperRunId,\n                metadata: {\n                    scraperType: \"unified\",\n                    apifyRunId: result.metadata?.runId,\n                    executionTime: result.metadata?.executionTime\n                }\n            });\n            // Get statistics\n            const stats = unifiedScraper.getStats(result.data || []);\n            // Update scraper run with final results\n            await databaseService.updateScraperRun(scraperRunId, {\n                status: \"completed\",\n                completedAt: new Date().toISOString(),\n                apifyRunId: result.metadata?.runId,\n                durationSeconds: result.metadata?.executionTime ? Math.round(result.metadata.executionTime / 1000) : undefined,\n                metadata: {\n                    businessesProcessed: result.data?.length || 0,\n                    businessesSaved: saveResult.savedBusinesses,\n                    reviewsSaved: saveResult.savedReviews,\n                    newBusinesses: saveResult.newBusinesses,\n                    updatedBusinesses: saveResult.updatedBusinesses\n                }\n            });\n            console.log(\"\\uD83D\\uDCBE Data saved successfully\");\n            console.log(`📊 Stats: ${stats.totalBusinesses} businesses, ${stats.totalReviews} reviews`);\n            console.log(`📊 Save Results: ${saveResult.savedBusinesses} businesses (${saveResult.newBusinesses} new, ${saveResult.updatedBusinesses} updated), ${saveResult.savedReviews} reviews`);\n            // Return success response\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                scraperRunId: scraperRunId,\n                data: {\n                    businesses: result.data || [],\n                    stats,\n                    saveResult\n                },\n                meta: {\n                    runId: result.metadata?.runId,\n                    executionTime: result.metadata?.executionTime,\n                    urlsProcessed: validUrls.length,\n                    urlsFiltered: body.urls.length - validUrls.length\n                }\n            });\n        } catch (scrapingError) {\n            console.error(\"❌ Scraping error:\", scrapingError);\n            await databaseService.updateScraperRun(scraperRunId, {\n                status: \"failed\",\n                completedAt: new Date().toISOString(),\n                metadata: {\n                    error: scrapingError instanceof Error ? scrapingError.message : \"Unknown scraping error\"\n                }\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Scraping failed\",\n                details: scrapingError instanceof Error ? scrapingError.message : \"Unknown error\",\n                scraperRunId: scraperRunId\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const scraperRunId = searchParams.get(\"scraperRunId\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const databaseService = new _services_database_multi_source_database_service__WEBPACK_IMPORTED_MODULE_2__.MultiSourceDatabaseService();\n        if (scraperRunId) {\n            // Get specific scraper run results\n            const scraperRun = await databaseService.getScraperRun(scraperRunId);\n            const businesses = await databaseService.getBusinessesWithSourcesAndReviews(limit);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                scraperRun,\n                businesses,\n                count: businesses.length\n            });\n        } else {\n            // Get recent businesses with sources and reviews\n            const businesses = await databaseService.getBusinessesWithSourcesAndReviews(limit);\n            const stats = await databaseService.getStats();\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                businesses,\n                stats,\n                count: businesses.length\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ GET API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch data\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/fetch-business/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/apify/apify-service.ts":
/*!*********************************************!*\
  !*** ./src/services/apify/apify-service.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApifyService: () => (/* binding */ ApifyService),\n/* harmony export */   createApifyService: () => (/* binding */ createApifyService),\n/* harmony export */   getApifyService: () => (/* binding */ getApifyService),\n/* harmony export */   resetApifyService: () => (/* binding */ resetApifyService)\n/* harmony export */ });\n/* harmony import */ var _scrapers_business_scraper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./scrapers/business-scraper */ \"(rsc)/./src/services/apify/scrapers/business-scraper.ts\");\n/* harmony import */ var _scrapers_unified_scraper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./scrapers/unified-scraper */ \"(rsc)/./src/services/apify/scrapers/unified-scraper.ts\");\n\n\n/**\n * Main Apify service that provides access to all scrapers\n */ class ApifyService {\n    constructor(config){\n        this.config = config;\n        this.validateConfig();\n    }\n    /**\n   * Get the business scraper instance (lazy initialization)\n   */ get businessScraper() {\n        if (!this._businessScraper) {\n            this._businessScraper = new _scrapers_business_scraper__WEBPACK_IMPORTED_MODULE_0__.BusinessScraper(this.config);\n        }\n        return this._businessScraper;\n    }\n    /**\n   * Get the unified scraper instance (lazy initialization)\n   */ get unifiedScraper() {\n        if (!this._unifiedScraper) {\n            this._unifiedScraper = new _scrapers_unified_scraper__WEBPACK_IMPORTED_MODULE_1__.UnifiedScraper(this.config);\n        }\n        return this._unifiedScraper;\n    }\n    /**\n   * Validate the service configuration\n   */ validateConfig() {\n        if (!this.config.token) {\n            throw new Error(\"Apify token is required\");\n        }\n    }\n    /**\n   * Get service configuration\n   */ getConfig() {\n        return {\n            ...this.config\n        };\n    }\n    /**\n   * Update service configuration\n   */ updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n        // Reset scrapers to pick up new config\n        this._businessScraper = undefined;\n        this._unifiedScraper = undefined;\n    }\n    /**\n   * Get information about all available scrapers\n   */ async getScrapersInfo() {\n        const scrapers = [\n            {\n                name: \"unifiedScraper\",\n                description: \"Scrapes Google Maps business information and reviews in one request\",\n                actorId: \"compass/crawler-google-places\",\n                instance: this.unifiedScraper\n            },\n            {\n                name: \"businessScraper\",\n                description: \"Scrapes Google Maps business information only\",\n                actorId: \"compass/crawler-google-places\",\n                instance: this.businessScraper\n            }\n        ];\n        const scrapersInfo = await Promise.all(scrapers.map(async (scraper)=>{\n            try {\n                const actorInfo = await scraper.instance.getActorInfo();\n                return {\n                    ...scraper,\n                    actorInfo,\n                    available: true\n                };\n            } catch (error) {\n                return {\n                    ...scraper,\n                    actorInfo: null,\n                    available: false,\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                };\n            }\n        }));\n        return scrapersInfo;\n    }\n    /**\n   * Test the service connection\n   */ async testConnection() {\n        try {\n            // Try to get info about the unified scraper actor\n            const actorInfo = await this.unifiedScraper.getActorInfo();\n            if (actorInfo) {\n                return {\n                    success: true,\n                    message: `Successfully connected to Apify. Actor \"${actorInfo.name}\" is available.`\n                };\n            } else {\n                return {\n                    success: false,\n                    message: \"Connected to Apify but could not retrieve actor information.\"\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Failed to connect to Apify: ${error instanceof Error ? error.message : \"Unknown error\"}`\n            };\n        }\n    }\n}\n/**\n * Factory function to create ApifyService instance\n */ function createApifyService(token, options) {\n    const config = {\n        token,\n        timeout: 300000,\n        retries: 3,\n        ...options\n    };\n    return new ApifyService(config);\n}\n/**\n * Singleton instance for the default service\n */ let defaultService = null;\n/**\n * Get or create the default Apify service instance\n */ function getApifyService(token) {\n    if (!defaultService) {\n        if (!token) {\n            throw new Error(\"Apify token is required to initialize the service\");\n        }\n        defaultService = createApifyService(token);\n    }\n    return defaultService;\n}\n/**\n * Reset the default service instance (useful for testing)\n */ function resetApifyService() {\n    defaultService = null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/apify/apify-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/apify/base-scraper.ts":
/*!********************************************!*\
  !*** ./src/services/apify/base-scraper.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseScraper: () => (/* binding */ BaseScraper)\n/* harmony export */ });\n/* harmony import */ var apify_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! apify-client */ \"(rsc)/./node_modules/apify-client/dist/index.mjs\");\n\nclass BaseScraper {\n    constructor(config){\n        this.config = config;\n        this.client = new apify_client__WEBPACK_IMPORTED_MODULE_0__.ApifyClient({\n            token: config.token\n        });\n    }\n    /**\n   * Common scraping logic that all scrapers can use\n   */ async executeScraper(input, options) {\n        const startTime = Date.now();\n        try {\n            console.log(`🚀 Starting ${this.constructor.name} with actor: ${this.getActorId()}`);\n            // Prepare the input for the specific actor\n            const actorInput = this.prepareInput(input);\n            console.log(\"\\uD83D\\uDCCB Actor input prepared:\", JSON.stringify(actorInput, null, 2));\n            // Run the Apify actor\n            const run = await this.client.actor(this.getActorId()).call(actorInput, {\n                timeout: options?.timeout || this.config.timeout,\n                memory: options?.memory,\n                build: options?.build\n            });\n            if (!run.defaultDatasetId) {\n                throw new Error(\"No dataset ID returned from the actor run\");\n            }\n            console.log(`✅ Actor run completed. Run ID: ${run.id}, Dataset ID: ${run.defaultDatasetId}`);\n            // Fetch all items from the dataset\n            const { items } = await this.client.dataset(run.defaultDatasetId).listItems();\n            console.log(`📋 Retrieved ${items.length} item(s) from dataset`);\n            // Process the results using the specific scraper's logic\n            const processedData = this.processResults(items);\n            const executionTime = Date.now() - startTime;\n            return {\n                success: true,\n                data: processedData,\n                message: `Successfully scraped ${processedData.length} item(s)`,\n                metadata: {\n                    runId: run.id,\n                    datasetId: run.defaultDatasetId,\n                    itemCount: items.length,\n                    executionTime\n                }\n            };\n        } catch (error) {\n            const executionTime = Date.now() - startTime;\n            console.error(`❌ ${this.constructor.name} failed:`, error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"Unknown error occurred\",\n                metadata: {\n                    executionTime\n                }\n            };\n        }\n    }\n    /**\n   * Validate that required configuration is present\n   */ validateConfig() {\n        if (!this.config.token) {\n            throw new Error(\"Apify token is required\");\n        }\n    }\n    /**\n   * Get actor information\n   */ async getActorInfo() {\n        try {\n            const actor = await this.client.actor(this.getActorId()).get();\n            if (!actor) {\n                return null;\n            }\n            return {\n                name: actor.name,\n                description: actor.description,\n                version: actor.defaultRunOptions?.build,\n                isPublic: actor.isPublic,\n                stats: actor.stats\n            };\n        } catch (error) {\n            console.error(\"Failed to get actor info:\", error);\n            return null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/apify/base-scraper.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/apify/scrapers/business-scraper.ts":
/*!*********************************************************!*\
  !*** ./src/services/apify/scrapers/business-scraper.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessScraper: () => (/* binding */ BusinessScraper)\n/* harmony export */ });\n/* harmony import */ var _base_scraper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../base-scraper */ \"(rsc)/./src/services/apify/base-scraper.ts\");\n\nclass BusinessScraper extends _base_scraper__WEBPACK_IMPORTED_MODULE_0__.BaseScraper {\n    constructor(config){\n        super(config);\n        this.validateConfig();\n    }\n    getActorId() {\n        // Using a different actor for business data extraction\n        // This could be 'compass/crawler-google-places' or another suitable actor\n        return \"compass/crawler-google-places\";\n    }\n    prepareInput(input) {\n        // Validate input\n        if (!input.urls || !Array.isArray(input.urls) || input.urls.length === 0) {\n            throw new Error(\"URLs array is required and must not be empty\");\n        }\n        return {\n            startUrls: input.urls.map((url)=>({\n                    url\n                })),\n            includeHistogram: false,\n            includeOpeningHours: input.extractHours !== false,\n            includePeopleAlsoSearch: false,\n            includeReviews: false,\n            includeImages: input.extractPhotos !== false,\n            maxImages: input.maxPhotos || 10,\n            reviewsSort: \"newest\",\n            language: \"en\",\n            maxReviews: 0,\n            exportPlaceUrls: false,\n            additionalInfo: input.extractContacts !== false\n        };\n    }\n    processResults(items) {\n        return items.map((item)=>({\n                url: item.url || item.searchPageUrl || \"\",\n                business: {\n                    id: item.placeId || item.id || \"\",\n                    name: item.title || item.name || \"\",\n                    address: item.address || item.location?.address || \"\",\n                    phone: item.phone || item.phoneNumber || \"\",\n                    website: item.website || item.websiteUrl || \"\",\n                    category: item.categoryName || item.category || \"\",\n                    rating: item.totalScore || item.rating || 0,\n                    totalReviews: item.reviewsCount || item.totalReviews || 0,\n                    priceLevel: item.priceLevel || \"\",\n                    hours: this.parseOpeningHours(item.openingHours || item.hours),\n                    coordinates: {\n                        lat: item.location?.lat || item.latitude || 0,\n                        lng: item.location?.lng || item.longitude || 0\n                    },\n                    photos: this.parsePhotos(item.images || item.photos || []),\n                    verified: item.isVerified || false,\n                    plusCode: item.plusCode || \"\",\n                    url: item.url || item.searchPageUrl || \"\"\n                },\n                extractedAt: new Date().toISOString()\n            }));\n    }\n    /**\n   * Parse opening hours from various formats\n   */ parseOpeningHours(hours) {\n        if (!hours) return {};\n        if (Array.isArray(hours)) {\n            const hoursObj = {};\n            hours.forEach((hour)=>{\n                if (typeof hour === \"string\") {\n                    // Parse \"Monday: 9:00 AM – 5:00 PM\" format\n                    const match = hour.match(/^(\\w+):\\s*(.+)$/);\n                    if (match) {\n                        hoursObj[match[1].toLowerCase()] = match[2];\n                    }\n                } else if (hour.day && hour.hours) {\n                    hoursObj[hour.day.toLowerCase()] = hour.hours;\n                }\n            });\n            return hoursObj;\n        }\n        if (typeof hours === \"object\") {\n            return hours;\n        }\n        return {};\n    }\n    /**\n   * Parse photos from various formats\n   */ parsePhotos(photos) {\n        if (!Array.isArray(photos)) return [];\n        return photos.slice(0, 10).map((photo)=>{\n            if (typeof photo === \"string\") {\n                return {\n                    url: photo\n                };\n            }\n            return {\n                url: photo.url || photo.src || photo.image || \"\",\n                caption: photo.caption || photo.alt || photo.title || undefined\n            };\n        }).filter((photo)=>photo.url);\n    }\n    /**\n   * Scrape Google Maps business data\n   */ async scrapeBusiness(input) {\n        console.log(\"\\uD83C\\uDFE2 Starting Google Maps business scraping...\");\n        console.log(`📍 URLs to scrape: ${input.urls.length}`);\n        console.log(`📞 Extract contacts: ${input.extractContacts !== false}`);\n        console.log(`🕒 Extract hours: ${input.extractHours !== false}`);\n        console.log(`📸 Extract photos: ${input.extractPhotos !== false}`);\n        return this.executeScraper(input);\n    }\n    /**\n   * Get business statistics from scraped data\n   */ getBusinessStats(results) {\n        const stats = {\n            totalBusinesses: results.length,\n            averageRating: 0,\n            categoryDistribution: {},\n            ratingDistribution: {},\n            businessesWithPhone: 0,\n            businessesWithWebsite: 0,\n            businessesWithHours: 0,\n            businessesWithPhotos: 0,\n            verifiedBusinesses: 0\n        };\n        let totalRatingSum = 0;\n        let businessesWithRating = 0;\n        results.forEach((result)=>{\n            const business = result.business;\n            // Rating statistics\n            if (business.rating > 0) {\n                totalRatingSum += business.rating;\n                businessesWithRating++;\n                const roundedRating = Math.round(business.rating);\n                stats.ratingDistribution[roundedRating] = (stats.ratingDistribution[roundedRating] || 0) + 1;\n            }\n            // Category statistics\n            if (business.category) {\n                stats.categoryDistribution[business.category] = (stats.categoryDistribution[business.category] || 0) + 1;\n            }\n            // Contact information\n            if (business.phone) stats.businessesWithPhone++;\n            if (business.website) stats.businessesWithWebsite++;\n            if (business.hours && Object.keys(business.hours).length > 0) stats.businessesWithHours++;\n            if (business.photos && business.photos.length > 0) stats.businessesWithPhotos++;\n            if (business.verified) stats.verifiedBusinesses++;\n        });\n        stats.averageRating = businessesWithRating > 0 ? totalRatingSum / businessesWithRating : 0;\n        return stats;\n    }\n    /**\n   * Filter businesses by criteria\n   */ filterBusinesses(results, filters) {\n        return results.filter((result)=>{\n            const business = result.business;\n            // Rating filter\n            if (filters.minRating && business.rating < filters.minRating) return false;\n            if (filters.maxRating && business.rating > filters.maxRating) return false;\n            // Category filter\n            if (filters.category && business.category !== filters.category) return false;\n            // Contact filters\n            if (filters.hasPhone && !business.phone) return false;\n            if (filters.hasWebsite && !business.website) return false;\n            if (filters.hasHours && (!business.hours || Object.keys(business.hours).length === 0)) return false;\n            // Verification filter\n            if (filters.verified !== undefined && business.verified !== filters.verified) return false;\n            // Reviews filter\n            if (filters.minReviews && business.totalReviews < filters.minReviews) return false;\n            return true;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/apify/scrapers/business-scraper.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/apify/scrapers/unified-scraper.ts":
/*!********************************************************!*\
  !*** ./src/services/apify/scrapers/unified-scraper.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedScraper: () => (/* binding */ UnifiedScraper)\n/* harmony export */ });\n/* harmony import */ var _base_scraper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../base-scraper */ \"(rsc)/./src/services/apify/base-scraper.ts\");\n\nclass UnifiedScraper extends _base_scraper__WEBPACK_IMPORTED_MODULE_0__.BaseScraper {\n    constructor(config){\n        super(config);\n        this.validateConfig();\n    }\n    getActorId() {\n        return \"compass/crawler-google-places\";\n    }\n    prepareInput(input) {\n        // Validate input\n        if (!input.urls || !Array.isArray(input.urls) || input.urls.length === 0) {\n            throw new Error(\"URLs array is required and must not be empty\");\n        }\n        return {\n            startUrls: input.urls.map((url)=>({\n                    url\n                })),\n            maxReviews: input.maxReviews || 10,\n            reviewsSort: input.reviewsSort || \"newest\",\n            language: input.language || \"en\",\n            scrapePlaceDetailPage: true,\n            scrapeReviewsPersonalData: true,\n            includeOpeningHours: input.extractHours !== false,\n            maxImages: input.extractPhotos !== false ? input.maxPhotos || 10 : 0,\n            scrapeContacts: input.extractContacts !== false,\n            reviewsOrigin: \"google\"\n        };\n    }\n    processResults(items) {\n        return items.map((item)=>({\n                business: this.extractBusinessData(item),\n                reviews: this.extractReviewsData(item.reviews || []),\n                extractedAt: new Date().toISOString()\n            }));\n    }\n    extractBusinessData(item) {\n        return {\n            googlePlaceId: item.placeId || \"\",\n            name: item.title || item.name || \"\",\n            address: item.address || \"\",\n            phone: item.phone || item.phoneNumber || \"\",\n            website: item.website || item.websiteUrl || \"\",\n            category: item.categoryName || item.category || \"\",\n            rating: item.totalScore || item.rating || 0,\n            totalReviews: item.reviewsCount || item.totalReviews || 0,\n            priceLevel: item.priceLevel || \"\",\n            coordinates: {\n                lat: item.location?.lat || item.latitude || 0,\n                lng: item.location?.lng || item.longitude || 0\n            },\n            verified: item.isVerified || false,\n            plusCode: item.plusCode || \"\",\n            openingHours: this.parseOpeningHours(item.openingHours || item.hours),\n            photos: this.parsePhotos(item.images || item.photos || [])\n        };\n    }\n    extractReviewsData(reviews) {\n        if (!Array.isArray(reviews)) return [];\n        return reviews.map((review)=>({\n                googleReviewId: review.id || review.reviewId || \"\",\n                authorName: review.name || review.author?.name || \"\",\n                authorId: review.reviewerId || review.author?.id || \"\",\n                authorAvatar: review.profilePhotoUrl || review.author?.avatar || \"\",\n                authorUrl: review.reviewerUrl || review.author?.url || \"\",\n                rating: review.stars || review.rating || 0,\n                reviewText: review.text || review.review || \"\",\n                language: review.language || \"\",\n                likes: review.likesCount || review.likes || 0,\n                publishedAt: review.publishedAtDate || review.publishedAt || new Date().toISOString(),\n                updatedAt: review.updatedAtDate || review.updatedAt || \"\",\n                replyText: review.responseFromOwnerText || review.reply || \"\",\n                replyPublishedAt: review.responseFromOwnerDate || review.replyPublishedAt || \"\",\n                images: this.parseReviewImages(review.reviewImageUrls || review.images || [])\n            }));\n    }\n    parseOpeningHours(hours) {\n        if (!hours) return {};\n        if (Array.isArray(hours)) {\n            const hoursObj = {};\n            hours.forEach((hour)=>{\n                if (typeof hour === \"string\") {\n                    // Parse \"Monday: 9:00 AM – 5:00 PM\" format\n                    const match = hour.match(/^(\\w+):\\s*(.+)$/);\n                    if (match) {\n                        hoursObj[match[1].toLowerCase()] = match[2];\n                    }\n                } else if (hour.day && hour.hours) {\n                    hoursObj[hour.day.toLowerCase()] = hour.hours;\n                }\n            });\n            return hoursObj;\n        }\n        if (typeof hours === \"object\") {\n            return hours;\n        }\n        return {};\n    }\n    parsePhotos(photos) {\n        if (!Array.isArray(photos)) return [];\n        return photos.slice(0, 10).map((photo)=>{\n            if (typeof photo === \"string\") {\n                return {\n                    url: photo\n                };\n            }\n            return {\n                url: photo.url || photo.src || photo.image || \"\",\n                caption: photo.caption || photo.alt || photo.title || undefined\n            };\n        }).filter((photo)=>photo.url);\n    }\n    parseReviewImages(images) {\n        if (!Array.isArray(images)) return [];\n        return images.map((image)=>{\n            if (typeof image === \"string\") {\n                return {\n                    url: image\n                };\n            }\n            return {\n                url: image.url || image.src || image,\n                caption: image.caption || image.alt || undefined\n            };\n        }).filter((image)=>image.url);\n    }\n    /**\n   * Scrape Google Maps business data with reviews\n   */ async fetchBusiness(input) {\n        console.log(\"\\uD83C\\uDFE2 Starting unified business + reviews scraping...\");\n        console.log(`📍 URLs to scrape: ${input.urls.length}`);\n        console.log(`📝 Max reviews per business: ${input.maxReviews || 10}`);\n        console.log(`🔄 Review sort: ${input.reviewsSort || \"newest\"}`);\n        console.log(`📞 Extract contacts: ${input.extractContacts !== false}`);\n        console.log(`🕒 Extract hours: ${input.extractHours !== false}`);\n        console.log(`📸 Extract photos: ${input.extractPhotos !== false}`);\n        return this.executeScraper(input);\n    }\n    /**\n   * Get statistics from scraped data\n   */ getStats(results) {\n        const stats = {\n            totalBusinesses: results.length,\n            totalReviews: 0,\n            averageBusinessRating: 0,\n            averageReviewRating: 0,\n            categoryDistribution: {},\n            languageDistribution: {},\n            businessesWithReviews: 0,\n            businessesWithPhone: 0,\n            businessesWithWebsite: 0,\n            totalLikes: 0\n        };\n        let businessRatingSum = 0;\n        let businessesWithRating = 0;\n        let reviewRatingSum = 0;\n        results.forEach((result)=>{\n            const business = result.business;\n            // Business statistics\n            if (business.rating > 0) {\n                businessRatingSum += business.rating;\n                businessesWithRating++;\n            }\n            if (business.category) {\n                stats.categoryDistribution[business.category] = (stats.categoryDistribution[business.category] || 0) + 1;\n            }\n            if (business.phone) stats.businessesWithPhone++;\n            if (business.website) stats.businessesWithWebsite++;\n            // Review statistics\n            if (result.reviews.length > 0) {\n                stats.businessesWithReviews++;\n                stats.totalReviews += result.reviews.length;\n                result.reviews.forEach((review)=>{\n                    reviewRatingSum += review.rating;\n                    stats.totalLikes += review.likes;\n                    if (review.language) {\n                        stats.languageDistribution[review.language] = (stats.languageDistribution[review.language] || 0) + 1;\n                    }\n                });\n            }\n        });\n        stats.averageBusinessRating = businessesWithRating > 0 ? businessRatingSum / businessesWithRating : 0;\n        stats.averageReviewRating = stats.totalReviews > 0 ? reviewRatingSum / stats.totalReviews : 0;\n        return stats;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/apify/scrapers/unified-scraper.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/database/multi-source-database-service.ts":
/*!****************************************************************!*\
  !*** ./src/services/database/multi-source-database-service.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiSourceDatabaseService: () => (/* binding */ MultiSourceDatabaseService)\n/* harmony export */ });\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase-client */ \"(rsc)/./src/services/database/supabase-client.ts\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n/**\n * Multi-Source Database Service for managing businesses and reviews from multiple sources\n */ class MultiSourceDatabaseService {\n    constructor(client){\n        if (client) {\n            this.client = client;\n        } else {\n            // Try to initialize Supabase if not already done\n            try {\n                this.client = (0,_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseAdmin)();\n            } catch (error) {\n                // If factory not initialized, initialize it\n                (0,_supabase_client__WEBPACK_IMPORTED_MODULE_0__.initializeSupabaseFromEnv)();\n                this.client = (0,_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseAdmin)();\n            }\n        }\n    }\n    /**\n   * Get source by name (e.g., 'google', 'yelp')\n   */ async getSourceByName(sourceName) {\n        const { data, error } = await this.client.from(\"sources\").select(\"*\").eq(\"name\", sourceName).single();\n        if (error) {\n            if (error.code === \"PGRST116\") {\n                return null;\n            }\n            throw new Error(`Failed to get source: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Create a new scraper run\n   */ async createScraperRun(data) {\n        const scraperRunId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const { error } = await this.client.from(\"scraper_runs\").insert({\n            id: scraperRunId,\n            source_id: data.sourceId,\n            actor_name: data.actorName,\n            status: \"pending\",\n            started_at: new Date().toISOString(),\n            parameters: data.parameters,\n            metadata: data.metadata\n        });\n        if (error) {\n            throw new Error(`Failed to create scraper run: ${error.message}`);\n        }\n        return scraperRunId;\n    }\n    /**\n   * Update scraper run status and metadata\n   */ async updateScraperRun(scraperRunId, updates) {\n        const updateData = {\n            updated_at: new Date().toISOString()\n        };\n        if (updates.status) updateData.status = updates.status;\n        if (updates.completedAt) updateData.completed_at = updates.completedAt;\n        if (updates.apifyRunId) updateData.apify_run_id = updates.apifyRunId;\n        if (updates.apifyDatasetId) updateData.apify_dataset_id = updates.apifyDatasetId;\n        if (updates.storagePath) updateData.storage_path = updates.storagePath;\n        if (updates.durationSeconds !== undefined) updateData.duration_seconds = updates.durationSeconds;\n        if (updates.cost !== undefined) updateData.cost = updates.cost;\n        if (updates.metadata) updateData.metadata = updates.metadata;\n        const { error } = await this.client.from(\"scraper_runs\").update(updateData).eq(\"id\", scraperRunId);\n        if (error) {\n            throw new Error(`Failed to update scraper run: ${error.message}`);\n        }\n    }\n    /**\n   * Find potential duplicate businesses using simple name and address matching\n   */ async findPotentialDuplicates(businessName, businessAddress, latitude, longitude, distanceThresholdKm = 0.1) {\n        // For now, use a simple approach to avoid the RPC function issue\n        // Look for businesses with similar names\n        const { data, error } = await this.client.from(\"businesses\").select(\"id, name, address, latitude, longitude\").ilike(\"name\", `%${businessName}%`).limit(10);\n        if (error) {\n            throw new Error(`Failed to find potential duplicates: ${error.message}`);\n        }\n        if (!data || data.length === 0) {\n            return [];\n        }\n        // Calculate similarity scores manually\n        return data.map((business)=>{\n            // Simple name similarity\n            const nameSimilarity = businessName.toLowerCase() === business.name.toLowerCase() ? 1.0 : businessName.toLowerCase().includes(business.name.toLowerCase()) || business.name.toLowerCase().includes(businessName.toLowerCase()) ? 0.8 : 0.0;\n            // Simple address similarity\n            const addressSimilarity = businessAddress.toLowerCase() === business.address.toLowerCase() ? 1.0 : businessAddress.toLowerCase().includes(business.address.toLowerCase()) || business.address.toLowerCase().includes(businessAddress.toLowerCase()) ? 0.6 : 0.0;\n            const similarity_score = (nameSimilarity + addressSimilarity) / 2.0;\n            // Calculate distance if coordinates are available\n            let distance_km;\n            if (latitude && longitude && business.latitude && business.longitude) {\n                const R = 6371 // Earth's radius in km\n                ;\n                const dLat = (business.latitude - latitude) * Math.PI / 180;\n                const dLon = (business.longitude - longitude) * Math.PI / 180;\n                const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(latitude * Math.PI / 180) * Math.cos(business.latitude * Math.PI / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n                distance_km = R * c;\n            }\n            return {\n                business_id: business.id,\n                name: business.name,\n                address: business.address,\n                similarity_score,\n                distance_km\n            };\n        }).filter((result)=>result.similarity_score > 0.3) // Only return reasonable matches\n        .sort((a, b)=>b.similarity_score - a.similarity_score) // Sort by similarity\n        ;\n    }\n    /**\n   * Check if a business already exists for a specific source\n   */ async getBusinessBySourceId(sourceId, sourceBusinessId) {\n        const { data, error } = await this.client.from(\"business_sources\").select(`\n        *,\n        businesses (*)\n      `).eq(\"source_id\", sourceId).eq(\"source_business_id\", sourceBusinessId).single();\n        if (error) {\n            if (error.code === \"PGRST116\") {\n                return null;\n            }\n            throw new Error(`Failed to get business by source ID: ${error.message}`);\n        }\n        return {\n            business: data.businesses,\n            businessSource: data\n        };\n    }\n    /**\n   * Deduplicate business - find existing or create new\n   */ async deduplicateBusiness(businessData, sourceId, sourceBusinessId) {\n        console.log(`🔍 Deduplicating business: ${businessData.name}`);\n        try {\n            // First check if this exact source business ID already exists\n            const existingSourceBusiness = await this.getBusinessBySourceId(sourceId, sourceBusinessId);\n            if (existingSourceBusiness) {\n                console.log(`✅ Found existing business by source ID: ${existingSourceBusiness.business.id}`);\n                return {\n                    isNewBusiness: false,\n                    businessId: existingSourceBusiness.business.id,\n                    existingBusiness: existingSourceBusiness.business\n                };\n            }\n            // Look for potential duplicates based on name, address, and location\n            console.log(`🔍 Looking for potential duplicates...`);\n            const potentialDuplicates = await this.findPotentialDuplicates(businessData.name, businessData.address, businessData.latitude, businessData.longitude);\n            console.log(`🔍 Found ${potentialDuplicates.length} potential duplicates`);\n            // If we find a high-confidence match (similarity > 0.8), use it\n            const highConfidenceMatch = potentialDuplicates.find((dup)=>dup.similarity_score > 0.8);\n            if (highConfidenceMatch) {\n                console.log(`✅ Found high-confidence match: ${highConfidenceMatch.name} (similarity: ${highConfidenceMatch.similarity_score})`);\n                const { data: existingBusiness, error } = await this.client.from(\"businesses\").select(\"*\").eq(\"id\", highConfidenceMatch.business_id).single();\n                if (error) {\n                    throw new Error(`Failed to get existing business: ${error.message}`);\n                }\n                return {\n                    isNewBusiness: false,\n                    businessId: highConfidenceMatch.business_id,\n                    existingBusiness: existingBusiness,\n                    similarBusinesses: potentialDuplicates\n                };\n            }\n            // No high-confidence match found, this is a new business\n            console.log(`✨ Creating new business: ${businessData.name}`);\n            return {\n                isNewBusiness: true,\n                businessId: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                similarBusinesses: potentialDuplicates.length > 0 ? potentialDuplicates : undefined\n            };\n        } catch (error) {\n            console.error(`❌ Error in deduplicateBusiness:`, error);\n            throw error;\n        }\n    }\n    /**\n   * Create or update a business\n   */ async upsertBusiness(businessData, scraperRunId) {\n        const businessId = businessData.id || (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Check if business already exists\n        const { data: existingBusiness } = await this.client.from(\"businesses\").select(\"*\").eq(\"id\", businessId).single();\n        const isUpdate = !!existingBusiness;\n        const currentVersion = existingBusiness?.version || 0;\n        const newVersion = currentVersion + 1;\n        const businessInsert = {\n            id: businessId,\n            name: businessData.name,\n            address: businessData.address,\n            phone: businessData.phone,\n            website: businessData.website,\n            category: businessData.category,\n            rating: businessData.rating || 0,\n            total_reviews: businessData.totalReviews || 0,\n            latitude: businessData.latitude,\n            longitude: businessData.longitude,\n            version: newVersion,\n            metadata: businessData.metadata || {}\n        };\n        const { data: business, error } = await this.client.from(\"businesses\").upsert(businessInsert, {\n            onConflict: \"id\",\n            ignoreDuplicates: false\n        }).select().single();\n        if (error) {\n            throw new Error(`Failed to upsert business: ${error.message}`);\n        }\n        // Track the version change in business_scraper_runs\n        if (isUpdate) {\n            await this.client.from(\"business_scraper_runs\").insert({\n                business_id: businessId,\n                scraper_run_id: scraperRunId,\n                version_before: currentVersion,\n                version_after: newVersion,\n                changes_made: {\n                    updated_fields: Object.keys(businessData),\n                    timestamp: new Date().toISOString()\n                }\n            });\n        }\n        return {\n            business,\n            versionIncremented: isUpdate\n        };\n    }\n    /**\n   * Create or update business source mapping\n   */ async upsertBusinessSource(businessId, sourceId, sourceBusinessId, sourceUrl, sourceMetadata) {\n        const businessSourceData = {\n            business_id: businessId,\n            source_id: sourceId,\n            source_business_id: sourceBusinessId,\n            source_url: sourceUrl,\n            source_metadata: sourceMetadata || {},\n            last_scraped_at: new Date().toISOString()\n        };\n        const { data, error } = await this.client.from(\"business_sources\").upsert(businessSourceData, {\n            onConflict: \"source_id,source_business_id\",\n            ignoreDuplicates: false\n        }).select().single();\n        if (error) {\n            throw new Error(`Failed to upsert business source: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Save reviews for a business\n   */ async saveReviews(businessId, sourceId, reviews) {\n        if (reviews.length === 0) return 0;\n        const reviewsData = reviews.map((review)=>({\n                business_id: businessId,\n                source_id: sourceId,\n                source_review_id: review.sourceReviewId,\n                author_name: review.authorName,\n                author_id: review.authorId,\n                author_avatar: review.authorAvatar,\n                rating: review.rating,\n                review_text: review.reviewText,\n                language: review.language,\n                likes: review.likes || 0,\n                published_at: review.publishedAt,\n                updated_review_at: review.updatedReviewAt,\n                reply_text: review.replyText,\n                reply_published_at: review.replyPublishedAt,\n                metadata: review.metadata || {}\n            }));\n        // Insert reviews in batches to avoid payload limits\n        const batchSize = 50;\n        let savedCount = 0;\n        for(let i = 0; i < reviewsData.length; i += batchSize){\n            const batch = reviewsData.slice(i, i + batchSize);\n            const { error } = await this.client.from(\"reviews\").upsert(batch, {\n                onConflict: \"source_id,source_review_id,business_id\",\n                ignoreDuplicates: true\n            });\n            if (error) {\n                console.error(`Failed to save reviews batch ${i}-${i + batch.length}:`, error);\n                throw new Error(`Failed to save reviews: ${error.message}`);\n            }\n            savedCount += batch.length;\n        }\n        return savedCount;\n    }\n    /**\n   * Save unified business + reviews data from scraping (main method)\n   */ async saveUnifiedData(sourceName, unifiedResults, options = {}) {\n        // Get source\n        const source = await this.getSourceByName(sourceName);\n        if (!source) {\n            throw new Error(`Source '${sourceName}' not found`);\n        }\n        let savedBusinesses = 0;\n        let savedReviews = 0;\n        let newBusinesses = 0;\n        let updatedBusinesses = 0;\n        const scraperRunId = options.scraperRunId || (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        console.log(`💾 Starting unified save for ${unifiedResults.length} businesses from ${sourceName}`);\n        for (const result of unifiedResults){\n            try {\n                // Deduplicate business\n                const deduplicationResult = await this.deduplicateBusiness({\n                    name: result.business.name,\n                    address: result.business.address,\n                    latitude: result.business.coordinates.lat,\n                    longitude: result.business.coordinates.lng\n                }, source.id, result.business.googlePlaceId);\n                // Upsert business\n                const { business, versionIncremented } = await this.upsertBusiness({\n                    id: deduplicationResult.businessId,\n                    name: result.business.name,\n                    address: result.business.address,\n                    phone: result.business.phone,\n                    website: result.business.website,\n                    category: result.business.category,\n                    rating: result.business.rating,\n                    totalReviews: result.business.totalReviews,\n                    latitude: result.business.coordinates.lat,\n                    longitude: result.business.coordinates.lng,\n                    metadata: {\n                        verified: result.business.verified,\n                        plusCode: result.business.plusCode,\n                        openingHours: result.business.openingHours,\n                        photos: result.business.photos,\n                        priceLevel: result.business.priceLevel,\n                        extractedAt: result.extractedAt,\n                        ...options.metadata\n                    }\n                }, scraperRunId);\n                // Create/update business source mapping\n                await this.upsertBusinessSource(business.id, source.id, result.business.googlePlaceId, undefined, {\n                    verified: result.business.verified,\n                    plusCode: result.business.plusCode,\n                    openingHours: result.business.openingHours,\n                    photos: result.business.photos,\n                    priceLevel: result.business.priceLevel\n                });\n                savedBusinesses++;\n                if (deduplicationResult.isNewBusiness) {\n                    newBusinesses++;\n                } else if (versionIncremented) {\n                    updatedBusinesses++;\n                }\n                console.log(`💾 Saved business: ${result.business.name} (${deduplicationResult.isNewBusiness ? \"new\" : \"existing\"})`);\n                // Save reviews if any\n                if (result.reviews && result.reviews.length > 0) {\n                    const reviewsSaved = await this.saveReviews(business.id, source.id, result.reviews.map((review)=>({\n                            sourceReviewId: review.googleReviewId,\n                            authorName: review.authorName,\n                            authorId: review.authorId,\n                            authorAvatar: review.authorAvatar,\n                            rating: review.rating,\n                            reviewText: review.reviewText,\n                            language: review.language,\n                            likes: review.likes,\n                            publishedAt: review.publishedAt,\n                            updatedReviewAt: review.updatedAt,\n                            replyText: review.replyText,\n                            replyPublishedAt: review.replyPublishedAt,\n                            metadata: {\n                                images: review.images || [],\n                                ...options.metadata\n                            }\n                        })));\n                    savedReviews += reviewsSaved;\n                    console.log(`💾 Saved ${reviewsSaved} reviews for ${result.business.name}`);\n                }\n            } catch (error) {\n                console.error(`Error processing business ${result.business.name}:`, error);\n            // Continue with other businesses instead of failing completely\n            }\n        }\n        console.log(`💾 Unified save complete: ${savedBusinesses} businesses (${newBusinesses} new, ${updatedBusinesses} updated), ${savedReviews} reviews`);\n        return {\n            scraperRunId,\n            savedBusinesses,\n            savedReviews,\n            newBusinesses,\n            updatedBusinesses\n        };\n    }\n    /**\n   * Get businesses with their sources and reviews\n   */ async getBusinessesWithSourcesAndReviews(limit = 10) {\n        const { data, error } = await this.client.from(\"businesses\").select(`\n        *,\n        business_sources (\n          *,\n          sources (*)\n        ),\n        reviews (*)\n      `).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            throw new Error(`Failed to get businesses with sources and reviews: ${error.message}`);\n        }\n        return data || [];\n    }\n    /**\n   * Get scraper run details\n   */ async getScraperRun(scraperRunId) {\n        const { data, error } = await this.client.from(\"scraper_runs\").select(\"*\").eq(\"id\", scraperRunId).single();\n        if (error) {\n            if (error.code === \"PGRST116\") {\n                return null;\n            }\n            throw new Error(`Failed to get scraper run: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Get database statistics\n   */ async getStats() {\n        const [sourcesResult, businessesResult, reviewsResult, scraperRunsResult] = await Promise.all([\n            this.client.from(\"sources\").select(\"count\"),\n            this.client.from(\"businesses\").select(\"count\"),\n            this.client.from(\"reviews\").select(\"count\"),\n            this.client.from(\"scraper_runs\").select(\"count\")\n        ]);\n        return {\n            totalSources: sourcesResult.data?.[0]?.count || 0,\n            totalBusinesses: businessesResult.data?.[0]?.count || 0,\n            totalReviews: reviewsResult.data?.[0]?.count || 0,\n            totalScraperRuns: scraperRunsResult.data?.[0]?.count || 0\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmljZXMvZGF0YWJhc2UvbXVsdGktc291cmNlLWRhdGFiYXNlLXNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRStFO0FBRTVDO0FBMkJuQzs7Q0FFQyxHQUNNLE1BQU1JO0lBR1hDLFlBQVlDLE1BQWlDLENBQUU7UUFDN0MsSUFBSUEsUUFBUTtZQUNWLElBQUksQ0FBQ0EsTUFBTSxHQUFHQTtRQUNoQixPQUFPO1lBQ0wsaURBQWlEO1lBQ2pELElBQUk7Z0JBQ0YsSUFBSSxDQUFDQSxNQUFNLEdBQUdOLGtFQUFnQkE7WUFDaEMsRUFBRSxPQUFPTyxPQUFPO2dCQUNkLDRDQUE0QztnQkFDNUNOLDJFQUF5QkE7Z0JBQ3pCLElBQUksQ0FBQ0ssTUFBTSxHQUFHTixrRUFBZ0JBO1lBQ2hDO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTVEsZ0JBQWdCQyxVQUFrQixFQUFxQztRQUMzRSxNQUFNLEVBQUVDLElBQUksRUFBRUgsS0FBSyxFQUFFLEdBQUcsTUFBTSxJQUFJLENBQUNELE1BQU0sQ0FDdENLLElBQUksQ0FBQyxXQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLFFBQVFKLFlBQ1hLLE1BQU07UUFFVCxJQUFJUCxPQUFPO1lBQ1QsSUFBSUEsTUFBTVEsSUFBSSxLQUFLLFlBQVk7Z0JBQzdCLE9BQU87WUFDVDtZQUNBLE1BQU0sSUFBSUMsTUFBTSxDQUFDLHNCQUFzQixFQUFFVCxNQUFNVSxPQUFPLENBQUMsQ0FBQztRQUMxRDtRQUVBLE9BQU9QO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELE1BQU1RLGlCQUFpQlIsSUFBb0IsRUFBbUI7UUFDNUQsTUFBTVMsZUFBZWhCLGdEQUFNQTtRQUUzQixNQUFNLEVBQUVJLEtBQUssRUFBRSxHQUFHLE1BQU0sSUFBSSxDQUFDRCxNQUFNLENBQ2hDSyxJQUFJLENBQUMsZ0JBQ0xTLE1BQU0sQ0FBQztZQUNOQyxJQUFJRjtZQUNKRyxXQUFXWixLQUFLYSxRQUFRO1lBQ3hCQyxZQUFZZCxLQUFLZSxTQUFTO1lBQzFCQyxRQUFRO1lBQ1JDLFlBQVksSUFBSUMsT0FBT0MsV0FBVztZQUNsQ0MsWUFBWXBCLEtBQUtvQixVQUFVO1lBQzNCQyxVQUFVckIsS0FBS3FCLFFBQVE7UUFDekI7UUFFRixJQUFJeEIsT0FBTztZQUNULE1BQU0sSUFBSVMsTUFBTSxDQUFDLDhCQUE4QixFQUFFVCxNQUFNVSxPQUFPLENBQUMsQ0FBQztRQUNsRTtRQUVBLE9BQU9FO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELE1BQU1hLGlCQUNKYixZQUFvQixFQUNwQmMsT0FTQyxFQUNjO1FBQ2YsTUFBTUMsYUFBa0I7WUFDdEJDLFlBQVksSUFBSVAsT0FBT0MsV0FBVztRQUNwQztRQUVBLElBQUlJLFFBQVFQLE1BQU0sRUFBRVEsV0FBV1IsTUFBTSxHQUFHTyxRQUFRUCxNQUFNO1FBQ3RELElBQUlPLFFBQVFHLFdBQVcsRUFBRUYsV0FBV0csWUFBWSxHQUFHSixRQUFRRyxXQUFXO1FBQ3RFLElBQUlILFFBQVFLLFVBQVUsRUFBRUosV0FBV0ssWUFBWSxHQUFHTixRQUFRSyxVQUFVO1FBQ3BFLElBQUlMLFFBQVFPLGNBQWMsRUFBRU4sV0FBV08sZ0JBQWdCLEdBQUdSLFFBQVFPLGNBQWM7UUFDaEYsSUFBSVAsUUFBUVMsV0FBVyxFQUFFUixXQUFXUyxZQUFZLEdBQUdWLFFBQVFTLFdBQVc7UUFDdEUsSUFBSVQsUUFBUVcsZUFBZSxLQUFLQyxXQUFXWCxXQUFXWSxnQkFBZ0IsR0FBR2IsUUFBUVcsZUFBZTtRQUNoRyxJQUFJWCxRQUFRYyxJQUFJLEtBQUtGLFdBQVdYLFdBQVdhLElBQUksR0FBR2QsUUFBUWMsSUFBSTtRQUM5RCxJQUFJZCxRQUFRRixRQUFRLEVBQUVHLFdBQVdILFFBQVEsR0FBR0UsUUFBUUYsUUFBUTtRQUU1RCxNQUFNLEVBQUV4QixLQUFLLEVBQUUsR0FBRyxNQUFNLElBQUksQ0FBQ0QsTUFBTSxDQUNoQ0ssSUFBSSxDQUFDLGdCQUNMcUMsTUFBTSxDQUFDZCxZQUNQckIsRUFBRSxDQUFDLE1BQU1NO1FBRVosSUFBSVosT0FBTztZQUNULE1BQU0sSUFBSVMsTUFBTSxDQUFDLDhCQUE4QixFQUFFVCxNQUFNVSxPQUFPLENBQUMsQ0FBQztRQUNsRTtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNZ0Msd0JBQ0pDLFlBQW9CLEVBQ3BCQyxlQUF1QixFQUN2QkMsUUFBaUIsRUFDakJDLFNBQWtCLEVBQ2xCQyxzQkFBOEIsR0FBRyxFQU8vQjtRQUNGLGlFQUFpRTtRQUNqRSx5Q0FBeUM7UUFDekMsTUFBTSxFQUFFNUMsSUFBSSxFQUFFSCxLQUFLLEVBQUUsR0FBRyxNQUFNLElBQUksQ0FBQ0QsTUFBTSxDQUN0Q0ssSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBQywwQ0FDUDJDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQyxFQUFFTCxhQUFhLENBQUMsQ0FBQyxFQUNqQ00sS0FBSyxDQUFDO1FBRVQsSUFBSWpELE9BQU87WUFDVCxNQUFNLElBQUlTLE1BQU0sQ0FBQyxxQ0FBcUMsRUFBRVQsTUFBTVUsT0FBTyxDQUFDLENBQUM7UUFDekU7UUFFQSxJQUFJLENBQUNQLFFBQVFBLEtBQUsrQyxNQUFNLEtBQUssR0FBRztZQUM5QixPQUFPLEVBQUU7UUFDWDtRQUVBLHVDQUF1QztRQUN2QyxPQUFPL0MsS0FBS2dELEdBQUcsQ0FBQ0MsQ0FBQUE7WUFDZCx5QkFBeUI7WUFDekIsTUFBTUMsaUJBQWlCVixhQUFhVyxXQUFXLE9BQU9GLFNBQVNHLElBQUksQ0FBQ0QsV0FBVyxLQUFLLE1BQ2xGLGFBQWNBLFdBQVcsR0FBR0UsUUFBUSxDQUFDSixTQUFTRyxJQUFJLENBQUNELFdBQVcsT0FDN0RGLFNBQVNHLElBQUksQ0FBQ0QsV0FBVyxHQUFHRSxRQUFRLENBQUNiLGFBQWFXLFdBQVcsTUFBTyxNQUFNO1lBRTdFLDRCQUE0QjtZQUM1QixNQUFNRyxvQkFBb0JiLGdCQUFnQlUsV0FBVyxPQUFPRixTQUFTTSxPQUFPLENBQUNKLFdBQVcsS0FBSyxNQUMzRixnQkFBaUJBLFdBQVcsR0FBR0UsUUFBUSxDQUFDSixTQUFTTSxPQUFPLENBQUNKLFdBQVcsT0FDbkVGLFNBQVNNLE9BQU8sQ0FBQ0osV0FBVyxHQUFHRSxRQUFRLENBQUNaLGdCQUFnQlUsV0FBVyxNQUFPLE1BQU07WUFFbkYsTUFBTUssbUJBQW1CLENBQUNOLGlCQUFpQkksaUJBQWdCLElBQUs7WUFFaEUsa0RBQWtEO1lBQ2xELElBQUlHO1lBQ0osSUFBSWYsWUFBWUMsYUFBYU0sU0FBU1AsUUFBUSxJQUFJTyxTQUFTTixTQUFTLEVBQUU7Z0JBQ3BFLE1BQU1lLElBQUksS0FBSyx1QkFBdUI7O2dCQUN0QyxNQUFNQyxPQUFPLENBQUNWLFNBQVNQLFFBQVEsR0FBR0EsUUFBTyxJQUFLa0IsS0FBS0MsRUFBRSxHQUFHO2dCQUN4RCxNQUFNQyxPQUFPLENBQUNiLFNBQVNOLFNBQVMsR0FBR0EsU0FBUSxJQUFLaUIsS0FBS0MsRUFBRSxHQUFHO2dCQUMxRCxNQUFNRSxJQUFJSCxLQUFLSSxHQUFHLENBQUNMLE9BQUssS0FBS0MsS0FBS0ksR0FBRyxDQUFDTCxPQUFLLEtBQ3pDQyxLQUFLSyxHQUFHLENBQUN2QixXQUFXa0IsS0FBS0MsRUFBRSxHQUFHLE9BQU9ELEtBQUtLLEdBQUcsQ0FBQ2hCLFNBQVNQLFFBQVEsR0FBR2tCLEtBQUtDLEVBQUUsR0FBRyxPQUM1RUQsS0FBS0ksR0FBRyxDQUFDRixPQUFLLEtBQUtGLEtBQUtJLEdBQUcsQ0FBQ0YsT0FBSztnQkFDbkMsTUFBTUksSUFBSSxJQUFJTixLQUFLTyxLQUFLLENBQUNQLEtBQUtRLElBQUksQ0FBQ0wsSUFBSUgsS0FBS1EsSUFBSSxDQUFDLElBQUVMO2dCQUNuRE4sY0FBY0MsSUFBSVE7WUFDcEI7WUFFQSxPQUFPO2dCQUNMRyxhQUFhcEIsU0FBU3RDLEVBQUU7Z0JBQ3hCeUMsTUFBTUgsU0FBU0csSUFBSTtnQkFDbkJHLFNBQVNOLFNBQVNNLE9BQU87Z0JBQ3pCQztnQkFDQUM7WUFDRjtRQUNGLEdBQUdhLE1BQU0sQ0FBQ0MsQ0FBQUEsU0FBVUEsT0FBT2YsZ0JBQWdCLEdBQUcsS0FBSyxpQ0FBaUM7U0FDakZnQixJQUFJLENBQUMsQ0FBQ1QsR0FBR1UsSUFBTUEsRUFBRWpCLGdCQUFnQixHQUFHTyxFQUFFUCxnQkFBZ0IsRUFBRSxxQkFBcUI7O0lBQ2xGO0lBRUE7O0dBRUMsR0FDRCxNQUFNa0Isc0JBQXNCN0QsUUFBZ0IsRUFBRThELGdCQUF3QixFQUc1RDtRQUNSLE1BQU0sRUFBRTNFLElBQUksRUFBRUgsS0FBSyxFQUFFLEdBQUcsTUFBTSxJQUFJLENBQUNELE1BQU0sQ0FDdENLLElBQUksQ0FBQyxvQkFDTEMsTUFBTSxDQUFDLENBQUM7OztNQUdULENBQUMsRUFDQUMsRUFBRSxDQUFDLGFBQWFVLFVBQ2hCVixFQUFFLENBQUMsc0JBQXNCd0Usa0JBQ3pCdkUsTUFBTTtRQUVULElBQUlQLE9BQU87WUFDVCxJQUFJQSxNQUFNUSxJQUFJLEtBQUssWUFBWTtnQkFDN0IsT0FBTztZQUNUO1lBQ0EsTUFBTSxJQUFJQyxNQUFNLENBQUMscUNBQXFDLEVBQUVULE1BQU1VLE9BQU8sQ0FBQyxDQUFDO1FBQ3pFO1FBRUEsT0FBTztZQUNMMEMsVUFBVSxLQUFjMkIsVUFBVTtZQUNsQ0MsZ0JBQWdCN0U7UUFDbEI7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTThFLG9CQUNKQyxZQUtDLEVBQ0RsRSxRQUFnQixFQUNoQjhELGdCQUF3QixFQUNjO1FBQ3RDSyxRQUFRQyxHQUFHLENBQUMsQ0FBQywyQkFBMkIsRUFBRUYsYUFBYTNCLElBQUksQ0FBQyxDQUFDO1FBRTdELElBQUk7WUFDRiw4REFBOEQ7WUFDOUQsTUFBTThCLHlCQUF5QixNQUFNLElBQUksQ0FBQ1IscUJBQXFCLENBQUM3RCxVQUFVOEQ7WUFFMUUsSUFBSU8sd0JBQXdCO2dCQUMxQkYsUUFBUUMsR0FBRyxDQUFDLENBQUMsd0NBQXdDLEVBQUVDLHVCQUF1QmpDLFFBQVEsQ0FBQ3RDLEVBQUUsQ0FBQyxDQUFDO2dCQUMzRixPQUFPO29CQUNMd0UsZUFBZTtvQkFDZkMsWUFBWUYsdUJBQXVCakMsUUFBUSxDQUFDdEMsRUFBRTtvQkFDOUMwRSxrQkFBa0JILHVCQUF1QmpDLFFBQVE7Z0JBQ25EO1lBQ0Y7WUFFQSxxRUFBcUU7WUFDckUrQixRQUFRQyxHQUFHLENBQUMsQ0FBQyxzQ0FBc0MsQ0FBQztZQUNwRCxNQUFNSyxzQkFBc0IsTUFBTSxJQUFJLENBQUMvQyx1QkFBdUIsQ0FDNUR3QyxhQUFhM0IsSUFBSSxFQUNqQjJCLGFBQWF4QixPQUFPLEVBQ3BCd0IsYUFBYXJDLFFBQVEsRUFDckJxQyxhQUFhcEMsU0FBUztZQUd4QnFDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLFNBQVMsRUFBRUssb0JBQW9CdkMsTUFBTSxDQUFDLHFCQUFxQixDQUFDO1lBRXpFLGdFQUFnRTtZQUNoRSxNQUFNd0Msc0JBQXNCRCxvQkFBb0JFLElBQUksQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSWpDLGdCQUFnQixHQUFHO1lBRW5GLElBQUkrQixxQkFBcUI7Z0JBQ3ZCUCxRQUFRQyxHQUFHLENBQUMsQ0FBQywrQkFBK0IsRUFBRU0sb0JBQW9CbkMsSUFBSSxDQUFDLGNBQWMsRUFBRW1DLG9CQUFvQi9CLGdCQUFnQixDQUFDLENBQUMsQ0FBQztnQkFFOUgsTUFBTSxFQUFFeEQsTUFBTXFGLGdCQUFnQixFQUFFeEYsS0FBSyxFQUFFLEdBQUcsTUFBTSxJQUFJLENBQUNELE1BQU0sQ0FDeERLLElBQUksQ0FBQyxjQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLE1BQU1vRixvQkFBb0JsQixXQUFXLEVBQ3hDakUsTUFBTTtnQkFFVCxJQUFJUCxPQUFPO29CQUNULE1BQU0sSUFBSVMsTUFBTSxDQUFDLGlDQUFpQyxFQUFFVCxNQUFNVSxPQUFPLENBQUMsQ0FBQztnQkFDckU7Z0JBRUEsT0FBTztvQkFDTDRFLGVBQWU7b0JBQ2ZDLFlBQVlHLG9CQUFvQmxCLFdBQVc7b0JBQzNDZ0Isa0JBQWtCQTtvQkFDbEJLLG1CQUFtQko7Z0JBQ3JCO1lBQ0Y7WUFFQSx5REFBeUQ7WUFDekROLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlCQUF5QixFQUFFRixhQUFhM0IsSUFBSSxDQUFDLENBQUM7WUFDM0QsT0FBTztnQkFDTCtCLGVBQWU7Z0JBQ2ZDLFlBQVkzRixnREFBTUE7Z0JBQ2xCaUcsbUJBQW1CSixvQkFBb0J2QyxNQUFNLEdBQUcsSUFBSXVDLHNCQUFzQm5EO1lBQzVFO1FBQ0YsRUFBRSxPQUFPdEMsT0FBTztZQUNkbUYsUUFBUW5GLEtBQUssQ0FBQyxDQUFDLCtCQUErQixDQUFDLEVBQUVBO1lBQ2pELE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTThGLGVBQ0paLFlBWUMsRUFDRHRFLFlBQW9CLEVBQ3NEO1FBQzFFLE1BQU0yRSxhQUFhTCxhQUFhcEUsRUFBRSxJQUFJbEIsZ0RBQU1BO1FBRTVDLG1DQUFtQztRQUNuQyxNQUFNLEVBQUVPLE1BQU1xRixnQkFBZ0IsRUFBRSxHQUFHLE1BQU0sSUFBSSxDQUFDekYsTUFBTSxDQUNqREssSUFBSSxDQUFDLGNBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTWlGLFlBQ1RoRixNQUFNO1FBRVQsTUFBTXdGLFdBQVcsQ0FBQyxDQUFDUDtRQUNuQixNQUFNUSxpQkFBaUJSLGtCQUFrQlMsV0FBVztRQUNwRCxNQUFNQyxhQUFhRixpQkFBaUI7UUFFcEMsTUFBTUcsaUJBQTZDO1lBQ2pEckYsSUFBSXlFO1lBQ0poQyxNQUFNMkIsYUFBYTNCLElBQUk7WUFDdkJHLFNBQVN3QixhQUFheEIsT0FBTztZQUM3QjBDLE9BQU9sQixhQUFha0IsS0FBSztZQUN6QkMsU0FBU25CLGFBQWFtQixPQUFPO1lBQzdCQyxVQUFVcEIsYUFBYW9CLFFBQVE7WUFDL0JDLFFBQVFyQixhQUFhcUIsTUFBTSxJQUFJO1lBQy9CQyxlQUFldEIsYUFBYXVCLFlBQVksSUFBSTtZQUM1QzVELFVBQVVxQyxhQUFhckMsUUFBUTtZQUMvQkMsV0FBV29DLGFBQWFwQyxTQUFTO1lBQ2pDbUQsU0FBU0M7WUFDVDFFLFVBQVUwRCxhQUFhMUQsUUFBUSxJQUFJLENBQUM7UUFDdEM7UUFFQSxNQUFNLEVBQUVyQixNQUFNaUQsUUFBUSxFQUFFcEQsS0FBSyxFQUFFLEdBQUcsTUFBTSxJQUFJLENBQUNELE1BQU0sQ0FDaERLLElBQUksQ0FBQyxjQUNMc0csTUFBTSxDQUFDUCxnQkFBZ0I7WUFDdEJRLFlBQVk7WUFDWkMsa0JBQWtCO1FBQ3BCLEdBQ0N2RyxNQUFNLEdBQ05FLE1BQU07UUFFVCxJQUFJUCxPQUFPO1lBQ1QsTUFBTSxJQUFJUyxNQUFNLENBQUMsMkJBQTJCLEVBQUVULE1BQU1VLE9BQU8sQ0FBQyxDQUFDO1FBQy9EO1FBRUEsb0RBQW9EO1FBQ3BELElBQUlxRixVQUFVO1lBQ1osTUFBTSxJQUFJLENBQUNoRyxNQUFNLENBQ2RLLElBQUksQ0FBQyx5QkFDTFMsTUFBTSxDQUFDO2dCQUNOMkQsYUFBYWU7Z0JBQ2JzQixnQkFBZ0JqRztnQkFDaEJrRyxnQkFBZ0JkO2dCQUNoQmUsZUFBZWI7Z0JBQ2ZjLGNBQWM7b0JBQ1pDLGdCQUFnQkMsT0FBT0MsSUFBSSxDQUFDakM7b0JBQzVCa0MsV0FBVyxJQUFJL0YsT0FBT0MsV0FBVztnQkFDbkM7WUFDRjtRQUNKO1FBRUEsT0FBTztZQUNMOEI7WUFDQWlFLG9CQUFvQnRCO1FBQ3RCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU11QixxQkFDSi9CLFVBQWtCLEVBQ2xCdkUsUUFBZ0IsRUFDaEI4RCxnQkFBd0IsRUFDeEJ5QyxTQUFrQixFQUNsQkMsY0FBb0MsRUFDQztRQUNyQyxNQUFNQyxxQkFBdUQ7WUFDM0RqRCxhQUFhZTtZQUNieEUsV0FBV0M7WUFDWDBHLG9CQUFvQjVDO1lBQ3BCNkMsWUFBWUo7WUFDWkssaUJBQWlCSixrQkFBa0IsQ0FBQztZQUNwQ0ssaUJBQWlCLElBQUl4RyxPQUFPQyxXQUFXO1FBQ3pDO1FBRUEsTUFBTSxFQUFFbkIsSUFBSSxFQUFFSCxLQUFLLEVBQUUsR0FBRyxNQUFNLElBQUksQ0FBQ0QsTUFBTSxDQUN0Q0ssSUFBSSxDQUFDLG9CQUNMc0csTUFBTSxDQUFDZSxvQkFBb0I7WUFDMUJkLFlBQVk7WUFDWkMsa0JBQWtCO1FBQ3BCLEdBQ0N2RyxNQUFNLEdBQ05FLE1BQU07UUFFVCxJQUFJUCxPQUFPO1lBQ1QsTUFBTSxJQUFJUyxNQUFNLENBQUMsa0NBQWtDLEVBQUVULE1BQU1VLE9BQU8sQ0FBQyxDQUFDO1FBQ3RFO1FBRUEsT0FBT1A7SUFDVDtJQUVBOztHQUVDLEdBQ0QsTUFBTTJILFlBQ0p2QyxVQUFrQixFQUNsQnZFLFFBQWdCLEVBQ2hCK0csT0FjRSxFQUNlO1FBQ2pCLElBQUlBLFFBQVE3RSxNQUFNLEtBQUssR0FBRyxPQUFPO1FBRWpDLE1BQU04RSxjQUF5Q0QsUUFBUTVFLEdBQUcsQ0FBQzhFLENBQUFBLFNBQVc7Z0JBQ3BFekQsYUFBYWU7Z0JBQ2J4RSxXQUFXQztnQkFDWGtILGtCQUFrQkQsT0FBT0UsY0FBYztnQkFDdkNDLGFBQWFILE9BQU9JLFVBQVU7Z0JBQzlCQyxXQUFXTCxPQUFPTSxRQUFRO2dCQUMxQkMsZUFBZVAsT0FBT1EsWUFBWTtnQkFDbENsQyxRQUFRMEIsT0FBTzFCLE1BQU07Z0JBQ3JCbUMsYUFBYVQsT0FBT1UsVUFBVTtnQkFDOUJDLFVBQVVYLE9BQU9XLFFBQVE7Z0JBQ3pCQyxPQUFPWixPQUFPWSxLQUFLLElBQUk7Z0JBQ3ZCQyxjQUFjYixPQUFPYyxXQUFXO2dCQUNoQ0MsbUJBQW1CZixPQUFPZ0IsZUFBZTtnQkFDekNDLFlBQVlqQixPQUFPa0IsU0FBUztnQkFDNUJDLG9CQUFvQm5CLE9BQU9vQixnQkFBZ0I7Z0JBQzNDN0gsVUFBVXlHLE9BQU96RyxRQUFRLElBQUksQ0FBQztZQUNoQztRQUVBLG9EQUFvRDtRQUNwRCxNQUFNOEgsWUFBWTtRQUNsQixJQUFJQyxhQUFhO1FBRWpCLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJeEIsWUFBWTlFLE1BQU0sRUFBRXNHLEtBQUtGLFVBQVc7WUFDdEQsTUFBTUcsUUFBUXpCLFlBQVkwQixLQUFLLENBQUNGLEdBQUdBLElBQUlGO1lBRXZDLE1BQU0sRUFBRXRKLEtBQUssRUFBRSxHQUFHLE1BQU0sSUFBSSxDQUFDRCxNQUFNLENBQ2hDSyxJQUFJLENBQUMsV0FDTHNHLE1BQU0sQ0FBQytDLE9BQU87Z0JBQ2I5QyxZQUFZO2dCQUNaQyxrQkFBa0I7WUFDcEI7WUFFRixJQUFJNUcsT0FBTztnQkFDVG1GLFFBQVFuRixLQUFLLENBQUMsQ0FBQyw2QkFBNkIsRUFBRXdKLEVBQUUsQ0FBQyxFQUFFQSxJQUFJQyxNQUFNdkcsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFbEQ7Z0JBQ3hFLE1BQU0sSUFBSVMsTUFBTSxDQUFDLHdCQUF3QixFQUFFVCxNQUFNVSxPQUFPLENBQUMsQ0FBQztZQUM1RDtZQUVBNkksY0FBY0UsTUFBTXZHLE1BQU07UUFDNUI7UUFFQSxPQUFPcUc7SUFDVDtJQUVBOztHQUVDLEdBQ0QsTUFBTUksZ0JBQ0p6SixVQUFrQixFQUNsQjBKLGNBQXNDLEVBQ3RDQyxVQUE4QixDQUFDLENBQUMsRUFPL0I7UUFDRCxhQUFhO1FBQ2IsTUFBTUMsU0FBUyxNQUFNLElBQUksQ0FBQzdKLGVBQWUsQ0FBQ0M7UUFDMUMsSUFBSSxDQUFDNEosUUFBUTtZQUNYLE1BQU0sSUFBSXJKLE1BQU0sQ0FBQyxRQUFRLEVBQUVQLFdBQVcsV0FBVyxDQUFDO1FBQ3BEO1FBRUEsSUFBSTZKLGtCQUFrQjtRQUN0QixJQUFJQyxlQUFlO1FBQ25CLElBQUlDLGdCQUFnQjtRQUNwQixJQUFJQyxvQkFBb0I7UUFFeEIsTUFBTXRKLGVBQWVpSixRQUFRakosWUFBWSxJQUFJaEIsZ0RBQU1BO1FBRW5EdUYsUUFBUUMsR0FBRyxDQUFDLENBQUMsNkJBQTZCLEVBQUV3RSxlQUFlMUcsTUFBTSxDQUFDLGlCQUFpQixFQUFFaEQsV0FBVyxDQUFDO1FBRWpHLEtBQUssTUFBTXdFLFVBQVVrRixlQUFnQjtZQUNuQyxJQUFJO2dCQUNGLHVCQUF1QjtnQkFDdkIsTUFBTU8sc0JBQXNCLE1BQU0sSUFBSSxDQUFDbEYsbUJBQW1CLENBQ3hEO29CQUNFMUIsTUFBTW1CLE9BQU90QixRQUFRLENBQUNHLElBQUk7b0JBQzFCRyxTQUFTZ0IsT0FBT3RCLFFBQVEsQ0FBQ00sT0FBTztvQkFDaENiLFVBQVU2QixPQUFPdEIsUUFBUSxDQUFDZ0gsV0FBVyxDQUFDQyxHQUFHO29CQUN6Q3ZILFdBQVc0QixPQUFPdEIsUUFBUSxDQUFDZ0gsV0FBVyxDQUFDRSxHQUFHO2dCQUM1QyxHQUNBUixPQUFPaEosRUFBRSxFQUNUNEQsT0FBT3RCLFFBQVEsQ0FBQ21ILGFBQWE7Z0JBRy9CLGtCQUFrQjtnQkFDbEIsTUFBTSxFQUFFbkgsUUFBUSxFQUFFaUUsa0JBQWtCLEVBQUUsR0FBRyxNQUFNLElBQUksQ0FBQ3ZCLGNBQWMsQ0FDaEU7b0JBQ0VoRixJQUFJcUosb0JBQW9CNUUsVUFBVTtvQkFDbENoQyxNQUFNbUIsT0FBT3RCLFFBQVEsQ0FBQ0csSUFBSTtvQkFDMUJHLFNBQVNnQixPQUFPdEIsUUFBUSxDQUFDTSxPQUFPO29CQUNoQzBDLE9BQU8xQixPQUFPdEIsUUFBUSxDQUFDZ0QsS0FBSztvQkFDNUJDLFNBQVMzQixPQUFPdEIsUUFBUSxDQUFDaUQsT0FBTztvQkFDaENDLFVBQVU1QixPQUFPdEIsUUFBUSxDQUFDa0QsUUFBUTtvQkFDbENDLFFBQVE3QixPQUFPdEIsUUFBUSxDQUFDbUQsTUFBTTtvQkFDOUJFLGNBQWMvQixPQUFPdEIsUUFBUSxDQUFDcUQsWUFBWTtvQkFDMUM1RCxVQUFVNkIsT0FBT3RCLFFBQVEsQ0FBQ2dILFdBQVcsQ0FBQ0MsR0FBRztvQkFDekN2SCxXQUFXNEIsT0FBT3RCLFFBQVEsQ0FBQ2dILFdBQVcsQ0FBQ0UsR0FBRztvQkFDMUM5SSxVQUFVO3dCQUNSZ0osVUFBVTlGLE9BQU90QixRQUFRLENBQUNvSCxRQUFRO3dCQUNsQ0MsVUFBVS9GLE9BQU90QixRQUFRLENBQUNxSCxRQUFRO3dCQUNsQ0MsY0FBY2hHLE9BQU90QixRQUFRLENBQUNzSCxZQUFZO3dCQUMxQ0MsUUFBUWpHLE9BQU90QixRQUFRLENBQUN1SCxNQUFNO3dCQUM5QkMsWUFBWWxHLE9BQU90QixRQUFRLENBQUN3SCxVQUFVO3dCQUN0Q0MsYUFBYW5HLE9BQU9tRyxXQUFXO3dCQUMvQixHQUFHaEIsUUFBUXJJLFFBQVE7b0JBQ3JCO2dCQUNGLEdBQ0FaO2dCQUdGLHdDQUF3QztnQkFDeEMsTUFBTSxJQUFJLENBQUMwRyxvQkFBb0IsQ0FDN0JsRSxTQUFTdEMsRUFBRSxFQUNYZ0osT0FBT2hKLEVBQUUsRUFDVDRELE9BQU90QixRQUFRLENBQUNtSCxhQUFhLEVBQzdCakksV0FDQTtvQkFDRWtJLFVBQVU5RixPQUFPdEIsUUFBUSxDQUFDb0gsUUFBUTtvQkFDbENDLFVBQVUvRixPQUFPdEIsUUFBUSxDQUFDcUgsUUFBUTtvQkFDbENDLGNBQWNoRyxPQUFPdEIsUUFBUSxDQUFDc0gsWUFBWTtvQkFDMUNDLFFBQVFqRyxPQUFPdEIsUUFBUSxDQUFDdUgsTUFBTTtvQkFDOUJDLFlBQVlsRyxPQUFPdEIsUUFBUSxDQUFDd0gsVUFBVTtnQkFDeEM7Z0JBR0ZiO2dCQUNBLElBQUlJLG9CQUFvQjdFLGFBQWEsRUFBRTtvQkFDckMyRTtnQkFDRixPQUFPLElBQUk1QyxvQkFBb0I7b0JBQzdCNkM7Z0JBQ0Y7Z0JBRUEvRSxRQUFRQyxHQUFHLENBQUMsQ0FBQyxtQkFBbUIsRUFBRVYsT0FBT3RCLFFBQVEsQ0FBQ0csSUFBSSxDQUFDLEVBQUUsRUFBRTRHLG9CQUFvQjdFLGFBQWEsR0FBRyxRQUFRLFdBQVcsQ0FBQyxDQUFDO2dCQUVwSCxzQkFBc0I7Z0JBQ3RCLElBQUlaLE9BQU9xRCxPQUFPLElBQUlyRCxPQUFPcUQsT0FBTyxDQUFDN0UsTUFBTSxHQUFHLEdBQUc7b0JBQy9DLE1BQU00SCxlQUFlLE1BQU0sSUFBSSxDQUFDaEQsV0FBVyxDQUN6QzFFLFNBQVN0QyxFQUFFLEVBQ1hnSixPQUFPaEosRUFBRSxFQUNUNEQsT0FBT3FELE9BQU8sQ0FBQzVFLEdBQUcsQ0FBQzhFLENBQUFBLFNBQVc7NEJBQzVCRSxnQkFBZ0JGLE9BQU84QyxjQUFjOzRCQUNyQzFDLFlBQVlKLE9BQU9JLFVBQVU7NEJBQzdCRSxVQUFVTixPQUFPTSxRQUFROzRCQUN6QkUsY0FBY1IsT0FBT1EsWUFBWTs0QkFDakNsQyxRQUFRMEIsT0FBTzFCLE1BQU07NEJBQ3JCb0MsWUFBWVYsT0FBT1UsVUFBVTs0QkFDN0JDLFVBQVVYLE9BQU9XLFFBQVE7NEJBQ3pCQyxPQUFPWixPQUFPWSxLQUFLOzRCQUNuQkUsYUFBYWQsT0FBT2MsV0FBVzs0QkFDL0JFLGlCQUFpQmhCLE9BQU8rQyxTQUFTOzRCQUNqQzdCLFdBQVdsQixPQUFPa0IsU0FBUzs0QkFDM0JFLGtCQUFrQnBCLE9BQU9vQixnQkFBZ0I7NEJBQ3pDN0gsVUFBVTtnQ0FDUnlKLFFBQVFoRCxPQUFPZ0QsTUFBTSxJQUFJLEVBQUU7Z0NBQzNCLEdBQUdwQixRQUFRckksUUFBUTs0QkFDckI7d0JBQ0Y7b0JBR0Z3SSxnQkFBZ0JjO29CQUNoQjNGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLFNBQVMsRUFBRTBGLGFBQWEsYUFBYSxFQUFFcEcsT0FBT3RCLFFBQVEsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Z0JBQzVFO1lBRUYsRUFBRSxPQUFPdkQsT0FBTztnQkFDZG1GLFFBQVFuRixLQUFLLENBQUMsQ0FBQywwQkFBMEIsRUFBRTBFLE9BQU90QixRQUFRLENBQUNHLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRXZEO1lBQ3BFLCtEQUErRDtZQUNqRTtRQUNGO1FBRUFtRixRQUFRQyxHQUFHLENBQUMsQ0FBQywwQkFBMEIsRUFBRTJFLGdCQUFnQixhQUFhLEVBQUVFLGNBQWMsTUFBTSxFQUFFQyxrQkFBa0IsV0FBVyxFQUFFRixhQUFhLFFBQVEsQ0FBQztRQUVuSixPQUFPO1lBQ0xwSjtZQUNBbUo7WUFDQUM7WUFDQUM7WUFDQUM7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNZ0IsbUNBQW1DakksUUFBZ0IsRUFBRSxFQUFrQjtRQUMzRSxNQUFNLEVBQUU5QyxJQUFJLEVBQUVILEtBQUssRUFBRSxHQUFHLE1BQU0sSUFBSSxDQUFDRCxNQUFNLENBQ3RDSyxJQUFJLENBQUMsY0FDTEMsTUFBTSxDQUFDLENBQUM7Ozs7Ozs7TUFPVCxDQUFDLEVBQ0E4SyxLQUFLLENBQUMsY0FBYztZQUFFQyxXQUFXO1FBQU0sR0FDdkNuSSxLQUFLLENBQUNBO1FBRVQsSUFBSWpELE9BQU87WUFDVCxNQUFNLElBQUlTLE1BQU0sQ0FBQyxtREFBbUQsRUFBRVQsTUFBTVUsT0FBTyxDQUFDLENBQUM7UUFDdkY7UUFFQSxPQUFPUCxRQUFRLEVBQUU7SUFDbkI7SUFFQTs7R0FFQyxHQUNELE1BQU1rTCxjQUFjekssWUFBb0IsRUFBMEM7UUFDaEYsTUFBTSxFQUFFVCxJQUFJLEVBQUVILEtBQUssRUFBRSxHQUFHLE1BQU0sSUFBSSxDQUFDRCxNQUFNLENBQ3RDSyxJQUFJLENBQUMsZ0JBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTU0sY0FDVEwsTUFBTTtRQUVULElBQUlQLE9BQU87WUFDVCxJQUFJQSxNQUFNUSxJQUFJLEtBQUssWUFBWTtnQkFDN0IsT0FBTztZQUNUO1lBQ0EsTUFBTSxJQUFJQyxNQUFNLENBQUMsMkJBQTJCLEVBQUVULE1BQU1VLE9BQU8sQ0FBQyxDQUFDO1FBQy9EO1FBRUEsT0FBT1A7SUFDVDtJQUVBOztHQUVDLEdBQ0QsTUFBTW1MLFdBS0g7UUFDRCxNQUFNLENBQ0pDLGVBQ0FDLGtCQUNBQyxlQUNBQyxrQkFDRCxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztZQUNwQixJQUFJLENBQUM3TCxNQUFNLENBQUNLLElBQUksQ0FBQyxXQUFXQyxNQUFNLENBQUM7WUFDbkMsSUFBSSxDQUFDTixNQUFNLENBQUNLLElBQUksQ0FBQyxjQUFjQyxNQUFNLENBQUM7WUFDdEMsSUFBSSxDQUFDTixNQUFNLENBQUNLLElBQUksQ0FBQyxXQUFXQyxNQUFNLENBQUM7WUFDbkMsSUFBSSxDQUFDTixNQUFNLENBQUNLLElBQUksQ0FBQyxnQkFBZ0JDLE1BQU0sQ0FBQztTQUN6QztRQUVELE9BQU87WUFDTHdMLGNBQWMsY0FBZTFMLElBQUksRUFBVSxDQUFDLEVBQUUsRUFBRTJMLFNBQVM7WUFDekRDLGlCQUFpQixpQkFBa0I1TCxJQUFJLEVBQVUsQ0FBQyxFQUFFLEVBQUUyTCxTQUFTO1lBQy9EckYsY0FBYyxjQUFldEcsSUFBSSxFQUFVLENBQUMsRUFBRSxFQUFFMkwsU0FBUztZQUN6REUsa0JBQWtCLGtCQUFtQjdMLElBQUksRUFBVSxDQUFDLEVBQUUsRUFBRTJMLFNBQVM7UUFDbkU7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNjcmFwZXIvLi9zcmMvc2VydmljZXMvZGF0YWJhc2UvbXVsdGktc291cmNlLWRhdGFiYXNlLXNlcnZpY2UudHM/NDU1OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdXBhYmFzZUNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcydcbmltcG9ydCB7IERhdGFiYXNlLCBUYWJsZXMsIFRhYmxlc0luc2VydCB9IGZyb20gJy4vdHlwZXMnXG5pbXBvcnQgeyBnZXRTdXBhYmFzZUFkbWluLCBpbml0aWFsaXplU3VwYWJhc2VGcm9tRW52IH0gZnJvbSAnLi9zdXBhYmFzZS1jbGllbnQnXG5pbXBvcnQgeyBVbmlmaWVkU2NyYXBlclJlc3VsdCB9IGZyb20gJy4uL2FwaWZ5L3NjcmFwZXJzL3VuaWZpZWQtc2NyYXBlcidcbmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnXG5cbmV4cG9ydCBpbnRlcmZhY2UgU2F2ZVVuaWZpZWRPcHRpb25zIHtcbiAgc2NyYXBlclJ1bklkPzogc3RyaW5nXG4gIG1ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PlxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFNjcmFwZXJSdW5EYXRhIHtcbiAgc291cmNlSWQ6IHN0cmluZ1xuICBhY3Rvck5hbWU6IHN0cmluZ1xuICBwYXJhbWV0ZXJzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gIG1ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PlxufVxuXG5leHBvcnQgaW50ZXJmYWNlIEJ1c2luZXNzRGVkdXBsaWNhdGlvblJlc3VsdCB7XG4gIGlzTmV3QnVzaW5lc3M6IGJvb2xlYW5cbiAgYnVzaW5lc3NJZDogc3RyaW5nXG4gIGV4aXN0aW5nQnVzaW5lc3M/OiBUYWJsZXM8J2J1c2luZXNzZXMnPlxuICBzaW1pbGFyQnVzaW5lc3Nlcz86IEFycmF5PHtcbiAgICBidXNpbmVzc19pZDogc3RyaW5nXG4gICAgbmFtZTogc3RyaW5nXG4gICAgYWRkcmVzczogc3RyaW5nXG4gICAgc2ltaWxhcml0eV9zY29yZTogbnVtYmVyXG4gICAgZGlzdGFuY2Vfa20/OiBudW1iZXJcbiAgfT5cbn1cblxuLyoqXG4gKiBNdWx0aS1Tb3VyY2UgRGF0YWJhc2UgU2VydmljZSBmb3IgbWFuYWdpbmcgYnVzaW5lc3NlcyBhbmQgcmV2aWV3cyBmcm9tIG11bHRpcGxlIHNvdXJjZXNcbiAqL1xuZXhwb3J0IGNsYXNzIE11bHRpU291cmNlRGF0YWJhc2VTZXJ2aWNlIHtcbiAgcHJpdmF0ZSBjbGllbnQ6IFN1cGFiYXNlQ2xpZW50PERhdGFiYXNlPlxuXG4gIGNvbnN0cnVjdG9yKGNsaWVudD86IFN1cGFiYXNlQ2xpZW50PERhdGFiYXNlPikge1xuICAgIGlmIChjbGllbnQpIHtcbiAgICAgIHRoaXMuY2xpZW50ID0gY2xpZW50XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFRyeSB0byBpbml0aWFsaXplIFN1cGFiYXNlIGlmIG5vdCBhbHJlYWR5IGRvbmVcbiAgICAgIHRyeSB7XG4gICAgICAgIHRoaXMuY2xpZW50ID0gZ2V0U3VwYWJhc2VBZG1pbigpXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAvLyBJZiBmYWN0b3J5IG5vdCBpbml0aWFsaXplZCwgaW5pdGlhbGl6ZSBpdFxuICAgICAgICBpbml0aWFsaXplU3VwYWJhc2VGcm9tRW52KClcbiAgICAgICAgdGhpcy5jbGllbnQgPSBnZXRTdXBhYmFzZUFkbWluKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IHNvdXJjZSBieSBuYW1lIChlLmcuLCAnZ29vZ2xlJywgJ3llbHAnKVxuICAgKi9cbiAgYXN5bmMgZ2V0U291cmNlQnlOYW1lKHNvdXJjZU5hbWU6IHN0cmluZyk6IFByb21pc2U8VGFibGVzPCdzb3VyY2VzJz4gfCBudWxsPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgdGhpcy5jbGllbnRcbiAgICAgIC5mcm9tKCdzb3VyY2VzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCduYW1lJywgc291cmNlTmFtZSlcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBpZiAoZXJyb3IuY29kZSA9PT0gJ1BHUlNUMTE2JykgeyAvLyBObyByb3dzIHJldHVybmVkXG4gICAgICAgIHJldHVybiBudWxsXG4gICAgICB9XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBnZXQgc291cmNlOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBzY3JhcGVyIHJ1blxuICAgKi9cbiAgYXN5bmMgY3JlYXRlU2NyYXBlclJ1bihkYXRhOiBTY3JhcGVyUnVuRGF0YSk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgY29uc3Qgc2NyYXBlclJ1bklkID0gdXVpZHY0KClcbiAgICBcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCB0aGlzLmNsaWVudFxuICAgICAgLmZyb20oJ3NjcmFwZXJfcnVucycpXG4gICAgICAuaW5zZXJ0KHtcbiAgICAgICAgaWQ6IHNjcmFwZXJSdW5JZCxcbiAgICAgICAgc291cmNlX2lkOiBkYXRhLnNvdXJjZUlkLFxuICAgICAgICBhY3Rvcl9uYW1lOiBkYXRhLmFjdG9yTmFtZSxcbiAgICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICAgIHN0YXJ0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcGFyYW1ldGVyczogZGF0YS5wYXJhbWV0ZXJzLFxuICAgICAgICBtZXRhZGF0YTogZGF0YS5tZXRhZGF0YSxcbiAgICAgIH0pXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBzY3JhcGVyIHJ1bjogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIHNjcmFwZXJSdW5JZFxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBzY3JhcGVyIHJ1biBzdGF0dXMgYW5kIG1ldGFkYXRhXG4gICAqL1xuICBhc3luYyB1cGRhdGVTY3JhcGVyUnVuKFxuICAgIHNjcmFwZXJSdW5JZDogc3RyaW5nLFxuICAgIHVwZGF0ZXM6IHtcbiAgICAgIHN0YXR1cz86ICdwZW5kaW5nJyB8ICdydW5uaW5nJyB8ICdjb21wbGV0ZWQnIHwgJ2ZhaWxlZCdcbiAgICAgIGNvbXBsZXRlZEF0Pzogc3RyaW5nXG4gICAgICBhcGlmeVJ1bklkPzogc3RyaW5nXG4gICAgICBhcGlmeURhdGFzZXRJZD86IHN0cmluZ1xuICAgICAgc3RvcmFnZVBhdGg/OiBzdHJpbmdcbiAgICAgIGR1cmF0aW9uU2Vjb25kcz86IG51bWJlclxuICAgICAgY29zdD86IG51bWJlclxuICAgICAgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgfVxuICApOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCB1cGRhdGVEYXRhOiBhbnkgPSB7XG4gICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgfVxuXG4gICAgaWYgKHVwZGF0ZXMuc3RhdHVzKSB1cGRhdGVEYXRhLnN0YXR1cyA9IHVwZGF0ZXMuc3RhdHVzXG4gICAgaWYgKHVwZGF0ZXMuY29tcGxldGVkQXQpIHVwZGF0ZURhdGEuY29tcGxldGVkX2F0ID0gdXBkYXRlcy5jb21wbGV0ZWRBdFxuICAgIGlmICh1cGRhdGVzLmFwaWZ5UnVuSWQpIHVwZGF0ZURhdGEuYXBpZnlfcnVuX2lkID0gdXBkYXRlcy5hcGlmeVJ1bklkXG4gICAgaWYgKHVwZGF0ZXMuYXBpZnlEYXRhc2V0SWQpIHVwZGF0ZURhdGEuYXBpZnlfZGF0YXNldF9pZCA9IHVwZGF0ZXMuYXBpZnlEYXRhc2V0SWRcbiAgICBpZiAodXBkYXRlcy5zdG9yYWdlUGF0aCkgdXBkYXRlRGF0YS5zdG9yYWdlX3BhdGggPSB1cGRhdGVzLnN0b3JhZ2VQYXRoXG4gICAgaWYgKHVwZGF0ZXMuZHVyYXRpb25TZWNvbmRzICE9PSB1bmRlZmluZWQpIHVwZGF0ZURhdGEuZHVyYXRpb25fc2Vjb25kcyA9IHVwZGF0ZXMuZHVyYXRpb25TZWNvbmRzXG4gICAgaWYgKHVwZGF0ZXMuY29zdCAhPT0gdW5kZWZpbmVkKSB1cGRhdGVEYXRhLmNvc3QgPSB1cGRhdGVzLmNvc3RcbiAgICBpZiAodXBkYXRlcy5tZXRhZGF0YSkgdXBkYXRlRGF0YS5tZXRhZGF0YSA9IHVwZGF0ZXMubWV0YWRhdGFcblxuICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHRoaXMuY2xpZW50XG4gICAgICAuZnJvbSgnc2NyYXBlcl9ydW5zJylcbiAgICAgIC51cGRhdGUodXBkYXRlRGF0YSlcbiAgICAgIC5lcSgnaWQnLCBzY3JhcGVyUnVuSWQpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHVwZGF0ZSBzY3JhcGVyIHJ1bjogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEZpbmQgcG90ZW50aWFsIGR1cGxpY2F0ZSBidXNpbmVzc2VzIHVzaW5nIHNpbXBsZSBuYW1lIGFuZCBhZGRyZXNzIG1hdGNoaW5nXG4gICAqL1xuICBhc3luYyBmaW5kUG90ZW50aWFsRHVwbGljYXRlcyhcbiAgICBidXNpbmVzc05hbWU6IHN0cmluZyxcbiAgICBidXNpbmVzc0FkZHJlc3M6IHN0cmluZyxcbiAgICBsYXRpdHVkZT86IG51bWJlcixcbiAgICBsb25naXR1ZGU/OiBudW1iZXIsXG4gICAgZGlzdGFuY2VUaHJlc2hvbGRLbTogbnVtYmVyID0gMC4xXG4gICk6IFByb21pc2U8QXJyYXk8e1xuICAgIGJ1c2luZXNzX2lkOiBzdHJpbmdcbiAgICBuYW1lOiBzdHJpbmdcbiAgICBhZGRyZXNzOiBzdHJpbmdcbiAgICBzaW1pbGFyaXR5X3Njb3JlOiBudW1iZXJcbiAgICBkaXN0YW5jZV9rbT86IG51bWJlclxuICB9Pj4ge1xuICAgIC8vIEZvciBub3csIHVzZSBhIHNpbXBsZSBhcHByb2FjaCB0byBhdm9pZCB0aGUgUlBDIGZ1bmN0aW9uIGlzc3VlXG4gICAgLy8gTG9vayBmb3IgYnVzaW5lc3NlcyB3aXRoIHNpbWlsYXIgbmFtZXNcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCB0aGlzLmNsaWVudFxuICAgICAgLmZyb20oJ2J1c2luZXNzZXMnKVxuICAgICAgLnNlbGVjdCgnaWQsIG5hbWUsIGFkZHJlc3MsIGxhdGl0dWRlLCBsb25naXR1ZGUnKVxuICAgICAgLmlsaWtlKCduYW1lJywgYCUke2J1c2luZXNzTmFtZX0lYClcbiAgICAgIC5saW1pdCgxMClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmluZCBwb3RlbnRpYWwgZHVwbGljYXRlczogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgaWYgKCFkYXRhIHx8IGRhdGEubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICAvLyBDYWxjdWxhdGUgc2ltaWxhcml0eSBzY29yZXMgbWFudWFsbHlcbiAgICByZXR1cm4gZGF0YS5tYXAoYnVzaW5lc3MgPT4ge1xuICAgICAgLy8gU2ltcGxlIG5hbWUgc2ltaWxhcml0eVxuICAgICAgY29uc3QgbmFtZVNpbWlsYXJpdHkgPSBidXNpbmVzc05hbWUudG9Mb3dlckNhc2UoKSA9PT0gYnVzaW5lc3MubmFtZS50b0xvd2VyQ2FzZSgpID8gMS4wIDpcbiAgICAgICAgKGJ1c2luZXNzTmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGJ1c2luZXNzLm5hbWUudG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgICAgIGJ1c2luZXNzLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhidXNpbmVzc05hbWUudG9Mb3dlckNhc2UoKSkpID8gMC44IDogMC4wXG5cbiAgICAgIC8vIFNpbXBsZSBhZGRyZXNzIHNpbWlsYXJpdHlcbiAgICAgIGNvbnN0IGFkZHJlc3NTaW1pbGFyaXR5ID0gYnVzaW5lc3NBZGRyZXNzLnRvTG93ZXJDYXNlKCkgPT09IGJ1c2luZXNzLmFkZHJlc3MudG9Mb3dlckNhc2UoKSA/IDEuMCA6XG4gICAgICAgIChidXNpbmVzc0FkZHJlc3MudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhidXNpbmVzcy5hZGRyZXNzLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICBidXNpbmVzcy5hZGRyZXNzLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoYnVzaW5lc3NBZGRyZXNzLnRvTG93ZXJDYXNlKCkpKSA/IDAuNiA6IDAuMFxuXG4gICAgICBjb25zdCBzaW1pbGFyaXR5X3Njb3JlID0gKG5hbWVTaW1pbGFyaXR5ICsgYWRkcmVzc1NpbWlsYXJpdHkpIC8gMi4wXG5cbiAgICAgIC8vIENhbGN1bGF0ZSBkaXN0YW5jZSBpZiBjb29yZGluYXRlcyBhcmUgYXZhaWxhYmxlXG4gICAgICBsZXQgZGlzdGFuY2Vfa206IG51bWJlciB8IHVuZGVmaW5lZFxuICAgICAgaWYgKGxhdGl0dWRlICYmIGxvbmdpdHVkZSAmJiBidXNpbmVzcy5sYXRpdHVkZSAmJiBidXNpbmVzcy5sb25naXR1ZGUpIHtcbiAgICAgICAgY29uc3QgUiA9IDYzNzEgLy8gRWFydGgncyByYWRpdXMgaW4ga21cbiAgICAgICAgY29uc3QgZExhdCA9IChidXNpbmVzcy5sYXRpdHVkZSAtIGxhdGl0dWRlKSAqIE1hdGguUEkgLyAxODBcbiAgICAgICAgY29uc3QgZExvbiA9IChidXNpbmVzcy5sb25naXR1ZGUgLSBsb25naXR1ZGUpICogTWF0aC5QSSAvIDE4MFxuICAgICAgICBjb25zdCBhID0gTWF0aC5zaW4oZExhdC8yKSAqIE1hdGguc2luKGRMYXQvMikgK1xuICAgICAgICAgIE1hdGguY29zKGxhdGl0dWRlICogTWF0aC5QSSAvIDE4MCkgKiBNYXRoLmNvcyhidXNpbmVzcy5sYXRpdHVkZSAqIE1hdGguUEkgLyAxODApICpcbiAgICAgICAgICBNYXRoLnNpbihkTG9uLzIpICogTWF0aC5zaW4oZExvbi8yKVxuICAgICAgICBjb25zdCBjID0gMiAqIE1hdGguYXRhbjIoTWF0aC5zcXJ0KGEpLCBNYXRoLnNxcnQoMS1hKSlcbiAgICAgICAgZGlzdGFuY2Vfa20gPSBSICogY1xuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBidXNpbmVzc19pZDogYnVzaW5lc3MuaWQsXG4gICAgICAgIG5hbWU6IGJ1c2luZXNzLm5hbWUsXG4gICAgICAgIGFkZHJlc3M6IGJ1c2luZXNzLmFkZHJlc3MsXG4gICAgICAgIHNpbWlsYXJpdHlfc2NvcmUsXG4gICAgICAgIGRpc3RhbmNlX2ttXG4gICAgICB9XG4gICAgfSkuZmlsdGVyKHJlc3VsdCA9PiByZXN1bHQuc2ltaWxhcml0eV9zY29yZSA+IDAuMykgLy8gT25seSByZXR1cm4gcmVhc29uYWJsZSBtYXRjaGVzXG4gICAgICAuc29ydCgoYSwgYikgPT4gYi5zaW1pbGFyaXR5X3Njb3JlIC0gYS5zaW1pbGFyaXR5X3Njb3JlKSAvLyBTb3J0IGJ5IHNpbWlsYXJpdHlcbiAgfVxuXG4gIC8qKlxuICAgKiBDaGVjayBpZiBhIGJ1c2luZXNzIGFscmVhZHkgZXhpc3RzIGZvciBhIHNwZWNpZmljIHNvdXJjZVxuICAgKi9cbiAgYXN5bmMgZ2V0QnVzaW5lc3NCeVNvdXJjZUlkKHNvdXJjZUlkOiBzdHJpbmcsIHNvdXJjZUJ1c2luZXNzSWQ6IHN0cmluZyk6IFByb21pc2U8e1xuICAgIGJ1c2luZXNzOiBUYWJsZXM8J2J1c2luZXNzZXMnPlxuICAgIGJ1c2luZXNzU291cmNlOiBUYWJsZXM8J2J1c2luZXNzX3NvdXJjZXMnPlxuICB9IHwgbnVsbD4ge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHRoaXMuY2xpZW50XG4gICAgICAuZnJvbSgnYnVzaW5lc3Nfc291cmNlcycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgYnVzaW5lc3NlcyAoKilcbiAgICAgIGApXG4gICAgICAuZXEoJ3NvdXJjZV9pZCcsIHNvdXJjZUlkKVxuICAgICAgLmVxKCdzb3VyY2VfYnVzaW5lc3NfaWQnLCBzb3VyY2VCdXNpbmVzc0lkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGlmIChlcnJvci5jb2RlID09PSAnUEdSU1QxMTYnKSB7IC8vIE5vIHJvd3MgcmV0dXJuZWRcbiAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgIH1cbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCBidXNpbmVzcyBieSBzb3VyY2UgSUQ6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBidXNpbmVzczogKGRhdGEgYXMgYW55KS5idXNpbmVzc2VzLFxuICAgICAgYnVzaW5lc3NTb3VyY2U6IGRhdGFcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRGVkdXBsaWNhdGUgYnVzaW5lc3MgLSBmaW5kIGV4aXN0aW5nIG9yIGNyZWF0ZSBuZXdcbiAgICovXG4gIGFzeW5jIGRlZHVwbGljYXRlQnVzaW5lc3MoXG4gICAgYnVzaW5lc3NEYXRhOiB7XG4gICAgICBuYW1lOiBzdHJpbmdcbiAgICAgIGFkZHJlc3M6IHN0cmluZ1xuICAgICAgbGF0aXR1ZGU/OiBudW1iZXJcbiAgICAgIGxvbmdpdHVkZT86IG51bWJlclxuICAgIH0sXG4gICAgc291cmNlSWQ6IHN0cmluZyxcbiAgICBzb3VyY2VCdXNpbmVzc0lkOiBzdHJpbmdcbiAgKTogUHJvbWlzZTxCdXNpbmVzc0RlZHVwbGljYXRpb25SZXN1bHQ+IHtcbiAgICBjb25zb2xlLmxvZyhg8J+UjSBEZWR1cGxpY2F0aW5nIGJ1c2luZXNzOiAke2J1c2luZXNzRGF0YS5uYW1lfWApXG5cbiAgICB0cnkge1xuICAgICAgLy8gRmlyc3QgY2hlY2sgaWYgdGhpcyBleGFjdCBzb3VyY2UgYnVzaW5lc3MgSUQgYWxyZWFkeSBleGlzdHNcbiAgICAgIGNvbnN0IGV4aXN0aW5nU291cmNlQnVzaW5lc3MgPSBhd2FpdCB0aGlzLmdldEJ1c2luZXNzQnlTb3VyY2VJZChzb3VyY2VJZCwgc291cmNlQnVzaW5lc3NJZClcblxuICAgICAgaWYgKGV4aXN0aW5nU291cmNlQnVzaW5lc3MpIHtcbiAgICAgICAgY29uc29sZS5sb2coYOKchSBGb3VuZCBleGlzdGluZyBidXNpbmVzcyBieSBzb3VyY2UgSUQ6ICR7ZXhpc3RpbmdTb3VyY2VCdXNpbmVzcy5idXNpbmVzcy5pZH1gKVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlzTmV3QnVzaW5lc3M6IGZhbHNlLFxuICAgICAgICAgIGJ1c2luZXNzSWQ6IGV4aXN0aW5nU291cmNlQnVzaW5lc3MuYnVzaW5lc3MuaWQsXG4gICAgICAgICAgZXhpc3RpbmdCdXNpbmVzczogZXhpc3RpbmdTb3VyY2VCdXNpbmVzcy5idXNpbmVzc1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIExvb2sgZm9yIHBvdGVudGlhbCBkdXBsaWNhdGVzIGJhc2VkIG9uIG5hbWUsIGFkZHJlc3MsIGFuZCBsb2NhdGlvblxuICAgICAgY29uc29sZS5sb2coYPCflI0gTG9va2luZyBmb3IgcG90ZW50aWFsIGR1cGxpY2F0ZXMuLi5gKVxuICAgICAgY29uc3QgcG90ZW50aWFsRHVwbGljYXRlcyA9IGF3YWl0IHRoaXMuZmluZFBvdGVudGlhbER1cGxpY2F0ZXMoXG4gICAgICAgIGJ1c2luZXNzRGF0YS5uYW1lLFxuICAgICAgICBidXNpbmVzc0RhdGEuYWRkcmVzcyxcbiAgICAgICAgYnVzaW5lc3NEYXRhLmxhdGl0dWRlLFxuICAgICAgICBidXNpbmVzc0RhdGEubG9uZ2l0dWRlXG4gICAgICApXG5cbiAgICAgIGNvbnNvbGUubG9nKGDwn5SNIEZvdW5kICR7cG90ZW50aWFsRHVwbGljYXRlcy5sZW5ndGh9IHBvdGVudGlhbCBkdXBsaWNhdGVzYClcblxuICAgICAgLy8gSWYgd2UgZmluZCBhIGhpZ2gtY29uZmlkZW5jZSBtYXRjaCAoc2ltaWxhcml0eSA+IDAuOCksIHVzZSBpdFxuICAgICAgY29uc3QgaGlnaENvbmZpZGVuY2VNYXRjaCA9IHBvdGVudGlhbER1cGxpY2F0ZXMuZmluZChkdXAgPT4gZHVwLnNpbWlsYXJpdHlfc2NvcmUgPiAwLjgpXG5cbiAgICAgIGlmIChoaWdoQ29uZmlkZW5jZU1hdGNoKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDinIUgRm91bmQgaGlnaC1jb25maWRlbmNlIG1hdGNoOiAke2hpZ2hDb25maWRlbmNlTWF0Y2gubmFtZX0gKHNpbWlsYXJpdHk6ICR7aGlnaENvbmZpZGVuY2VNYXRjaC5zaW1pbGFyaXR5X3Njb3JlfSlgKVxuXG4gICAgICAgIGNvbnN0IHsgZGF0YTogZXhpc3RpbmdCdXNpbmVzcywgZXJyb3IgfSA9IGF3YWl0IHRoaXMuY2xpZW50XG4gICAgICAgICAgLmZyb20oJ2J1c2luZXNzZXMnKVxuICAgICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAgIC5lcSgnaWQnLCBoaWdoQ29uZmlkZW5jZU1hdGNoLmJ1c2luZXNzX2lkKVxuICAgICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCBleGlzdGluZyBidXNpbmVzczogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlzTmV3QnVzaW5lc3M6IGZhbHNlLFxuICAgICAgICAgIGJ1c2luZXNzSWQ6IGhpZ2hDb25maWRlbmNlTWF0Y2guYnVzaW5lc3NfaWQsXG4gICAgICAgICAgZXhpc3RpbmdCdXNpbmVzczogZXhpc3RpbmdCdXNpbmVzcyxcbiAgICAgICAgICBzaW1pbGFyQnVzaW5lc3NlczogcG90ZW50aWFsRHVwbGljYXRlc1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIE5vIGhpZ2gtY29uZmlkZW5jZSBtYXRjaCBmb3VuZCwgdGhpcyBpcyBhIG5ldyBidXNpbmVzc1xuICAgICAgY29uc29sZS5sb2coYOKcqCBDcmVhdGluZyBuZXcgYnVzaW5lc3M6ICR7YnVzaW5lc3NEYXRhLm5hbWV9YClcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGlzTmV3QnVzaW5lc3M6IHRydWUsXG4gICAgICAgIGJ1c2luZXNzSWQ6IHV1aWR2NCgpLCAvLyBHZW5lcmF0ZSBuZXcgSUQgZm9yIHRoZSBidXNpbmVzc1xuICAgICAgICBzaW1pbGFyQnVzaW5lc3NlczogcG90ZW50aWFsRHVwbGljYXRlcy5sZW5ndGggPiAwID8gcG90ZW50aWFsRHVwbGljYXRlcyA6IHVuZGVmaW5lZFxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGDinYwgRXJyb3IgaW4gZGVkdXBsaWNhdGVCdXNpbmVzczpgLCBlcnJvcilcbiAgICAgIHRocm93IGVycm9yXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBvciB1cGRhdGUgYSBidXNpbmVzc1xuICAgKi9cbiAgYXN5bmMgdXBzZXJ0QnVzaW5lc3MoXG4gICAgYnVzaW5lc3NEYXRhOiB7XG4gICAgICBpZD86IHN0cmluZ1xuICAgICAgbmFtZTogc3RyaW5nXG4gICAgICBhZGRyZXNzOiBzdHJpbmdcbiAgICAgIHBob25lPzogc3RyaW5nXG4gICAgICB3ZWJzaXRlPzogc3RyaW5nXG4gICAgICBjYXRlZ29yeT86IHN0cmluZ1xuICAgICAgcmF0aW5nPzogbnVtYmVyXG4gICAgICB0b3RhbFJldmlld3M/OiBudW1iZXJcbiAgICAgIGxhdGl0dWRlPzogbnVtYmVyXG4gICAgICBsb25naXR1ZGU/OiBudW1iZXJcbiAgICAgIG1ldGFkYXRhPzogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIH0sXG4gICAgc2NyYXBlclJ1bklkOiBzdHJpbmdcbiAgKTogUHJvbWlzZTx7IGJ1c2luZXNzOiBUYWJsZXM8J2J1c2luZXNzZXMnPjsgdmVyc2lvbkluY3JlbWVudGVkOiBib29sZWFuIH0+IHtcbiAgICBjb25zdCBidXNpbmVzc0lkID0gYnVzaW5lc3NEYXRhLmlkIHx8IHV1aWR2NCgpXG5cbiAgICAvLyBDaGVjayBpZiBidXNpbmVzcyBhbHJlYWR5IGV4aXN0c1xuICAgIGNvbnN0IHsgZGF0YTogZXhpc3RpbmdCdXNpbmVzcyB9ID0gYXdhaXQgdGhpcy5jbGllbnRcbiAgICAgIC5mcm9tKCdidXNpbmVzc2VzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCdpZCcsIGJ1c2luZXNzSWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGNvbnN0IGlzVXBkYXRlID0gISFleGlzdGluZ0J1c2luZXNzXG4gICAgY29uc3QgY3VycmVudFZlcnNpb24gPSBleGlzdGluZ0J1c2luZXNzPy52ZXJzaW9uIHx8IDBcbiAgICBjb25zdCBuZXdWZXJzaW9uID0gY3VycmVudFZlcnNpb24gKyAxXG5cbiAgICBjb25zdCBidXNpbmVzc0luc2VydDogVGFibGVzSW5zZXJ0PCdidXNpbmVzc2VzJz4gPSB7XG4gICAgICBpZDogYnVzaW5lc3NJZCxcbiAgICAgIG5hbWU6IGJ1c2luZXNzRGF0YS5uYW1lLFxuICAgICAgYWRkcmVzczogYnVzaW5lc3NEYXRhLmFkZHJlc3MsXG4gICAgICBwaG9uZTogYnVzaW5lc3NEYXRhLnBob25lLFxuICAgICAgd2Vic2l0ZTogYnVzaW5lc3NEYXRhLndlYnNpdGUsXG4gICAgICBjYXRlZ29yeTogYnVzaW5lc3NEYXRhLmNhdGVnb3J5LFxuICAgICAgcmF0aW5nOiBidXNpbmVzc0RhdGEucmF0aW5nIHx8IDAsXG4gICAgICB0b3RhbF9yZXZpZXdzOiBidXNpbmVzc0RhdGEudG90YWxSZXZpZXdzIHx8IDAsXG4gICAgICBsYXRpdHVkZTogYnVzaW5lc3NEYXRhLmxhdGl0dWRlLFxuICAgICAgbG9uZ2l0dWRlOiBidXNpbmVzc0RhdGEubG9uZ2l0dWRlLFxuICAgICAgdmVyc2lvbjogbmV3VmVyc2lvbixcbiAgICAgIG1ldGFkYXRhOiBidXNpbmVzc0RhdGEubWV0YWRhdGEgfHwge31cbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGE6IGJ1c2luZXNzLCBlcnJvciB9ID0gYXdhaXQgdGhpcy5jbGllbnRcbiAgICAgIC5mcm9tKCdidXNpbmVzc2VzJylcbiAgICAgIC51cHNlcnQoYnVzaW5lc3NJbnNlcnQsIHtcbiAgICAgICAgb25Db25mbGljdDogJ2lkJyxcbiAgICAgICAgaWdub3JlRHVwbGljYXRlczogZmFsc2VcbiAgICAgIH0pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1cHNlcnQgYnVzaW5lc3M6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIC8vIFRyYWNrIHRoZSB2ZXJzaW9uIGNoYW5nZSBpbiBidXNpbmVzc19zY3JhcGVyX3J1bnNcbiAgICBpZiAoaXNVcGRhdGUpIHtcbiAgICAgIGF3YWl0IHRoaXMuY2xpZW50XG4gICAgICAgIC5mcm9tKCdidXNpbmVzc19zY3JhcGVyX3J1bnMnKVxuICAgICAgICAuaW5zZXJ0KHtcbiAgICAgICAgICBidXNpbmVzc19pZDogYnVzaW5lc3NJZCxcbiAgICAgICAgICBzY3JhcGVyX3J1bl9pZDogc2NyYXBlclJ1bklkLFxuICAgICAgICAgIHZlcnNpb25fYmVmb3JlOiBjdXJyZW50VmVyc2lvbixcbiAgICAgICAgICB2ZXJzaW9uX2FmdGVyOiBuZXdWZXJzaW9uLFxuICAgICAgICAgIGNoYW5nZXNfbWFkZToge1xuICAgICAgICAgICAgdXBkYXRlZF9maWVsZHM6IE9iamVjdC5rZXlzKGJ1c2luZXNzRGF0YSksXG4gICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgYnVzaW5lc3MsXG4gICAgICB2ZXJzaW9uSW5jcmVtZW50ZWQ6IGlzVXBkYXRlXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBvciB1cGRhdGUgYnVzaW5lc3Mgc291cmNlIG1hcHBpbmdcbiAgICovXG4gIGFzeW5jIHVwc2VydEJ1c2luZXNzU291cmNlKFxuICAgIGJ1c2luZXNzSWQ6IHN0cmluZyxcbiAgICBzb3VyY2VJZDogc3RyaW5nLFxuICAgIHNvdXJjZUJ1c2luZXNzSWQ6IHN0cmluZyxcbiAgICBzb3VyY2VVcmw/OiBzdHJpbmcsXG4gICAgc291cmNlTWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICk6IFByb21pc2U8VGFibGVzPCdidXNpbmVzc19zb3VyY2VzJz4+IHtcbiAgICBjb25zdCBidXNpbmVzc1NvdXJjZURhdGE6IFRhYmxlc0luc2VydDwnYnVzaW5lc3Nfc291cmNlcyc+ID0ge1xuICAgICAgYnVzaW5lc3NfaWQ6IGJ1c2luZXNzSWQsXG4gICAgICBzb3VyY2VfaWQ6IHNvdXJjZUlkLFxuICAgICAgc291cmNlX2J1c2luZXNzX2lkOiBzb3VyY2VCdXNpbmVzc0lkLFxuICAgICAgc291cmNlX3VybDogc291cmNlVXJsLFxuICAgICAgc291cmNlX21ldGFkYXRhOiBzb3VyY2VNZXRhZGF0YSB8fCB7fSxcbiAgICAgIGxhc3Rfc2NyYXBlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgdGhpcy5jbGllbnRcbiAgICAgIC5mcm9tKCdidXNpbmVzc19zb3VyY2VzJylcbiAgICAgIC51cHNlcnQoYnVzaW5lc3NTb3VyY2VEYXRhLCB7XG4gICAgICAgIG9uQ29uZmxpY3Q6ICdzb3VyY2VfaWQsc291cmNlX2J1c2luZXNzX2lkJyxcbiAgICAgICAgaWdub3JlRHVwbGljYXRlczogZmFsc2VcbiAgICAgIH0pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB1cHNlcnQgYnVzaW5lc3Mgc291cmNlOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIFNhdmUgcmV2aWV3cyBmb3IgYSBidXNpbmVzc1xuICAgKi9cbiAgYXN5bmMgc2F2ZVJldmlld3MoXG4gICAgYnVzaW5lc3NJZDogc3RyaW5nLFxuICAgIHNvdXJjZUlkOiBzdHJpbmcsXG4gICAgcmV2aWV3czogQXJyYXk8e1xuICAgICAgc291cmNlUmV2aWV3SWQ6IHN0cmluZ1xuICAgICAgYXV0aG9yTmFtZTogc3RyaW5nXG4gICAgICBhdXRob3JJZD86IHN0cmluZ1xuICAgICAgYXV0aG9yQXZhdGFyPzogc3RyaW5nXG4gICAgICByYXRpbmc6IG51bWJlclxuICAgICAgcmV2aWV3VGV4dD86IHN0cmluZ1xuICAgICAgbGFuZ3VhZ2U/OiBzdHJpbmdcbiAgICAgIGxpa2VzPzogbnVtYmVyXG4gICAgICBwdWJsaXNoZWRBdDogc3RyaW5nXG4gICAgICB1cGRhdGVkUmV2aWV3QXQ/OiBzdHJpbmdcbiAgICAgIHJlcGx5VGV4dD86IHN0cmluZ1xuICAgICAgcmVwbHlQdWJsaXNoZWRBdD86IHN0cmluZ1xuICAgICAgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgfT5cbiAgKTogUHJvbWlzZTxudW1iZXI+IHtcbiAgICBpZiAocmV2aWV3cy5sZW5ndGggPT09IDApIHJldHVybiAwXG5cbiAgICBjb25zdCByZXZpZXdzRGF0YTogVGFibGVzSW5zZXJ0PCdyZXZpZXdzJz5bXSA9IHJldmlld3MubWFwKHJldmlldyA9PiAoe1xuICAgICAgYnVzaW5lc3NfaWQ6IGJ1c2luZXNzSWQsXG4gICAgICBzb3VyY2VfaWQ6IHNvdXJjZUlkLFxuICAgICAgc291cmNlX3Jldmlld19pZDogcmV2aWV3LnNvdXJjZVJldmlld0lkLFxuICAgICAgYXV0aG9yX25hbWU6IHJldmlldy5hdXRob3JOYW1lLFxuICAgICAgYXV0aG9yX2lkOiByZXZpZXcuYXV0aG9ySWQsXG4gICAgICBhdXRob3JfYXZhdGFyOiByZXZpZXcuYXV0aG9yQXZhdGFyLFxuICAgICAgcmF0aW5nOiByZXZpZXcucmF0aW5nLFxuICAgICAgcmV2aWV3X3RleHQ6IHJldmlldy5yZXZpZXdUZXh0LFxuICAgICAgbGFuZ3VhZ2U6IHJldmlldy5sYW5ndWFnZSxcbiAgICAgIGxpa2VzOiByZXZpZXcubGlrZXMgfHwgMCxcbiAgICAgIHB1Ymxpc2hlZF9hdDogcmV2aWV3LnB1Ymxpc2hlZEF0LFxuICAgICAgdXBkYXRlZF9yZXZpZXdfYXQ6IHJldmlldy51cGRhdGVkUmV2aWV3QXQsXG4gICAgICByZXBseV90ZXh0OiByZXZpZXcucmVwbHlUZXh0LFxuICAgICAgcmVwbHlfcHVibGlzaGVkX2F0OiByZXZpZXcucmVwbHlQdWJsaXNoZWRBdCxcbiAgICAgIG1ldGFkYXRhOiByZXZpZXcubWV0YWRhdGEgfHwge31cbiAgICB9KSlcblxuICAgIC8vIEluc2VydCByZXZpZXdzIGluIGJhdGNoZXMgdG8gYXZvaWQgcGF5bG9hZCBsaW1pdHNcbiAgICBjb25zdCBiYXRjaFNpemUgPSA1MFxuICAgIGxldCBzYXZlZENvdW50ID0gMFxuXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCByZXZpZXdzRGF0YS5sZW5ndGg7IGkgKz0gYmF0Y2hTaXplKSB7XG4gICAgICBjb25zdCBiYXRjaCA9IHJldmlld3NEYXRhLnNsaWNlKGksIGkgKyBiYXRjaFNpemUpXG5cbiAgICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHRoaXMuY2xpZW50XG4gICAgICAgIC5mcm9tKCdyZXZpZXdzJylcbiAgICAgICAgLnVwc2VydChiYXRjaCwge1xuICAgICAgICAgIG9uQ29uZmxpY3Q6ICdzb3VyY2VfaWQsc291cmNlX3Jldmlld19pZCxidXNpbmVzc19pZCcsXG4gICAgICAgICAgaWdub3JlRHVwbGljYXRlczogdHJ1ZVxuICAgICAgICB9KVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIHNhdmUgcmV2aWV3cyBiYXRjaCAke2l9LSR7aSArIGJhdGNoLmxlbmd0aH06YCwgZXJyb3IpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHNhdmUgcmV2aWV3czogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIHNhdmVkQ291bnQgKz0gYmF0Y2gubGVuZ3RoXG4gICAgfVxuXG4gICAgcmV0dXJuIHNhdmVkQ291bnRcbiAgfVxuXG4gIC8qKlxuICAgKiBTYXZlIHVuaWZpZWQgYnVzaW5lc3MgKyByZXZpZXdzIGRhdGEgZnJvbSBzY3JhcGluZyAobWFpbiBtZXRob2QpXG4gICAqL1xuICBhc3luYyBzYXZlVW5pZmllZERhdGEoXG4gICAgc291cmNlTmFtZTogc3RyaW5nLFxuICAgIHVuaWZpZWRSZXN1bHRzOiBVbmlmaWVkU2NyYXBlclJlc3VsdFtdLFxuICAgIG9wdGlvbnM6IFNhdmVVbmlmaWVkT3B0aW9ucyA9IHt9XG4gICk6IFByb21pc2U8e1xuICAgIHNjcmFwZXJSdW5JZDogc3RyaW5nXG4gICAgc2F2ZWRCdXNpbmVzc2VzOiBudW1iZXJcbiAgICBzYXZlZFJldmlld3M6IG51bWJlclxuICAgIG5ld0J1c2luZXNzZXM6IG51bWJlclxuICAgIHVwZGF0ZWRCdXNpbmVzc2VzOiBudW1iZXJcbiAgfT4ge1xuICAgIC8vIEdldCBzb3VyY2VcbiAgICBjb25zdCBzb3VyY2UgPSBhd2FpdCB0aGlzLmdldFNvdXJjZUJ5TmFtZShzb3VyY2VOYW1lKVxuICAgIGlmICghc291cmNlKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFNvdXJjZSAnJHtzb3VyY2VOYW1lfScgbm90IGZvdW5kYClcbiAgICB9XG5cbiAgICBsZXQgc2F2ZWRCdXNpbmVzc2VzID0gMFxuICAgIGxldCBzYXZlZFJldmlld3MgPSAwXG4gICAgbGV0IG5ld0J1c2luZXNzZXMgPSAwXG4gICAgbGV0IHVwZGF0ZWRCdXNpbmVzc2VzID0gMFxuXG4gICAgY29uc3Qgc2NyYXBlclJ1bklkID0gb3B0aW9ucy5zY3JhcGVyUnVuSWQgfHwgdXVpZHY0KClcblxuICAgIGNvbnNvbGUubG9nKGDwn5K+IFN0YXJ0aW5nIHVuaWZpZWQgc2F2ZSBmb3IgJHt1bmlmaWVkUmVzdWx0cy5sZW5ndGh9IGJ1c2luZXNzZXMgZnJvbSAke3NvdXJjZU5hbWV9YClcblxuICAgIGZvciAoY29uc3QgcmVzdWx0IG9mIHVuaWZpZWRSZXN1bHRzKSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBEZWR1cGxpY2F0ZSBidXNpbmVzc1xuICAgICAgICBjb25zdCBkZWR1cGxpY2F0aW9uUmVzdWx0ID0gYXdhaXQgdGhpcy5kZWR1cGxpY2F0ZUJ1c2luZXNzKFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIG5hbWU6IHJlc3VsdC5idXNpbmVzcy5uYW1lLFxuICAgICAgICAgICAgYWRkcmVzczogcmVzdWx0LmJ1c2luZXNzLmFkZHJlc3MsXG4gICAgICAgICAgICBsYXRpdHVkZTogcmVzdWx0LmJ1c2luZXNzLmNvb3JkaW5hdGVzLmxhdCxcbiAgICAgICAgICAgIGxvbmdpdHVkZTogcmVzdWx0LmJ1c2luZXNzLmNvb3JkaW5hdGVzLmxuZ1xuICAgICAgICAgIH0sXG4gICAgICAgICAgc291cmNlLmlkLFxuICAgICAgICAgIHJlc3VsdC5idXNpbmVzcy5nb29nbGVQbGFjZUlkXG4gICAgICAgIClcblxuICAgICAgICAvLyBVcHNlcnQgYnVzaW5lc3NcbiAgICAgICAgY29uc3QgeyBidXNpbmVzcywgdmVyc2lvbkluY3JlbWVudGVkIH0gPSBhd2FpdCB0aGlzLnVwc2VydEJ1c2luZXNzKFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIGlkOiBkZWR1cGxpY2F0aW9uUmVzdWx0LmJ1c2luZXNzSWQsXG4gICAgICAgICAgICBuYW1lOiByZXN1bHQuYnVzaW5lc3MubmFtZSxcbiAgICAgICAgICAgIGFkZHJlc3M6IHJlc3VsdC5idXNpbmVzcy5hZGRyZXNzLFxuICAgICAgICAgICAgcGhvbmU6IHJlc3VsdC5idXNpbmVzcy5waG9uZSxcbiAgICAgICAgICAgIHdlYnNpdGU6IHJlc3VsdC5idXNpbmVzcy53ZWJzaXRlLFxuICAgICAgICAgICAgY2F0ZWdvcnk6IHJlc3VsdC5idXNpbmVzcy5jYXRlZ29yeSxcbiAgICAgICAgICAgIHJhdGluZzogcmVzdWx0LmJ1c2luZXNzLnJhdGluZyxcbiAgICAgICAgICAgIHRvdGFsUmV2aWV3czogcmVzdWx0LmJ1c2luZXNzLnRvdGFsUmV2aWV3cyxcbiAgICAgICAgICAgIGxhdGl0dWRlOiByZXN1bHQuYnVzaW5lc3MuY29vcmRpbmF0ZXMubGF0LFxuICAgICAgICAgICAgbG9uZ2l0dWRlOiByZXN1bHQuYnVzaW5lc3MuY29vcmRpbmF0ZXMubG5nLFxuICAgICAgICAgICAgbWV0YWRhdGE6IHtcbiAgICAgICAgICAgICAgdmVyaWZpZWQ6IHJlc3VsdC5idXNpbmVzcy52ZXJpZmllZCxcbiAgICAgICAgICAgICAgcGx1c0NvZGU6IHJlc3VsdC5idXNpbmVzcy5wbHVzQ29kZSxcbiAgICAgICAgICAgICAgb3BlbmluZ0hvdXJzOiByZXN1bHQuYnVzaW5lc3Mub3BlbmluZ0hvdXJzLFxuICAgICAgICAgICAgICBwaG90b3M6IHJlc3VsdC5idXNpbmVzcy5waG90b3MsXG4gICAgICAgICAgICAgIHByaWNlTGV2ZWw6IHJlc3VsdC5idXNpbmVzcy5wcmljZUxldmVsLFxuICAgICAgICAgICAgICBleHRyYWN0ZWRBdDogcmVzdWx0LmV4dHJhY3RlZEF0LFxuICAgICAgICAgICAgICAuLi5vcHRpb25zLm1ldGFkYXRhXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICBzY3JhcGVyUnVuSWRcbiAgICAgICAgKVxuXG4gICAgICAgIC8vIENyZWF0ZS91cGRhdGUgYnVzaW5lc3Mgc291cmNlIG1hcHBpbmdcbiAgICAgICAgYXdhaXQgdGhpcy51cHNlcnRCdXNpbmVzc1NvdXJjZShcbiAgICAgICAgICBidXNpbmVzcy5pZCxcbiAgICAgICAgICBzb3VyY2UuaWQsXG4gICAgICAgICAgcmVzdWx0LmJ1c2luZXNzLmdvb2dsZVBsYWNlSWQsXG4gICAgICAgICAgdW5kZWZpbmVkLCAvLyBXZSBkb24ndCBoYXZlIHRoZSBvcmlnaW5hbCBVUkwgaW4gdGhlIGN1cnJlbnQgZGF0YVxuICAgICAgICAgIHtcbiAgICAgICAgICAgIHZlcmlmaWVkOiByZXN1bHQuYnVzaW5lc3MudmVyaWZpZWQsXG4gICAgICAgICAgICBwbHVzQ29kZTogcmVzdWx0LmJ1c2luZXNzLnBsdXNDb2RlLFxuICAgICAgICAgICAgb3BlbmluZ0hvdXJzOiByZXN1bHQuYnVzaW5lc3Mub3BlbmluZ0hvdXJzLFxuICAgICAgICAgICAgcGhvdG9zOiByZXN1bHQuYnVzaW5lc3MucGhvdG9zLFxuICAgICAgICAgICAgcHJpY2VMZXZlbDogcmVzdWx0LmJ1c2luZXNzLnByaWNlTGV2ZWxcbiAgICAgICAgICB9XG4gICAgICAgIClcblxuICAgICAgICBzYXZlZEJ1c2luZXNzZXMrK1xuICAgICAgICBpZiAoZGVkdXBsaWNhdGlvblJlc3VsdC5pc05ld0J1c2luZXNzKSB7XG4gICAgICAgICAgbmV3QnVzaW5lc3NlcysrXG4gICAgICAgIH0gZWxzZSBpZiAodmVyc2lvbkluY3JlbWVudGVkKSB7XG4gICAgICAgICAgdXBkYXRlZEJ1c2luZXNzZXMrK1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5sb2coYPCfkr4gU2F2ZWQgYnVzaW5lc3M6ICR7cmVzdWx0LmJ1c2luZXNzLm5hbWV9ICgke2RlZHVwbGljYXRpb25SZXN1bHQuaXNOZXdCdXNpbmVzcyA/ICduZXcnIDogJ2V4aXN0aW5nJ30pYClcblxuICAgICAgICAvLyBTYXZlIHJldmlld3MgaWYgYW55XG4gICAgICAgIGlmIChyZXN1bHQucmV2aWV3cyAmJiByZXN1bHQucmV2aWV3cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgY29uc3QgcmV2aWV3c1NhdmVkID0gYXdhaXQgdGhpcy5zYXZlUmV2aWV3cyhcbiAgICAgICAgICAgIGJ1c2luZXNzLmlkLFxuICAgICAgICAgICAgc291cmNlLmlkLFxuICAgICAgICAgICAgcmVzdWx0LnJldmlld3MubWFwKHJldmlldyA9PiAoe1xuICAgICAgICAgICAgICBzb3VyY2VSZXZpZXdJZDogcmV2aWV3Lmdvb2dsZVJldmlld0lkLFxuICAgICAgICAgICAgICBhdXRob3JOYW1lOiByZXZpZXcuYXV0aG9yTmFtZSxcbiAgICAgICAgICAgICAgYXV0aG9ySWQ6IHJldmlldy5hdXRob3JJZCxcbiAgICAgICAgICAgICAgYXV0aG9yQXZhdGFyOiByZXZpZXcuYXV0aG9yQXZhdGFyLFxuICAgICAgICAgICAgICByYXRpbmc6IHJldmlldy5yYXRpbmcsXG4gICAgICAgICAgICAgIHJldmlld1RleHQ6IHJldmlldy5yZXZpZXdUZXh0LFxuICAgICAgICAgICAgICBsYW5ndWFnZTogcmV2aWV3Lmxhbmd1YWdlLFxuICAgICAgICAgICAgICBsaWtlczogcmV2aWV3Lmxpa2VzLFxuICAgICAgICAgICAgICBwdWJsaXNoZWRBdDogcmV2aWV3LnB1Ymxpc2hlZEF0LFxuICAgICAgICAgICAgICB1cGRhdGVkUmV2aWV3QXQ6IHJldmlldy51cGRhdGVkQXQsXG4gICAgICAgICAgICAgIHJlcGx5VGV4dDogcmV2aWV3LnJlcGx5VGV4dCxcbiAgICAgICAgICAgICAgcmVwbHlQdWJsaXNoZWRBdDogcmV2aWV3LnJlcGx5UHVibGlzaGVkQXQsXG4gICAgICAgICAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgICAgICAgICAgaW1hZ2VzOiByZXZpZXcuaW1hZ2VzIHx8IFtdLFxuICAgICAgICAgICAgICAgIC4uLm9wdGlvbnMubWV0YWRhdGFcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSkpXG4gICAgICAgICAgKVxuXG4gICAgICAgICAgc2F2ZWRSZXZpZXdzICs9IHJldmlld3NTYXZlZFxuICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5K+IFNhdmVkICR7cmV2aWV3c1NhdmVkfSByZXZpZXdzIGZvciAke3Jlc3VsdC5idXNpbmVzcy5uYW1lfWApXG4gICAgICAgIH1cblxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgcHJvY2Vzc2luZyBidXNpbmVzcyAke3Jlc3VsdC5idXNpbmVzcy5uYW1lfTpgLCBlcnJvcilcbiAgICAgICAgLy8gQ29udGludWUgd2l0aCBvdGhlciBidXNpbmVzc2VzIGluc3RlYWQgb2YgZmFpbGluZyBjb21wbGV0ZWx5XG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coYPCfkr4gVW5pZmllZCBzYXZlIGNvbXBsZXRlOiAke3NhdmVkQnVzaW5lc3Nlc30gYnVzaW5lc3NlcyAoJHtuZXdCdXNpbmVzc2VzfSBuZXcsICR7dXBkYXRlZEJ1c2luZXNzZXN9IHVwZGF0ZWQpLCAke3NhdmVkUmV2aWV3c30gcmV2aWV3c2ApXG5cbiAgICByZXR1cm4ge1xuICAgICAgc2NyYXBlclJ1bklkLFxuICAgICAgc2F2ZWRCdXNpbmVzc2VzLFxuICAgICAgc2F2ZWRSZXZpZXdzLFxuICAgICAgbmV3QnVzaW5lc3NlcyxcbiAgICAgIHVwZGF0ZWRCdXNpbmVzc2VzXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBidXNpbmVzc2VzIHdpdGggdGhlaXIgc291cmNlcyBhbmQgcmV2aWV3c1xuICAgKi9cbiAgYXN5bmMgZ2V0QnVzaW5lc3Nlc1dpdGhTb3VyY2VzQW5kUmV2aWV3cyhsaW1pdDogbnVtYmVyID0gMTApOiBQcm9taXNlPGFueVtdPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgdGhpcy5jbGllbnRcbiAgICAgIC5mcm9tKCdidXNpbmVzc2VzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBidXNpbmVzc19zb3VyY2VzIChcbiAgICAgICAgICAqLFxuICAgICAgICAgIHNvdXJjZXMgKCopXG4gICAgICAgICksXG4gICAgICAgIHJldmlld3MgKCopXG4gICAgICBgKVxuICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgICAubGltaXQobGltaXQpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCBidXNpbmVzc2VzIHdpdGggc291cmNlcyBhbmQgcmV2aWV3czogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgfHwgW11cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgc2NyYXBlciBydW4gZGV0YWlsc1xuICAgKi9cbiAgYXN5bmMgZ2V0U2NyYXBlclJ1bihzY3JhcGVyUnVuSWQ6IHN0cmluZyk6IFByb21pc2U8VGFibGVzPCdzY3JhcGVyX3J1bnMnPiB8IG51bGw+IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCB0aGlzLmNsaWVudFxuICAgICAgLmZyb20oJ3NjcmFwZXJfcnVucycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5lcSgnaWQnLCBzY3JhcGVyUnVuSWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgaWYgKGVycm9yLmNvZGUgPT09ICdQR1JTVDExNicpIHsgLy8gTm8gcm93cyByZXR1cm5lZFxuICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgfVxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZ2V0IHNjcmFwZXIgcnVuOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBkYXRhYmFzZSBzdGF0aXN0aWNzXG4gICAqL1xuICBhc3luYyBnZXRTdGF0cygpOiBQcm9taXNlPHtcbiAgICB0b3RhbFNvdXJjZXM6IG51bWJlclxuICAgIHRvdGFsQnVzaW5lc3NlczogbnVtYmVyXG4gICAgdG90YWxSZXZpZXdzOiBudW1iZXJcbiAgICB0b3RhbFNjcmFwZXJSdW5zOiBudW1iZXJcbiAgfT4ge1xuICAgIGNvbnN0IFtcbiAgICAgIHNvdXJjZXNSZXN1bHQsXG4gICAgICBidXNpbmVzc2VzUmVzdWx0LFxuICAgICAgcmV2aWV3c1Jlc3VsdCxcbiAgICAgIHNjcmFwZXJSdW5zUmVzdWx0XG4gICAgXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgIHRoaXMuY2xpZW50LmZyb20oJ3NvdXJjZXMnKS5zZWxlY3QoJ2NvdW50JyksXG4gICAgICB0aGlzLmNsaWVudC5mcm9tKCdidXNpbmVzc2VzJykuc2VsZWN0KCdjb3VudCcpLFxuICAgICAgdGhpcy5jbGllbnQuZnJvbSgncmV2aWV3cycpLnNlbGVjdCgnY291bnQnKSxcbiAgICAgIHRoaXMuY2xpZW50LmZyb20oJ3NjcmFwZXJfcnVucycpLnNlbGVjdCgnY291bnQnKVxuICAgIF0pXG5cbiAgICByZXR1cm4ge1xuICAgICAgdG90YWxTb3VyY2VzOiAoc291cmNlc1Jlc3VsdC5kYXRhIGFzIGFueSk/LlswXT8uY291bnQgfHwgMCxcbiAgICAgIHRvdGFsQnVzaW5lc3NlczogKGJ1c2luZXNzZXNSZXN1bHQuZGF0YSBhcyBhbnkpPy5bMF0/LmNvdW50IHx8IDAsXG4gICAgICB0b3RhbFJldmlld3M6IChyZXZpZXdzUmVzdWx0LmRhdGEgYXMgYW55KT8uWzBdPy5jb3VudCB8fCAwLFxuICAgICAgdG90YWxTY3JhcGVyUnVuczogKHNjcmFwZXJSdW5zUmVzdWx0LmRhdGEgYXMgYW55KT8uWzBdPy5jb3VudCB8fCAwXG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiZ2V0U3VwYWJhc2VBZG1pbiIsImluaXRpYWxpemVTdXBhYmFzZUZyb21FbnYiLCJ2NCIsInV1aWR2NCIsIk11bHRpU291cmNlRGF0YWJhc2VTZXJ2aWNlIiwiY29uc3RydWN0b3IiLCJjbGllbnQiLCJlcnJvciIsImdldFNvdXJjZUJ5TmFtZSIsInNvdXJjZU5hbWUiLCJkYXRhIiwiZnJvbSIsInNlbGVjdCIsImVxIiwic2luZ2xlIiwiY29kZSIsIkVycm9yIiwibWVzc2FnZSIsImNyZWF0ZVNjcmFwZXJSdW4iLCJzY3JhcGVyUnVuSWQiLCJpbnNlcnQiLCJpZCIsInNvdXJjZV9pZCIsInNvdXJjZUlkIiwiYWN0b3JfbmFtZSIsImFjdG9yTmFtZSIsInN0YXR1cyIsInN0YXJ0ZWRfYXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJwYXJhbWV0ZXJzIiwibWV0YWRhdGEiLCJ1cGRhdGVTY3JhcGVyUnVuIiwidXBkYXRlcyIsInVwZGF0ZURhdGEiLCJ1cGRhdGVkX2F0IiwiY29tcGxldGVkQXQiLCJjb21wbGV0ZWRfYXQiLCJhcGlmeVJ1bklkIiwiYXBpZnlfcnVuX2lkIiwiYXBpZnlEYXRhc2V0SWQiLCJhcGlmeV9kYXRhc2V0X2lkIiwic3RvcmFnZVBhdGgiLCJzdG9yYWdlX3BhdGgiLCJkdXJhdGlvblNlY29uZHMiLCJ1bmRlZmluZWQiLCJkdXJhdGlvbl9zZWNvbmRzIiwiY29zdCIsInVwZGF0ZSIsImZpbmRQb3RlbnRpYWxEdXBsaWNhdGVzIiwiYnVzaW5lc3NOYW1lIiwiYnVzaW5lc3NBZGRyZXNzIiwibGF0aXR1ZGUiLCJsb25naXR1ZGUiLCJkaXN0YW5jZVRocmVzaG9sZEttIiwiaWxpa2UiLCJsaW1pdCIsImxlbmd0aCIsIm1hcCIsImJ1c2luZXNzIiwibmFtZVNpbWlsYXJpdHkiLCJ0b0xvd2VyQ2FzZSIsIm5hbWUiLCJpbmNsdWRlcyIsImFkZHJlc3NTaW1pbGFyaXR5IiwiYWRkcmVzcyIsInNpbWlsYXJpdHlfc2NvcmUiLCJkaXN0YW5jZV9rbSIsIlIiLCJkTGF0IiwiTWF0aCIsIlBJIiwiZExvbiIsImEiLCJzaW4iLCJjb3MiLCJjIiwiYXRhbjIiLCJzcXJ0IiwiYnVzaW5lc3NfaWQiLCJmaWx0ZXIiLCJyZXN1bHQiLCJzb3J0IiwiYiIsImdldEJ1c2luZXNzQnlTb3VyY2VJZCIsInNvdXJjZUJ1c2luZXNzSWQiLCJidXNpbmVzc2VzIiwiYnVzaW5lc3NTb3VyY2UiLCJkZWR1cGxpY2F0ZUJ1c2luZXNzIiwiYnVzaW5lc3NEYXRhIiwiY29uc29sZSIsImxvZyIsImV4aXN0aW5nU291cmNlQnVzaW5lc3MiLCJpc05ld0J1c2luZXNzIiwiYnVzaW5lc3NJZCIsImV4aXN0aW5nQnVzaW5lc3MiLCJwb3RlbnRpYWxEdXBsaWNhdGVzIiwiaGlnaENvbmZpZGVuY2VNYXRjaCIsImZpbmQiLCJkdXAiLCJzaW1pbGFyQnVzaW5lc3NlcyIsInVwc2VydEJ1c2luZXNzIiwiaXNVcGRhdGUiLCJjdXJyZW50VmVyc2lvbiIsInZlcnNpb24iLCJuZXdWZXJzaW9uIiwiYnVzaW5lc3NJbnNlcnQiLCJwaG9uZSIsIndlYnNpdGUiLCJjYXRlZ29yeSIsInJhdGluZyIsInRvdGFsX3Jldmlld3MiLCJ0b3RhbFJldmlld3MiLCJ1cHNlcnQiLCJvbkNvbmZsaWN0IiwiaWdub3JlRHVwbGljYXRlcyIsInNjcmFwZXJfcnVuX2lkIiwidmVyc2lvbl9iZWZvcmUiLCJ2ZXJzaW9uX2FmdGVyIiwiY2hhbmdlc19tYWRlIiwidXBkYXRlZF9maWVsZHMiLCJPYmplY3QiLCJrZXlzIiwidGltZXN0YW1wIiwidmVyc2lvbkluY3JlbWVudGVkIiwidXBzZXJ0QnVzaW5lc3NTb3VyY2UiLCJzb3VyY2VVcmwiLCJzb3VyY2VNZXRhZGF0YSIsImJ1c2luZXNzU291cmNlRGF0YSIsInNvdXJjZV9idXNpbmVzc19pZCIsInNvdXJjZV91cmwiLCJzb3VyY2VfbWV0YWRhdGEiLCJsYXN0X3NjcmFwZWRfYXQiLCJzYXZlUmV2aWV3cyIsInJldmlld3MiLCJyZXZpZXdzRGF0YSIsInJldmlldyIsInNvdXJjZV9yZXZpZXdfaWQiLCJzb3VyY2VSZXZpZXdJZCIsImF1dGhvcl9uYW1lIiwiYXV0aG9yTmFtZSIsImF1dGhvcl9pZCIsImF1dGhvcklkIiwiYXV0aG9yX2F2YXRhciIsImF1dGhvckF2YXRhciIsInJldmlld190ZXh0IiwicmV2aWV3VGV4dCIsImxhbmd1YWdlIiwibGlrZXMiLCJwdWJsaXNoZWRfYXQiLCJwdWJsaXNoZWRBdCIsInVwZGF0ZWRfcmV2aWV3X2F0IiwidXBkYXRlZFJldmlld0F0IiwicmVwbHlfdGV4dCIsInJlcGx5VGV4dCIsInJlcGx5X3B1Ymxpc2hlZF9hdCIsInJlcGx5UHVibGlzaGVkQXQiLCJiYXRjaFNpemUiLCJzYXZlZENvdW50IiwiaSIsImJhdGNoIiwic2xpY2UiLCJzYXZlVW5pZmllZERhdGEiLCJ1bmlmaWVkUmVzdWx0cyIsIm9wdGlvbnMiLCJzb3VyY2UiLCJzYXZlZEJ1c2luZXNzZXMiLCJzYXZlZFJldmlld3MiLCJuZXdCdXNpbmVzc2VzIiwidXBkYXRlZEJ1c2luZXNzZXMiLCJkZWR1cGxpY2F0aW9uUmVzdWx0IiwiY29vcmRpbmF0ZXMiLCJsYXQiLCJsbmciLCJnb29nbGVQbGFjZUlkIiwidmVyaWZpZWQiLCJwbHVzQ29kZSIsIm9wZW5pbmdIb3VycyIsInBob3RvcyIsInByaWNlTGV2ZWwiLCJleHRyYWN0ZWRBdCIsInJldmlld3NTYXZlZCIsImdvb2dsZVJldmlld0lkIiwidXBkYXRlZEF0IiwiaW1hZ2VzIiwiZ2V0QnVzaW5lc3Nlc1dpdGhTb3VyY2VzQW5kUmV2aWV3cyIsIm9yZGVyIiwiYXNjZW5kaW5nIiwiZ2V0U2NyYXBlclJ1biIsImdldFN0YXRzIiwic291cmNlc1Jlc3VsdCIsImJ1c2luZXNzZXNSZXN1bHQiLCJyZXZpZXdzUmVzdWx0Iiwic2NyYXBlclJ1bnNSZXN1bHQiLCJQcm9taXNlIiwiYWxsIiwidG90YWxTb3VyY2VzIiwiY291bnQiLCJ0b3RhbEJ1c2luZXNzZXMiLCJ0b3RhbFNjcmFwZXJSdW5zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/services/database/multi-source-database-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/database/supabase-client.ts":
/*!**************************************************!*\
  !*** ./src/services/database/supabase-client.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseClientFactory: () => (/* binding */ SupabaseClientFactory),\n/* harmony export */   getSupabaseAdmin: () => (/* binding */ getSupabaseAdmin),\n/* harmony export */   getSupabaseAnon: () => (/* binding */ getSupabaseAnon),\n/* harmony export */   getSupabaseFactory: () => (/* binding */ getSupabaseFactory),\n/* harmony export */   initializeSupabase: () => (/* binding */ initializeSupabase),\n/* harmony export */   initializeSupabaseFromEnv: () => (/* binding */ initializeSupabaseFromEnv)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n/**\n * Supabase client factory for different access levels\n */ class SupabaseClientFactory {\n    constructor(config){\n        this.config = config;\n        this.validateConfig();\n    }\n    /**\n   * Get client for user-facing operations (respects RLS)\n   */ getAnonClient() {\n        if (!this._anonClient) {\n            if (!this.config.anonKey) {\n                throw new Error(\"Anonymous key is required for anon client\");\n            }\n            this._anonClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(this.config.url, this.config.anonKey);\n        }\n        return this._anonClient;\n    }\n    /**\n   * Get client for admin operations (bypasses RLS)\n   */ getAdminClient() {\n        if (!this._adminClient) {\n            if (!this.config.serviceRoleKey) {\n                throw new Error(\"Service role key is required for admin client\");\n            }\n            this._adminClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(this.config.url, this.config.serviceRoleKey, {\n                auth: {\n                    autoRefreshToken: false,\n                    persistSession: false\n                }\n            });\n        }\n        return this._adminClient;\n    }\n    /**\n   * Test connection to Supabase\n   */ async testConnection() {\n        try {\n            const client = this.getAnonClient();\n            const { error } = await client.from(\"scrape_sessions\").select(\"count\").limit(1);\n            if (error) {\n                return {\n                    success: false,\n                    message: `Connection failed: ${error.message}`\n                };\n            }\n            return {\n                success: true,\n                message: \"Successfully connected to Supabase\"\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `Connection error: ${error instanceof Error ? error.message : \"Unknown error\"}`\n            };\n        }\n    }\n    validateConfig() {\n        if (!this.config.url) {\n            throw new Error(\"Supabase URL is required\");\n        }\n        if (!this.config.anonKey && !this.config.serviceRoleKey) {\n            throw new Error(\"At least one of anonKey or serviceRoleKey is required\");\n        }\n    }\n}\n/**\n * Default factory instance\n */ let defaultFactory = null;\n/**\n * Initialize the default Supabase factory\n */ function initializeSupabase(config) {\n    defaultFactory = new SupabaseClientFactory(config);\n    return defaultFactory;\n}\n/**\n * Get the default Supabase factory\n */ function getSupabaseFactory() {\n    if (!defaultFactory) {\n        throw new Error(\"Supabase factory not initialized. Call initializeSupabase() first.\");\n    }\n    return defaultFactory;\n}\n/**\n * Get admin client (bypasses RLS)\n */ function getSupabaseAdmin() {\n    return getSupabaseFactory().getAdminClient();\n}\n/**\n * Get anon client (respects RLS)\n */ function getSupabaseAnon() {\n    return getSupabaseFactory().getAnonClient();\n}\n/**\n * Initialize Supabase from environment variables\n */ function initializeSupabaseFromEnv() {\n    const url = process.env.SUPABASE_URL;\n    const anonKey = process.env.SUPABASE_ANON_KEY;\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!url) {\n        throw new Error(\"SUPABASE_URL environment variable is required\");\n    }\n    return initializeSupabase({\n        url,\n        anonKey,\n        serviceRoleKey\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/database/supabase-client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/apify-client","vendor-chunks/ow","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/uuid","vendor-chunks/whatwg-url","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/agentkeepalive","vendor-chunks/@apify","vendor-chunks/retry","vendor-chunks/get-proto","vendor-chunks/tr46","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/ansi-colors","vendor-chunks/tslib","vendor-chunks/axios","vendor-chunks/webidl-conversions","vendor-chunks/vali-date","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/lodash.isequal","vendor-chunks/is-obj","vendor-chunks/humanize-ms","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/dot-prop","vendor-chunks/delayed-stream","vendor-chunks/content-type","vendor-chunks/combined-stream","vendor-chunks/callsites","vendor-chunks/async-retry","vendor-chunks/@sindresorhus"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ffetch-business%2Froute&page=%2Fapi%2Ffetch-business%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffetch-business%2Froute.ts&appDir=%2FUsers%2F302720%2FRepositories%2Fapify%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2F302720%2FRepositories%2Fapify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();