"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/content-type";
exports.ids = ["vendor-chunks/content-type"];
exports.modules = {

/***/ "(rsc)/./node_modules/content-type/index.js":
/*!********************************************!*\
  !*** ./node_modules/content-type/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*!\n * content-type\n * Copyright(c) 2015 Douglas Christopher Wilson\n * MIT Licensed\n */ \n/**\n * RegExp to match *( \";\" parameter ) in RFC 7231 sec 3.1.1.1\n *\n * parameter     = token \"=\" ( token / quoted-string )\n * token         = 1*tchar\n * tchar         = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" / \"*\"\n *               / \"+\" / \"-\" / \".\" / \"^\" / \"_\" / \"`\" / \"|\" / \"~\"\n *               / DIGIT / ALPHA\n *               ; any VCHAR, except delimiters\n * quoted-string = DQUOTE *( qdtext / quoted-pair ) DQUOTE\n * qdtext        = HTAB / SP / %x21 / %x23-5B / %x5D-7E / obs-text\n * obs-text      = %x80-FF\n * quoted-pair   = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n */ var PARAM_REGEXP = /; *([!#$%&'*+.^_`|~0-9A-Za-z-]+) *= *(\"(?:[\\u000b\\u0020\\u0021\\u0023-\\u005b\\u005d-\\u007e\\u0080-\\u00ff]|\\\\[\\u000b\\u0020-\\u00ff])*\"|[!#$%&'*+.^_`|~0-9A-Za-z-]+) */g // eslint-disable-line no-control-regex\n;\nvar TEXT_REGEXP = /^[\\u000b\\u0020-\\u007e\\u0080-\\u00ff]+$/ // eslint-disable-line no-control-regex\n;\nvar TOKEN_REGEXP = /^[!#$%&'*+.^_`|~0-9A-Za-z-]+$/;\n/**\n * RegExp to match quoted-pair in RFC 7230 sec 3.2.6\n *\n * quoted-pair = \"\\\" ( HTAB / SP / VCHAR / obs-text )\n * obs-text    = %x80-FF\n */ var QESC_REGEXP = /\\\\([\\u000b\\u0020-\\u00ff])/g // eslint-disable-line no-control-regex\n;\n/**\n * RegExp to match chars that must be quoted-pair in RFC 7230 sec 3.2.6\n */ var QUOTE_REGEXP = /([\\\\\"])/g;\n/**\n * RegExp to match type in RFC 7231 sec 3.1.1.1\n *\n * media-type = type \"/\" subtype\n * type       = token\n * subtype    = token\n */ var TYPE_REGEXP = /^[!#$%&'*+.^_`|~0-9A-Za-z-]+\\/[!#$%&'*+.^_`|~0-9A-Za-z-]+$/;\n/**\n * Module exports.\n * @public\n */ exports.format = format;\nexports.parse = parse;\n/**\n * Format object to media type.\n *\n * @param {object} obj\n * @return {string}\n * @public\n */ function format(obj) {\n    if (!obj || typeof obj !== \"object\") {\n        throw new TypeError(\"argument obj is required\");\n    }\n    var parameters = obj.parameters;\n    var type = obj.type;\n    if (!type || !TYPE_REGEXP.test(type)) {\n        throw new TypeError(\"invalid type\");\n    }\n    var string = type;\n    // append parameters\n    if (parameters && typeof parameters === \"object\") {\n        var param;\n        var params = Object.keys(parameters).sort();\n        for(var i = 0; i < params.length; i++){\n            param = params[i];\n            if (!TOKEN_REGEXP.test(param)) {\n                throw new TypeError(\"invalid parameter name\");\n            }\n            string += \"; \" + param + \"=\" + qstring(parameters[param]);\n        }\n    }\n    return string;\n}\n/**\n * Parse media type to object.\n *\n * @param {string|object} string\n * @return {Object}\n * @public\n */ function parse(string) {\n    if (!string) {\n        throw new TypeError(\"argument string is required\");\n    }\n    // support req/res-like objects as argument\n    var header = typeof string === \"object\" ? getcontenttype(string) : string;\n    if (typeof header !== \"string\") {\n        throw new TypeError(\"argument string is required to be a string\");\n    }\n    var index = header.indexOf(\";\");\n    var type = index !== -1 ? header.slice(0, index).trim() : header.trim();\n    if (!TYPE_REGEXP.test(type)) {\n        throw new TypeError(\"invalid media type\");\n    }\n    var obj = new ContentType(type.toLowerCase());\n    // parse parameters\n    if (index !== -1) {\n        var key;\n        var match;\n        var value;\n        PARAM_REGEXP.lastIndex = index;\n        while(match = PARAM_REGEXP.exec(header)){\n            if (match.index !== index) {\n                throw new TypeError(\"invalid parameter format\");\n            }\n            index += match[0].length;\n            key = match[1].toLowerCase();\n            value = match[2];\n            if (value.charCodeAt(0) === 0x22 /* \" */ ) {\n                // remove quotes\n                value = value.slice(1, -1);\n                // remove escapes\n                if (value.indexOf(\"\\\\\") !== -1) {\n                    value = value.replace(QESC_REGEXP, \"$1\");\n                }\n            }\n            obj.parameters[key] = value;\n        }\n        if (index !== header.length) {\n            throw new TypeError(\"invalid parameter format\");\n        }\n    }\n    return obj;\n}\n/**\n * Get content-type from req/res objects.\n *\n * @param {object}\n * @return {Object}\n * @private\n */ function getcontenttype(obj) {\n    var header;\n    if (typeof obj.getHeader === \"function\") {\n        // res-like\n        header = obj.getHeader(\"content-type\");\n    } else if (typeof obj.headers === \"object\") {\n        // req-like\n        header = obj.headers && obj.headers[\"content-type\"];\n    }\n    if (typeof header !== \"string\") {\n        throw new TypeError(\"content-type header is missing from object\");\n    }\n    return header;\n}\n/**\n * Quote a string if necessary.\n *\n * @param {string} val\n * @return {string}\n * @private\n */ function qstring(val) {\n    var str = String(val);\n    // no need to quote tokens\n    if (TOKEN_REGEXP.test(str)) {\n        return str;\n    }\n    if (str.length > 0 && !TEXT_REGEXP.test(str)) {\n        throw new TypeError(\"invalid parameter value\");\n    }\n    return '\"' + str.replace(QUOTE_REGEXP, \"\\\\$1\") + '\"';\n}\n/**\n * Class to represent a content type.\n * @private\n */ function ContentType(type) {\n    this.parameters = Object.create(null);\n    this.type = type;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/content-type/index.js\n");

/***/ })

};
;