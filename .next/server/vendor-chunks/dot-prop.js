"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dot-prop";
exports.ids = ["vendor-chunks/dot-prop"];
exports.modules = {

/***/ "(rsc)/./node_modules/dot-prop/index.js":
/*!****************************************!*\
  !*** ./node_modules/dot-prop/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst isObj = __webpack_require__(/*! is-obj */ \"(rsc)/./node_modules/is-obj/index.js\");\nconst disallowedKeys = new Set([\n    \"__proto__\",\n    \"prototype\",\n    \"constructor\"\n]);\nconst isValidPath = (pathSegments)=>!pathSegments.some((segment)=>disallowedKeys.has(segment));\nfunction getPathSegments(path) {\n    const pathArray = path.split(\".\");\n    const parts = [];\n    for(let i = 0; i < pathArray.length; i++){\n        let p = pathArray[i];\n        while(p[p.length - 1] === \"\\\\\" && pathArray[i + 1] !== undefined){\n            p = p.slice(0, -1) + \".\";\n            p += pathArray[++i];\n        }\n        parts.push(p);\n    }\n    if (!isValidPath(parts)) {\n        return [];\n    }\n    return parts;\n}\nmodule.exports = {\n    get (object, path, value) {\n        if (!isObj(object) || typeof path !== \"string\") {\n            return value === undefined ? object : value;\n        }\n        const pathArray = getPathSegments(path);\n        if (pathArray.length === 0) {\n            return;\n        }\n        for(let i = 0; i < pathArray.length; i++){\n            object = object[pathArray[i]];\n            if (object === undefined || object === null) {\n                // `object` is either `undefined` or `null` so we want to stop the loop, and\n                // if this is not the last bit of the path, and\n                // if it did't return `undefined`\n                // it would return `null` if `object` is `null`\n                // but we want `get({foo: null}, 'foo.bar')` to equal `undefined`, or the supplied value, not `null`\n                if (i !== pathArray.length - 1) {\n                    return value;\n                }\n                break;\n            }\n        }\n        return object === undefined ? value : object;\n    },\n    set (object, path, value) {\n        if (!isObj(object) || typeof path !== \"string\") {\n            return object;\n        }\n        const root = object;\n        const pathArray = getPathSegments(path);\n        for(let i = 0; i < pathArray.length; i++){\n            const p = pathArray[i];\n            if (!isObj(object[p])) {\n                object[p] = {};\n            }\n            if (i === pathArray.length - 1) {\n                object[p] = value;\n            }\n            object = object[p];\n        }\n        return root;\n    },\n    delete (object, path) {\n        if (!isObj(object) || typeof path !== \"string\") {\n            return false;\n        }\n        const pathArray = getPathSegments(path);\n        for(let i = 0; i < pathArray.length; i++){\n            const p = pathArray[i];\n            if (i === pathArray.length - 1) {\n                delete object[p];\n                return true;\n            }\n            object = object[p];\n            if (!isObj(object)) {\n                return false;\n            }\n        }\n    },\n    has (object, path) {\n        if (!isObj(object) || typeof path !== \"string\") {\n            return false;\n        }\n        const pathArray = getPathSegments(path);\n        if (pathArray.length === 0) {\n            return false;\n        }\n        // eslint-disable-next-line unicorn/no-for-loop\n        for(let i = 0; i < pathArray.length; i++){\n            if (isObj(object)) {\n                if (!(pathArray[i] in object)) {\n                    return false;\n                }\n                object = object[pathArray[i]];\n            } else {\n                return false;\n            }\n        }\n        return true;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dot-prop/index.js\n");

/***/ })

};
;