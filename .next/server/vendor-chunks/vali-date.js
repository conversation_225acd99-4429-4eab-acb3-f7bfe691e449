"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vali-date";
exports.ids = ["vendor-chunks/vali-date"];
exports.modules = {

/***/ "(rsc)/./node_modules/vali-date/index.js":
/*!*****************************************!*\
  !*** ./node_modules/vali-date/index.js ***!
  \*****************************************/
/***/ ((module) => {

eval("\nmodule.exports = function(str) {\n    return !isNaN(Date.parse(str));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdmFsaS1kYXRlL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0FBLE9BQU9DLE9BQU8sR0FBRyxTQUFVQyxHQUFHO0lBQzdCLE9BQU8sQ0FBQ0MsTUFBTUMsS0FBS0MsS0FBSyxDQUFDSDtBQUMxQiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zY3JhcGVyLy4vbm9kZV9tb2R1bGVzL3ZhbGktZGF0ZS9pbmRleC5qcz9lNDJjIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0Jztcbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKHN0cikge1xuXHRyZXR1cm4gIWlzTmFOKERhdGUucGFyc2Uoc3RyKSk7XG59O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJzdHIiLCJpc05hTiIsIkRhdGUiLCJwYXJzZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/vali-date/index.js\n");

/***/ })

};
;