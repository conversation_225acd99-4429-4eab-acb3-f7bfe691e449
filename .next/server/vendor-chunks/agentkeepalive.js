"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/agentkeepalive";
exports.ids = ["vendor-chunks/agentkeepalive"];
exports.modules = {

/***/ "(rsc)/./node_modules/agentkeepalive/index.js":
/*!**********************************************!*\
  !*** ./node_modules/agentkeepalive/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst HttpAgent = __webpack_require__(/*! ./lib/agent */ \"(rsc)/./node_modules/agentkeepalive/lib/agent.js\");\nmodule.exports = HttpAgent;\nmodule.exports.HttpAgent = HttpAgent;\nmodule.exports.HttpsAgent = __webpack_require__(/*! ./lib/https_agent */ \"(rsc)/./node_modules/agentkeepalive/lib/https_agent.js\");\nmodule.exports.constants = __webpack_require__(/*! ./lib/constants */ \"(rsc)/./node_modules/agentkeepalive/lib/constants.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYWdlbnRrZWVwYWxpdmUvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxNQUFNQSxZQUFZQyxtQkFBT0EsQ0FBQztBQUMxQkMsT0FBT0MsT0FBTyxHQUFHSDtBQUNqQkUsd0JBQXdCLEdBQUdGO0FBQzNCRSxrSUFBb0M7QUFDcENBLDZIQUFtQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zY3JhcGVyLy4vbm9kZV9tb2R1bGVzL2FnZW50a2VlcGFsaXZlL2luZGV4LmpzPzBjMjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCBIdHRwQWdlbnQgPSByZXF1aXJlKCcuL2xpYi9hZ2VudCcpO1xubW9kdWxlLmV4cG9ydHMgPSBIdHRwQWdlbnQ7XG5tb2R1bGUuZXhwb3J0cy5IdHRwQWdlbnQgPSBIdHRwQWdlbnQ7XG5tb2R1bGUuZXhwb3J0cy5IdHRwc0FnZW50ID0gcmVxdWlyZSgnLi9saWIvaHR0cHNfYWdlbnQnKTtcbm1vZHVsZS5leHBvcnRzLmNvbnN0YW50cyA9IHJlcXVpcmUoJy4vbGliL2NvbnN0YW50cycpO1xuIl0sIm5hbWVzIjpbIkh0dHBBZ2VudCIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwiSHR0cHNBZ2VudCIsImNvbnN0YW50cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agentkeepalive/lib/agent.js":
/*!**************************************************!*\
  !*** ./node_modules/agentkeepalive/lib/agent.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst OriginalAgent = (__webpack_require__(/*! http */ \"http\").Agent);\nconst ms = __webpack_require__(/*! humanize-ms */ \"(rsc)/./node_modules/humanize-ms/index.js\");\nconst debug = (__webpack_require__(/*! util */ \"util\").debuglog)(\"agentkeepalive\");\nconst { INIT_SOCKET, CURRENT_ID, CREATE_ID, SOCKET_CREATED_TIME, SOCKET_NAME, SOCKET_REQUEST_COUNT, SOCKET_REQUEST_FINISHED_COUNT } = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/agentkeepalive/lib/constants.js\");\n// OriginalAgent come from\n// - https://github.com/nodejs/node/blob/v8.12.0/lib/_http_agent.js\n// - https://github.com/nodejs/node/blob/v10.12.0/lib/_http_agent.js\n// node <= 10\nlet defaultTimeoutListenerCount = 1;\nconst majorVersion = parseInt(process.version.split(\".\", 1)[0].substring(1));\nif (majorVersion >= 11 && majorVersion <= 12) {\n    defaultTimeoutListenerCount = 2;\n} else if (majorVersion >= 13) {\n    defaultTimeoutListenerCount = 3;\n}\nfunction deprecate(message) {\n    console.log(\"[agentkeepalive:deprecated] %s\", message);\n}\nclass Agent extends OriginalAgent {\n    constructor(options){\n        options = options || {};\n        options.keepAlive = options.keepAlive !== false;\n        // default is keep-alive and 4s free socket timeout\n        // see https://medium.com/ssense-tech/reduce-networking-errors-in-nodejs-23b4eb9f2d83\n        if (options.freeSocketTimeout === undefined) {\n            options.freeSocketTimeout = 4000;\n        }\n        // Legacy API: keepAliveTimeout should be rename to `freeSocketTimeout`\n        if (options.keepAliveTimeout) {\n            deprecate(\"options.keepAliveTimeout is deprecated, please use options.freeSocketTimeout instead\");\n            options.freeSocketTimeout = options.keepAliveTimeout;\n            delete options.keepAliveTimeout;\n        }\n        // Legacy API: freeSocketKeepAliveTimeout should be rename to `freeSocketTimeout`\n        if (options.freeSocketKeepAliveTimeout) {\n            deprecate(\"options.freeSocketKeepAliveTimeout is deprecated, please use options.freeSocketTimeout instead\");\n            options.freeSocketTimeout = options.freeSocketKeepAliveTimeout;\n            delete options.freeSocketKeepAliveTimeout;\n        }\n        // Sets the socket to timeout after timeout milliseconds of inactivity on the socket.\n        // By default is double free socket timeout.\n        if (options.timeout === undefined) {\n            // make sure socket default inactivity timeout >= 8s\n            options.timeout = Math.max(options.freeSocketTimeout * 2, 8000);\n        }\n        // support humanize format\n        options.timeout = ms(options.timeout);\n        options.freeSocketTimeout = ms(options.freeSocketTimeout);\n        options.socketActiveTTL = options.socketActiveTTL ? ms(options.socketActiveTTL) : 0;\n        super(options);\n        this[CURRENT_ID] = 0;\n        // create socket success counter\n        this.createSocketCount = 0;\n        this.createSocketCountLastCheck = 0;\n        this.createSocketErrorCount = 0;\n        this.createSocketErrorCountLastCheck = 0;\n        this.closeSocketCount = 0;\n        this.closeSocketCountLastCheck = 0;\n        // socket error event count\n        this.errorSocketCount = 0;\n        this.errorSocketCountLastCheck = 0;\n        // request finished counter\n        this.requestCount = 0;\n        this.requestCountLastCheck = 0;\n        // including free socket timeout counter\n        this.timeoutSocketCount = 0;\n        this.timeoutSocketCountLastCheck = 0;\n        this.on(\"free\", (socket)=>{\n            // https://github.com/nodejs/node/pull/32000\n            // Node.js native agent will check socket timeout eqs agent.options.timeout.\n            // Use the ttl or freeSocketTimeout to overwrite.\n            const timeout = this.calcSocketTimeout(socket);\n            if (timeout > 0 && socket.timeout !== timeout) {\n                socket.setTimeout(timeout);\n            }\n        });\n    }\n    get freeSocketKeepAliveTimeout() {\n        deprecate(\"agent.freeSocketKeepAliveTimeout is deprecated, please use agent.options.freeSocketTimeout instead\");\n        return this.options.freeSocketTimeout;\n    }\n    get timeout() {\n        deprecate(\"agent.timeout is deprecated, please use agent.options.timeout instead\");\n        return this.options.timeout;\n    }\n    get socketActiveTTL() {\n        deprecate(\"agent.socketActiveTTL is deprecated, please use agent.options.socketActiveTTL instead\");\n        return this.options.socketActiveTTL;\n    }\n    calcSocketTimeout(socket) {\n        /**\n     * return <= 0: should free socket\n     * return > 0: should update socket timeout\n     * return undefined: not find custom timeout\n     */ let freeSocketTimeout = this.options.freeSocketTimeout;\n        const socketActiveTTL = this.options.socketActiveTTL;\n        if (socketActiveTTL) {\n            // check socketActiveTTL\n            const aliveTime = Date.now() - socket[SOCKET_CREATED_TIME];\n            const diff = socketActiveTTL - aliveTime;\n            if (diff <= 0) {\n                return diff;\n            }\n            if (freeSocketTimeout && diff < freeSocketTimeout) {\n                freeSocketTimeout = diff;\n            }\n        }\n        // set freeSocketTimeout\n        if (freeSocketTimeout) {\n            // set free keepalive timer\n            // try to use socket custom freeSocketTimeout first, support headers['keep-alive']\n            // https://github.com/node-modules/urllib/blob/b76053020923f4d99a1c93cf2e16e0c5ba10bacf/lib/urllib.js#L498\n            const customFreeSocketTimeout = socket.freeSocketTimeout || socket.freeSocketKeepAliveTimeout;\n            return customFreeSocketTimeout || freeSocketTimeout;\n        }\n    }\n    keepSocketAlive(socket) {\n        const result = super.keepSocketAlive(socket);\n        // should not keepAlive, do nothing\n        if (!result) return result;\n        const customTimeout = this.calcSocketTimeout(socket);\n        if (typeof customTimeout === \"undefined\") {\n            return true;\n        }\n        if (customTimeout <= 0) {\n            debug(\"%s(requests: %s, finished: %s) free but need to destroy by TTL, request count %s, diff is %s\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], customTimeout);\n            return false;\n        }\n        if (socket.timeout !== customTimeout) {\n            socket.setTimeout(customTimeout);\n        }\n        return true;\n    }\n    // only call on addRequest\n    reuseSocket(...args) {\n        // reuseSocket(socket, req)\n        super.reuseSocket(...args);\n        const socket = args[0];\n        const req = args[1];\n        req.reusedSocket = true;\n        const agentTimeout = this.options.timeout;\n        if (getSocketTimeout(socket) !== agentTimeout) {\n            // reset timeout before use\n            socket.setTimeout(agentTimeout);\n            debug(\"%s reset timeout to %sms\", socket[SOCKET_NAME], agentTimeout);\n        }\n        socket[SOCKET_REQUEST_COUNT]++;\n        debug(\"%s(requests: %s, finished: %s) reuse on addRequest, timeout %sms\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], getSocketTimeout(socket));\n    }\n    [CREATE_ID]() {\n        const id = this[CURRENT_ID]++;\n        if (this[CURRENT_ID] === Number.MAX_SAFE_INTEGER) this[CURRENT_ID] = 0;\n        return id;\n    }\n    [INIT_SOCKET](socket, options) {\n        // bugfix here.\n        // https on node 8, 10 won't set agent.options.timeout by default\n        // TODO: need to fix on node itself\n        if (options.timeout) {\n            const timeout = getSocketTimeout(socket);\n            if (!timeout) {\n                socket.setTimeout(options.timeout);\n            }\n        }\n        if (this.options.keepAlive) {\n            // Disable Nagle's algorithm: http://blog.caustik.com/2012/04/08/scaling-node-js-to-100k-concurrent-connections/\n            // https://fengmk2.com/benchmark/nagle-algorithm-delayed-ack-mock.html\n            socket.setNoDelay(true);\n        }\n        this.createSocketCount++;\n        if (this.options.socketActiveTTL) {\n            socket[SOCKET_CREATED_TIME] = Date.now();\n        }\n        // don't show the hole '-----BEGIN CERTIFICATE----' key string\n        socket[SOCKET_NAME] = `sock[${this[CREATE_ID]()}#${options._agentKey}]`.split(\"-----BEGIN\", 1)[0];\n        socket[SOCKET_REQUEST_COUNT] = 1;\n        socket[SOCKET_REQUEST_FINISHED_COUNT] = 0;\n        installListeners(this, socket, options);\n    }\n    createConnection(options, oncreate) {\n        let called = false;\n        const onNewCreate = (err, socket)=>{\n            if (called) return;\n            called = true;\n            if (err) {\n                this.createSocketErrorCount++;\n                return oncreate(err);\n            }\n            this[INIT_SOCKET](socket, options);\n            oncreate(err, socket);\n        };\n        const newSocket = super.createConnection(options, onNewCreate);\n        if (newSocket) onNewCreate(null, newSocket);\n        return newSocket;\n    }\n    get statusChanged() {\n        const changed = this.createSocketCount !== this.createSocketCountLastCheck || this.createSocketErrorCount !== this.createSocketErrorCountLastCheck || this.closeSocketCount !== this.closeSocketCountLastCheck || this.errorSocketCount !== this.errorSocketCountLastCheck || this.timeoutSocketCount !== this.timeoutSocketCountLastCheck || this.requestCount !== this.requestCountLastCheck;\n        if (changed) {\n            this.createSocketCountLastCheck = this.createSocketCount;\n            this.createSocketErrorCountLastCheck = this.createSocketErrorCount;\n            this.closeSocketCountLastCheck = this.closeSocketCount;\n            this.errorSocketCountLastCheck = this.errorSocketCount;\n            this.timeoutSocketCountLastCheck = this.timeoutSocketCount;\n            this.requestCountLastCheck = this.requestCount;\n        }\n        return changed;\n    }\n    getCurrentStatus() {\n        return {\n            createSocketCount: this.createSocketCount,\n            createSocketErrorCount: this.createSocketErrorCount,\n            closeSocketCount: this.closeSocketCount,\n            errorSocketCount: this.errorSocketCount,\n            timeoutSocketCount: this.timeoutSocketCount,\n            requestCount: this.requestCount,\n            freeSockets: inspect(this.freeSockets),\n            sockets: inspect(this.sockets),\n            requests: inspect(this.requests)\n        };\n    }\n}\n// node 8 don't has timeout attribute on socket\n// https://github.com/nodejs/node/pull/21204/files#diff-e6ef024c3775d787c38487a6309e491dR408\nfunction getSocketTimeout(socket) {\n    return socket.timeout || socket._idleTimeout;\n}\nfunction installListeners(agent, socket, options) {\n    debug(\"%s create, timeout %sms\", socket[SOCKET_NAME], getSocketTimeout(socket));\n    // listener socket events: close, timeout, error, free\n    function onFree() {\n        // create and socket.emit('free') logic\n        // https://github.com/nodejs/node/blob/master/lib/_http_agent.js#L311\n        // no req on the socket, it should be the new socket\n        if (!socket._httpMessage && socket[SOCKET_REQUEST_COUNT] === 1) return;\n        socket[SOCKET_REQUEST_FINISHED_COUNT]++;\n        agent.requestCount++;\n        debug(\"%s(requests: %s, finished: %s) free\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);\n        // should reuse on pedding requests?\n        const name = agent.getName(options);\n        if (socket.writable && agent.requests[name] && agent.requests[name].length) {\n            // will be reuse on agent free listener\n            socket[SOCKET_REQUEST_COUNT]++;\n            debug(\"%s(requests: %s, finished: %s) will be reuse on agent free event\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);\n        }\n    }\n    socket.on(\"free\", onFree);\n    function onClose(isError) {\n        debug(\"%s(requests: %s, finished: %s) close, isError: %s\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], isError);\n        agent.closeSocketCount++;\n    }\n    socket.on(\"close\", onClose);\n    // start socket timeout handler\n    function onTimeout() {\n        // onTimeout and emitRequestTimeout(_http_client.js)\n        // https://github.com/nodejs/node/blob/v12.x/lib/_http_client.js#L711\n        const listenerCount = socket.listeners(\"timeout\").length;\n        // node <= 10, default listenerCount is 1, onTimeout\n        // 11 < node <= 12, default listenerCount is 2, onTimeout and emitRequestTimeout\n        // node >= 13, default listenerCount is 3, onTimeout,\n        //   onTimeout(https://github.com/nodejs/node/pull/32000/files#diff-5f7fb0850412c6be189faeddea6c5359R333)\n        //   and emitRequestTimeout\n        const timeout = getSocketTimeout(socket);\n        const req = socket._httpMessage;\n        const reqTimeoutListenerCount = req && req.listeners(\"timeout\").length || 0;\n        debug(\"%s(requests: %s, finished: %s) timeout after %sms, listeners %s, defaultTimeoutListenerCount %s, hasHttpRequest %s, HttpRequest timeoutListenerCount %s\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], timeout, listenerCount, defaultTimeoutListenerCount, !!req, reqTimeoutListenerCount);\n        if (debug.enabled) {\n            debug(\"timeout listeners: %s\", socket.listeners(\"timeout\").map((f)=>f.name).join(\", \"));\n        }\n        agent.timeoutSocketCount++;\n        const name = agent.getName(options);\n        if (agent.freeSockets[name] && agent.freeSockets[name].indexOf(socket) !== -1) {\n            // free socket timeout, destroy quietly\n            socket.destroy();\n            // Remove it from freeSockets list immediately to prevent new requests\n            // from being sent through this socket.\n            agent.removeSocket(socket, options);\n            debug(\"%s is free, destroy quietly\", socket[SOCKET_NAME]);\n        } else {\n            // if there is no any request socket timeout handler,\n            // agent need to handle socket timeout itself.\n            //\n            // custom request socket timeout handle logic must follow these rules:\n            //  1. Destroy socket first\n            //  2. Must emit socket 'agentRemove' event tell agent remove socket\n            //     from freeSockets list immediately.\n            //     Otherise you may be get 'socket hang up' error when reuse\n            //     free socket and timeout happen in the same time.\n            if (reqTimeoutListenerCount === 0) {\n                const error = new Error(\"Socket timeout\");\n                error.code = \"ERR_SOCKET_TIMEOUT\";\n                error.timeout = timeout;\n                // must manually call socket.end() or socket.destroy() to end the connection.\n                // https://nodejs.org/dist/latest-v10.x/docs/api/net.html#net_socket_settimeout_timeout_callback\n                socket.destroy(error);\n                agent.removeSocket(socket, options);\n                debug(\"%s destroy with timeout error\", socket[SOCKET_NAME]);\n            }\n        }\n    }\n    socket.on(\"timeout\", onTimeout);\n    function onError(err) {\n        const listenerCount = socket.listeners(\"error\").length;\n        debug(\"%s(requests: %s, finished: %s) error: %s, listenerCount: %s\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], err, listenerCount);\n        agent.errorSocketCount++;\n        if (listenerCount === 1) {\n            // if socket don't contain error event handler, don't catch it, emit it again\n            debug(\"%s emit uncaught error event\", socket[SOCKET_NAME]);\n            socket.removeListener(\"error\", onError);\n            socket.emit(\"error\", err);\n        }\n    }\n    socket.on(\"error\", onError);\n    function onRemove() {\n        debug(\"%s(requests: %s, finished: %s) agentRemove\", socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);\n        // We need this function for cases like HTTP 'upgrade'\n        // (defined by WebSockets) where we need to remove a socket from the\n        // pool because it'll be locked up indefinitely\n        socket.removeListener(\"close\", onClose);\n        socket.removeListener(\"error\", onError);\n        socket.removeListener(\"free\", onFree);\n        socket.removeListener(\"timeout\", onTimeout);\n        socket.removeListener(\"agentRemove\", onRemove);\n    }\n    socket.on(\"agentRemove\", onRemove);\n}\nmodule.exports = Agent;\nfunction inspect(obj) {\n    const res = {};\n    for(const key in obj){\n        res[key] = obj[key].length;\n    }\n    return res;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/lib/agent.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agentkeepalive/lib/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/agentkeepalive/lib/constants.js ***!
  \******************************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n    // agent\n    CURRENT_ID: Symbol(\"agentkeepalive#currentId\"),\n    CREATE_ID: Symbol(\"agentkeepalive#createId\"),\n    INIT_SOCKET: Symbol(\"agentkeepalive#initSocket\"),\n    CREATE_HTTPS_CONNECTION: Symbol(\"agentkeepalive#createHttpsConnection\"),\n    // socket\n    SOCKET_CREATED_TIME: Symbol(\"agentkeepalive#socketCreatedTime\"),\n    SOCKET_NAME: Symbol(\"agentkeepalive#socketName\"),\n    SOCKET_REQUEST_COUNT: Symbol(\"agentkeepalive#socketRequestCount\"),\n    SOCKET_REQUEST_FINISHED_COUNT: Symbol(\"agentkeepalive#socketRequestFinishedCount\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYWdlbnRrZWVwYWxpdmUvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBQSxPQUFPQyxPQUFPLEdBQUc7SUFDZixRQUFRO0lBQ1JDLFlBQVlDLE9BQU87SUFDbkJDLFdBQVdELE9BQU87SUFDbEJFLGFBQWFGLE9BQU87SUFDcEJHLHlCQUF5QkgsT0FBTztJQUNoQyxTQUFTO0lBQ1RJLHFCQUFxQkosT0FBTztJQUM1QkssYUFBYUwsT0FBTztJQUNwQk0sc0JBQXNCTixPQUFPO0lBQzdCTywrQkFBK0JQLE9BQU87QUFDeEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2NyYXBlci8uL25vZGVfbW9kdWxlcy9hZ2VudGtlZXBhbGl2ZS9saWIvY29uc3RhbnRzLmpzPzM5MWYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgLy8gYWdlbnRcbiAgQ1VSUkVOVF9JRDogU3ltYm9sKCdhZ2VudGtlZXBhbGl2ZSNjdXJyZW50SWQnKSxcbiAgQ1JFQVRFX0lEOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI2NyZWF0ZUlkJyksXG4gIElOSVRfU09DS0VUOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI2luaXRTb2NrZXQnKSxcbiAgQ1JFQVRFX0hUVFBTX0NPTk5FQ1RJT046IFN5bWJvbCgnYWdlbnRrZWVwYWxpdmUjY3JlYXRlSHR0cHNDb25uZWN0aW9uJyksXG4gIC8vIHNvY2tldFxuICBTT0NLRVRfQ1JFQVRFRF9USU1FOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI3NvY2tldENyZWF0ZWRUaW1lJyksXG4gIFNPQ0tFVF9OQU1FOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI3NvY2tldE5hbWUnKSxcbiAgU09DS0VUX1JFUVVFU1RfQ09VTlQ6IFN5bWJvbCgnYWdlbnRrZWVwYWxpdmUjc29ja2V0UmVxdWVzdENvdW50JyksXG4gIFNPQ0tFVF9SRVFVRVNUX0ZJTklTSEVEX0NPVU5UOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI3NvY2tldFJlcXVlc3RGaW5pc2hlZENvdW50JyksXG59O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJDVVJSRU5UX0lEIiwiU3ltYm9sIiwiQ1JFQVRFX0lEIiwiSU5JVF9TT0NLRVQiLCJDUkVBVEVfSFRUUFNfQ09OTkVDVElPTiIsIlNPQ0tFVF9DUkVBVEVEX1RJTUUiLCJTT0NLRVRfTkFNRSIsIlNPQ0tFVF9SRVFVRVNUX0NPVU5UIiwiU09DS0VUX1JFUVVFU1RfRklOSVNIRURfQ09VTlQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agentkeepalive/lib/https_agent.js":
/*!********************************************************!*\
  !*** ./node_modules/agentkeepalive/lib/https_agent.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst OriginalHttpsAgent = (__webpack_require__(/*! https */ \"https\").Agent);\nconst HttpAgent = __webpack_require__(/*! ./agent */ \"(rsc)/./node_modules/agentkeepalive/lib/agent.js\");\nconst { INIT_SOCKET, CREATE_HTTPS_CONNECTION } = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/agentkeepalive/lib/constants.js\");\nclass HttpsAgent extends HttpAgent {\n    constructor(options){\n        super(options);\n        this.defaultPort = 443;\n        this.protocol = \"https:\";\n        this.maxCachedSessions = this.options.maxCachedSessions;\n        /* istanbul ignore next */ if (this.maxCachedSessions === undefined) {\n            this.maxCachedSessions = 100;\n        }\n        this._sessionCache = {\n            map: {},\n            list: []\n        };\n    }\n    createConnection(options, oncreate) {\n        const socket = this[CREATE_HTTPS_CONNECTION](options, oncreate);\n        this[INIT_SOCKET](socket, options);\n        return socket;\n    }\n}\n// https://github.com/nodejs/node/blob/master/lib/https.js#L89\nHttpsAgent.prototype[CREATE_HTTPS_CONNECTION] = OriginalHttpsAgent.prototype.createConnection;\n[\n    \"getName\",\n    \"_getSession\",\n    \"_cacheSession\",\n    // https://github.com/nodejs/node/pull/4982\n    \"_evictSession\"\n].forEach(function(method) {\n    /* istanbul ignore next */ if (typeof OriginalHttpsAgent.prototype[method] === \"function\") {\n        HttpsAgent.prototype[method] = OriginalHttpsAgent.prototype[method];\n    }\n});\nmodule.exports = HttpsAgent;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/lib/https_agent.js\n");

/***/ })

};
;