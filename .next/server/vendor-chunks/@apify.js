"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@apify";
exports.ids = ["vendor-chunks/@apify"];
exports.modules = {

/***/ "(rsc)/./node_modules/@apify/consts/cjs/index.cjs":
/*!**************************************************!*\
  !*** ./node_modules/@apify/consts/cjs/index.cjs ***!
  \**************************************************/
/***/ ((module) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all)=>{\n    for(var name in all)__defProp(target, name, {\n        get: all[name],\n        enumerable: true\n    });\n};\nvar __copyProps = (to, from, except, desc)=>{\n    if (from && typeof from === \"object\" || typeof from === \"function\") {\n        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n            get: ()=>from[key],\n            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n        });\n    }\n    return to;\n};\nvar __toCommonJS = (mod)=>__copyProps(__defProp({}, \"__esModule\", {\n        value: true\n    }), mod);\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n    ACTOR_BUILD_ARGS: ()=>ACTOR_BUILD_ARGS,\n    ACTOR_CATEGORIES: ()=>ACTOR_CATEGORIES,\n    ACTOR_ENV_VARS: ()=>ACTOR_ENV_VARS,\n    ACTOR_EVENT_NAMES: ()=>ACTOR_EVENT_NAMES,\n    ACTOR_JOB_STATUSES: ()=>ACTOR_JOB_STATUSES,\n    ACTOR_JOB_TERMINAL_STATUSES: ()=>ACTOR_JOB_TERMINAL_STATUSES,\n    ACTOR_JOB_TYPES: ()=>ACTOR_JOB_TYPES,\n    ACTOR_LIMITS: ()=>ACTOR_LIMITS,\n    ACTOR_NAME: ()=>ACTOR_NAME,\n    ACTOR_PERMISSION_LEVEL: ()=>ACTOR_PERMISSION_LEVEL,\n    ACTOR_RESTART_ON_ERROR: ()=>ACTOR_RESTART_ON_ERROR,\n    ACTOR_SOURCE_TYPES: ()=>ACTOR_SOURCE_TYPES,\n    ACTOR_TYPES: ()=>ACTOR_TYPES,\n    ACT_JOB_STATUSES: ()=>ACT_JOB_STATUSES,\n    ACT_JOB_TERMINAL_STATUSES: ()=>ACT_JOB_TERMINAL_STATUSES,\n    ACT_JOB_TYPES: ()=>ACT_JOB_TYPES,\n    ACT_RESTART_ON_ERROR: ()=>ACT_RESTART_ON_ERROR,\n    ACT_SOURCE_TYPES: ()=>ACT_SOURCE_TYPES,\n    ACT_TYPES: ()=>ACT_TYPES,\n    ALL_ACTOR_CATEGORIES: ()=>ALL_ACTOR_CATEGORIES,\n    ANONYMOUS_USERNAME: ()=>ANONYMOUS_USERNAME,\n    APIFY_ENV_VARS: ()=>APIFY_ENV_VARS,\n    APIFY_ID_REGEX: ()=>APIFY_ID_REGEX,\n    APIFY_PROXY_VALUE_REGEX: ()=>APIFY_PROXY_VALUE_REGEX,\n    BUILD_TAG_LATEST: ()=>BUILD_TAG_LATEST,\n    COMMA_SEPARATED_EMAILS_REGEX: ()=>COMMA_SEPARATED_EMAILS_REGEX,\n    COMMA_SEPARATED_EMAILS_REGEX_STR: ()=>COMMA_SEPARATED_EMAILS_REGEX_STR,\n    COMMA_SEPARATED_LIST_ENV_VARS: ()=>COMMA_SEPARATED_LIST_ENV_VARS,\n    COMPUTE_UNIT_MB: ()=>COMPUTE_UNIT_MB,\n    COMPUTE_UNIT_MILLIS: ()=>COMPUTE_UNIT_MILLIS,\n    CONTACT_LINK_REGEX: ()=>CONTACT_LINK_REGEX,\n    DEFAULT_ACTOR_STANDBY_PORT: ()=>DEFAULT_ACTOR_STANDBY_PORT,\n    DEFAULT_CONTAINER_PORT: ()=>DEFAULT_CONTAINER_PORT,\n    DEFAULT_PLATFORM_LIMITS: ()=>DEFAULT_PLATFORM_LIMITS,\n    DNS_SAFE_NAME_MAX_LENGTH: ()=>DNS_SAFE_NAME_MAX_LENGTH,\n    DNS_SAFE_NAME_REGEX: ()=>DNS_SAFE_NAME_REGEX,\n    DOCKER_LABELS: ()=>DOCKER_LABELS,\n    EMAIL: ()=>EMAIL,\n    EMAIL_REGEX: ()=>EMAIL_REGEX,\n    EMAIL_REGEX_STR: ()=>EMAIL_REGEX_STR,\n    ENV_VARS: ()=>ENV_VARS,\n    FINISHED_PROJECT_STATUSES: ()=>FINISHED_PROJECT_STATUSES,\n    FREE_SUBSCRIPTION_PLAN_CODE: ()=>FREE_SUBSCRIPTION_PLAN_CODE,\n    GITHUB_GIST_URL_REGEX: ()=>GITHUB_GIST_URL_REGEX,\n    GITHUB_REGEX: ()=>GITHUB_REGEX,\n    GIT_MAIN_BRANCH: ()=>GIT_MAIN_BRANCH,\n    GIT_REPO_REGEX: ()=>GIT_REPO_REGEX,\n    HTTP_URL_REGEX: ()=>HTTP_URL_REGEX,\n    INTEGER_ENV_VARS: ()=>INTEGER_ENV_VARS,\n    ISSUES_STATUS_ALL: ()=>ISSUES_STATUS_ALL,\n    ISSUES_STATUS_TYPES: ()=>ISSUES_STATUS_TYPES,\n    KEY_VALUE_STORE_KEYS: ()=>KEY_VALUE_STORE_KEYS,\n    KEY_VALUE_STORE_KEY_REGEX: ()=>KEY_VALUE_STORE_KEY_REGEX,\n    LINKEDIN_PROFILE_REGEX: ()=>LINKEDIN_PROFILE_REGEX,\n    LOCAL_ACTOR_ENV_VARS: ()=>LOCAL_ACTOR_ENV_VARS,\n    LOCAL_APIFY_ENV_VARS: ()=>LOCAL_APIFY_ENV_VARS,\n    LOCAL_ENV_VARS: ()=>LOCAL_ENV_VARS,\n    LOCAL_STORAGE_SUBDIRS: ()=>LOCAL_STORAGE_SUBDIRS,\n    MARKETPLACE_USER_ROLES: ()=>MARKETPLACE_USER_ROLES,\n    MAX_MULTIFILE_BYTES: ()=>MAX_MULTIFILE_BYTES,\n    MAX_PAYLOAD_SIZE_BYTES: ()=>MAX_PAYLOAD_SIZE_BYTES,\n    META_ORIGINS: ()=>META_ORIGINS,\n    ME_USER_NAME_PLACEHOLDER: ()=>ME_USER_NAME_PLACEHOLDER,\n    PROFILE_NAME: ()=>PROFILE_NAME,\n    PROJECT_STATUSES: ()=>PROJECT_STATUSES,\n    PROXY_URL_REGEX: ()=>PROXY_URL_REGEX,\n    RELATIVE_URL_REGEX: ()=>RELATIVE_URL_REGEX,\n    REQUEST_QUEUE_HEAD_MAX_LIMIT: ()=>REQUEST_QUEUE_HEAD_MAX_LIMIT,\n    REQUEST_QUEUE_MAX_REQUESTS_PER_BATCH_OPERATION: ()=>REQUEST_QUEUE_MAX_REQUESTS_PER_BATCH_OPERATION,\n    RUN_GENERAL_ACCESS: ()=>RUN_GENERAL_ACCESS,\n    SHORT_CRAWLER_ID_LENGTH: ()=>SHORT_CRAWLER_ID_LENGTH,\n    SOURCE_FILE_FORMATS: ()=>SOURCE_FILE_FORMATS,\n    SPLIT_PATH_REGEX: ()=>SPLIT_PATH_REGEX,\n    STORAGE_GENERAL_ACCESS: ()=>STORAGE_GENERAL_ACCESS,\n    TWITTER_REGEX: ()=>TWITTER_REGEX,\n    URL_REGEX: ()=>URL_REGEX,\n    USERNAME: ()=>USERNAME,\n    USER_BASIC_TEXT_XSS_OPTIONS: ()=>USER_BASIC_TEXT_XSS_OPTIONS,\n    USER_PERSONA_TYPES: ()=>USER_PERSONA_TYPES,\n    VERSION_INT_MAJOR_BASE: ()=>VERSION_INT_MAJOR_BASE,\n    VERSION_INT_MINOR_BASE: ()=>VERSION_INT_MINOR_BASE,\n    WEBHOOK_ALLOWED_PAYLOAD_VARIABLES: ()=>WEBHOOK_ALLOWED_PAYLOAD_VARIABLES,\n    WEBHOOK_DEFAULT_PAYLOAD_TEMPLATE: ()=>WEBHOOK_DEFAULT_PAYLOAD_TEMPLATE,\n    WEBHOOK_DISPATCH_STATUSES: ()=>WEBHOOK_DISPATCH_STATUSES,\n    WEBHOOK_EVENT_TYPES: ()=>WEBHOOK_EVENT_TYPES,\n    WEBHOOK_EVENT_TYPE_GROUPS: ()=>WEBHOOK_EVENT_TYPE_GROUPS,\n    WORKER_SERVICE_TYPES: ()=>WORKER_SERVICE_TYPES\n});\nmodule.exports = __toCommonJS(index_exports);\n// src/regexs.ts\nvar namePartSubRegexStr = \"[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+\";\nvar nameSubRegexStr = `${namePartSubRegexStr}(?:\\\\.${namePartSubRegexStr})*`;\nvar domainPartSubRegexStr = \"[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\";\nvar domainSubRegexStr = `${domainPartSubRegexStr}(?:\\\\.${domainPartSubRegexStr})+`;\nvar EMAIL_REGEX_STR = `${nameSubRegexStr}@${domainSubRegexStr}`;\nvar EMAIL_REGEX = new RegExp(`^${EMAIL_REGEX_STR}$`);\nvar COMMA_SEPARATED_EMAILS_REGEX_STR = `(${EMAIL_REGEX_STR})( *, *${EMAIL_REGEX_STR})*`;\nvar COMMA_SEPARATED_EMAILS_REGEX = new RegExp(`^${COMMA_SEPARATED_EMAILS_REGEX_STR}$`);\nvar GIT_REPO_REGEX = /^(?:git|ssh|https?|git@[-\\w.]+):(\\/\\/)?(.*?)(\\/?|#[-\\d\\w._:/]+?)$/;\nvar DNS_SAFE_NAME_REGEX = /^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])$/;\nvar APIFY_PROXY_VALUE_REGEX = /^[\\w._~]+$/;\nvar PROXY_URL_REGEX = /^(socks(4|4a|5|5h)?|https?):\\/\\/(([^:]+:)?[^@]*@)?[^.:@]+\\.[^:]+:[\\d]+?$/;\nvar KEY_VALUE_STORE_KEY_REGEX = /^([a-zA-Z0-9!\\-_.'()]{1,256})$/;\nvar GITHUB_REGEX_STR = \"[a-z\\\\d](?:[a-z\\\\d]|-(?=[a-z\\\\d])){0,38}\";\nvar TWITTER_REGEX = /^@[a-z0-9_]{1,15}$/i;\nvar GITHUB_REGEX = new RegExp(`^${GITHUB_REGEX_STR}$`, \"i\");\nvar LINKEDIN_PROFILE_REGEX = /^(https?:\\/\\/)?(www\\.)?([a-z]{2}\\.)?linkedin.com\\/(in|company)\\/([A-Za-z0-9_-]+)\\/?$/;\nvar URL_REGEX = /^https?:\\/\\//i;\nvar HTTP_URL_REGEX = new RegExp(\"^(?:(?:(?:https?):)?\\\\/\\\\/)(?:\\\\S+(?::\\\\S*)?@)?(?:(?!(?:10|127)(?:\\\\.\\\\d{1,3}){3})(?!(?:169\\\\.254|192\\\\.168)(?:\\\\.\\\\d{1,3}){2})(?!172\\\\.(?:1[6-9]|2\\\\d|3[0-1])(?:\\\\.\\\\d{1,3}){2})(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[1-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z0-9\\\\u00a1-\\\\uffff][a-z0-9\\\\u00a1-\\\\uffff_-]{0,62})?[a-z0-9\\\\u00a1-\\\\uffff]\\\\.)+(?:[a-z\\\\u00a1-\\\\uffff]{2,}\\\\.?|xn--[a-z0-9]+))(?::\\\\d{2,5})?(?:[/?#]\\\\S*)?$\", \"i\");\nvar GITHUB_GIST_URL_REGEX = new RegExp(`^https:\\\\/\\\\/gist\\\\.github\\\\.com\\\\/${GITHUB_REGEX_STR}\\\\/[0-9a-f]{32}$`, \"i\");\nvar SPLIT_PATH_REGEX = /[^/]+/g;\nvar RELATIVE_URL_REGEX = /^(?!www\\.|(?:http|ftp)s?:\\/\\/|[A-Za-z]:\\|\\/\\/).*/i;\nvar CONTACT_LINK_REGEX = /^(mailto|tel|sms):.*$/i;\nvar APIFY_ID_REGEX = /[a-zA-Z0-9]{17}/;\n// src/consts.ts\nvar FREE_SUBSCRIPTION_PLAN_CODE = \"DEV\";\nvar ACTOR_JOB_TYPES = {\n    BUILD: \"BUILD\",\n    RUN: \"RUN\"\n};\nvar ACTOR_SOURCE_TYPES = {\n    SOURCE_CODE: \"SOURCE_CODE\",\n    SOURCE_FILES: \"SOURCE_FILES\",\n    GIT_REPO: \"GIT_REPO\",\n    TARBALL: \"TARBALL\",\n    GITHUB_GIST: \"GITHUB_GIST\"\n};\nvar ACTOR_EVENT_NAMES = {\n    CPU_INFO: \"cpuInfo\",\n    SYSTEM_INFO: \"systemInfo\",\n    MIGRATING: \"migrating\",\n    PERSIST_STATE: \"persistState\",\n    ABORTING: \"aborting\"\n};\nvar ACTOR_JOB_STATUSES = {\n    READY: \"READY\",\n    // started but not allocated to any worker yet\n    RUNNING: \"RUNNING\",\n    // running on worker\n    SUCCEEDED: \"SUCCEEDED\",\n    // finished and all good\n    FAILED: \"FAILED\",\n    // run or build failed\n    TIMING_OUT: \"TIMING-OUT\",\n    // timing out now\n    TIMED_OUT: \"TIMED-OUT\",\n    // timed out\n    ABORTING: \"ABORTING\",\n    // being aborted by user\n    ABORTED: \"ABORTED\"\n};\nvar WEBHOOK_DISPATCH_STATUSES = {\n    ACTIVE: \"ACTIVE\",\n    // Attempting to deliver the webhook\n    SUCCEEDED: \"SUCCEEDED\",\n    // Webhook was delivered\n    FAILED: \"FAILED\"\n};\nvar ACTOR_JOB_TERMINAL_STATUSES = [\n    ACTOR_JOB_STATUSES.SUCCEEDED,\n    ACTOR_JOB_STATUSES.FAILED,\n    ACTOR_JOB_STATUSES.TIMED_OUT,\n    ACTOR_JOB_STATUSES.ABORTED\n];\nvar WORKER_SERVICE_TYPES = {\n    CRAWLING: \"crawling\",\n    ACTOR: \"actor\"\n};\nvar META_ORIGINS = {\n    DEVELOPMENT: \"DEVELOPMENT\",\n    // Job started from Developer console in Source section of actor\n    WEB: \"WEB\",\n    // Job started from other place on the website (either console or task detail page)\n    API: \"API\",\n    // Job started through API\n    SCHEDULER: \"SCHEDULER\",\n    // Job started through Scheduler\n    TEST: \"TEST\",\n    // Job started through test actor page\n    WEBHOOK: \"WEBHOOK\",\n    // Job started by the webhook\n    ACTOR: \"ACTOR\",\n    // Job started by another actor run\n    CLI: \"CLI\",\n    // Job started by apify CLI\n    STANDBY: \"STANDBY\"\n};\nvar DOCKER_LABELS = {\n    ACTOR_BUILD_ID: \"com.apify.actBuildId\",\n    ACTOR_RUN_ID: \"com.apify.actRunId\",\n    // Kept for backwards compatibility, will be removed soon (TODO: remove old usages!)\n    /** @deprecated Use ACTOR_BUILD_ID instead! */ ACT_BUILD_ID: \"com.apify.actBuildId\",\n    /** @deprecated Use ACTOR_RUN_ID instead! */ ACT_RUN_ID: \"com.apify.actRunId\"\n};\nvar ACTOR_TYPES = {\n    ACT: \"acts\",\n    CRAWLER: \"crawlers\"\n};\nvar ME_USER_NAME_PLACEHOLDER = \"me\";\nvar ANONYMOUS_USERNAME = \"anonymous\";\nvar USERNAME = {\n    MIN_LENGTH: 3,\n    MAX_LENGTH: 30,\n    // Regexes matching a potentially allowed username. The numbers must match MIN and MAX!\n    // Note that username must also pass isForbiddenUser() test to be allowed!\n    REGEX: /^[a-z0-9_.-]{3,30}$/i,\n    RESTRICTED_REGEX: /^(?!.*apify)[a-z0-9_.-]{3,30}$/i\n};\nvar EMAIL = {\n    MAX_LENGTH: 254,\n    // see https://www.rfc-editor.org/errata_search.php?rfc=3696&eid=1690\n    REGEX: EMAIL_REGEX\n};\nvar PROFILE_NAME = {\n    MAX_LENGTH: 50,\n    REGEX: /^(?!.*:\\/\\/)[^@><]*$/\n};\nvar DNS_SAFE_NAME_MAX_LENGTH = 63;\nvar ACTOR_NAME = {\n    MIN_LENGTH: 3,\n    MAX_LENGTH: DNS_SAFE_NAME_MAX_LENGTH,\n    // DNS-safe string length\n    REGEX: DNS_SAFE_NAME_REGEX\n};\nvar SHORT_CRAWLER_ID_LENGTH = 5;\nvar BUILD_TAG_LATEST = \"latest\";\nvar ACTOR_RESTART_ON_ERROR = {\n    MAX_RESTARTS: 3,\n    // This needs to be low enough so that it only covers restart loops, rather than e.g.\n    // errors during crawling of large lists of URLs\n    INTERVAL_MILLIS: 1 * 60 * 1e3\n};\nvar ACT_RESTART_ON_ERROR = ACTOR_RESTART_ON_ERROR;\nvar ACT_JOB_TYPES = ACTOR_JOB_TYPES;\nvar ACT_SOURCE_TYPES = ACTOR_SOURCE_TYPES;\nvar ACT_JOB_STATUSES = ACTOR_JOB_STATUSES;\nvar ACT_JOB_TERMINAL_STATUSES = ACTOR_JOB_TERMINAL_STATUSES;\nvar ACT_TYPES = ACTOR_TYPES;\nvar COMPUTE_UNIT_MB = 1024;\nvar COMPUTE_UNIT_MILLIS = 60 * 60 * 1e3;\nvar ACTOR_LIMITS = {\n    // The actualy used limit is taken from private package @apify-packages/consts\n    BUILD_DEFAULT_MEMORY_MBYTES: 4096,\n    // Maximum duration of build in seconds.\n    BUILD_TIMEOUT_SECS: 1800,\n    // For each build or run container, set disk quota based on memory size\n    RUN_DISK_TO_MEMORY_SIZE_COEFF: 2,\n    // For each build or run container, set CPU cores based on memory size\n    RUN_MEMORY_MBYTES_PER_CPU_CORE: 4096,\n    // The default limit of memory for all running Actor jobs for free accounts.\n    FREE_ACCOUNT_MAX_MEMORY_MBYTES: 8192,\n    // The default limit of memory for all running Actor jobs for paid accounts.\n    PAID_ACCOUNT_MAX_MEMORY_MBYTES: 65536,\n    // Minimum and maximum memory for a single act run.\n    MIN_RUN_MEMORY_MBYTES: 128,\n    MAX_RUN_MEMORY_MBYTES: 32768,\n    // Maximum size of actor input schema.\n    INPUT_SCHEMA_MAX_BYTES: 500 * 1024,\n    // Max length of run/build log in number of characters\n    LOG_MAX_CHARS: 10 * 1024 * 1024\n};\nvar DEFAULT_PLATFORM_LIMITS = {\n    // Maximum number of actors per user\n    MAX_ACTORS_PER_USER: 500,\n    // Maximum number of tasks per user\n    MAX_TASKS_PER_USER: 5e3,\n    // Maximum number of schedules per user\n    MAX_SCHEDULES_PER_USER: 100,\n    // Maximum number of webhooks per user\n    MAX_WEBHOOKS_PER_USER: 100,\n    // Maximum number of concurrent actor runs per user for free accounts.\n    FREE_ACCOUNT_MAX_CONCURRENT_ACTOR_RUNS_PER_USER: 25,\n    // Maximum number of concurrent actor runs per user for paid accounts.\n    PAID_ACCOUNT_MAX_CONCURRENT_ACTOR_RUNS_PER_USER: 250,\n    // Maximum number of actors per scheduler\n    MAX_ACTORS_PER_SCHEDULER: 10,\n    // Maximum number of tasks per scheduler\n    MAX_TASKS_PER_SCHEDULER: 10\n};\nvar REQUEST_QUEUE_HEAD_MAX_LIMIT = 1e3;\nvar APIFY_ENV_VARS = {\n    API_BASE_URL: \"APIFY_API_BASE_URL\",\n    API_PUBLIC_BASE_URL: \"APIFY_API_PUBLIC_BASE_URL\",\n    CHROME_EXECUTABLE_PATH: \"APIFY_CHROME_EXECUTABLE_PATH\",\n    DEDICATED_CPUS: \"APIFY_DEDICATED_CPUS\",\n    DISABLE_OUTDATED_WARNING: \"APIFY_DISABLE_OUTDATED_WARNING\",\n    FACT: \"APIFY_FACT\",\n    HEADLESS: \"APIFY_HEADLESS\",\n    INPUT_SECRETS_PRIVATE_KEY_FILE: \"APIFY_INPUT_SECRETS_PRIVATE_KEY_FILE\",\n    INPUT_SECRETS_PRIVATE_KEY_PASSPHRASE: \"APIFY_INPUT_SECRETS_PRIVATE_KEY_PASSPHRASE\",\n    IS_AT_HOME: \"APIFY_IS_AT_HOME\",\n    LOCAL_STORAGE_DIR: \"APIFY_LOCAL_STORAGE_DIR\",\n    LOG_FORMAT: \"APIFY_LOG_FORMAT\",\n    LOG_LEVEL: \"APIFY_LOG_LEVEL\",\n    METAMORPH_AFTER_SLEEP_MILLIS: \"APIFY_METAMORPH_AFTER_SLEEP_MILLIS\",\n    META_ORIGIN: \"APIFY_META_ORIGIN\",\n    PERSIST_STATE_INTERVAL_MILLIS: \"APIFY_PERSIST_STATE_INTERVAL_MILLIS\",\n    PROXY_HOSTNAME: \"APIFY_PROXY_HOSTNAME\",\n    PROXY_PASSWORD: \"APIFY_PROXY_PASSWORD\",\n    PROXY_PORT: \"APIFY_PROXY_PORT\",\n    PROXY_STATUS_URL: \"APIFY_PROXY_STATUS_URL\",\n    PURGE_ON_START: \"APIFY_PURGE_ON_START\",\n    SDK_LATEST_VERSION: \"APIFY_SDK_LATEST_VERSION\",\n    SYSTEM_INFO_INTERVAL_MILLIS: \"APIFY_SYSTEM_INFO_INTERVAL_MILLIS\",\n    TOKEN: \"APIFY_TOKEN\",\n    USER_ID: \"APIFY_USER_ID\",\n    USER_IS_PAYING: \"APIFY_USER_IS_PAYING\",\n    USER_PRICING_TIER: \"APIFY_USER_PRICING_TIER\",\n    WORKFLOW_KEY: \"APIFY_WORKFLOW_KEY\",\n    XVFB: \"APIFY_XVFB\",\n    // Replaced by ACTOR_ENV_VARS, kept for backward compatibility:\n    ACTOR_BUILD_ID: \"APIFY_ACTOR_BUILD_ID\",\n    ACTOR_BUILD_NUMBER: \"APIFY_ACTOR_BUILD_NUMBER\",\n    ACTOR_EVENTS_WS_URL: \"APIFY_ACTOR_EVENTS_WS_URL\",\n    ACTOR_ID: \"APIFY_ACTOR_ID\",\n    ACTOR_MAX_PAID_DATASET_ITEMS: \"ACTOR_MAX_PAID_DATASET_ITEMS\",\n    ACTOR_RUN_ID: \"APIFY_ACTOR_RUN_ID\",\n    ACTOR_TASK_ID: \"APIFY_ACTOR_TASK_ID\",\n    CONTAINER_PORT: \"APIFY_CONTAINER_PORT\",\n    CONTAINER_URL: \"APIFY_CONTAINER_URL\",\n    DEFAULT_DATASET_ID: \"APIFY_DEFAULT_DATASET_ID\",\n    DEFAULT_KEY_VALUE_STORE_ID: \"APIFY_DEFAULT_KEY_VALUE_STORE_ID\",\n    DEFAULT_REQUEST_QUEUE_ID: \"APIFY_DEFAULT_REQUEST_QUEUE_ID\",\n    INPUT_KEY: \"APIFY_INPUT_KEY\",\n    MEMORY_MBYTES: \"APIFY_MEMORY_MBYTES\",\n    STARTED_AT: \"APIFY_STARTED_AT\",\n    TIMEOUT_AT: \"APIFY_TIMEOUT_AT\",\n    // Deprecated, keep them for backward compatibility:\n    ACT_ID: \"APIFY_ACT_ID\",\n    ACT_RUN_ID: \"APIFY_ACT_RUN_ID\"\n};\nvar ENV_VARS = APIFY_ENV_VARS;\nvar ACTOR_ENV_VARS = {\n    BUILD_ID: \"ACTOR_BUILD_ID\",\n    BUILD_NUMBER: \"ACTOR_BUILD_NUMBER\",\n    BUILD_TAGS: \"ACTOR_BUILD_TAGS\",\n    DEFAULT_DATASET_ID: \"ACTOR_DEFAULT_DATASET_ID\",\n    DEFAULT_KEY_VALUE_STORE_ID: \"ACTOR_DEFAULT_KEY_VALUE_STORE_ID\",\n    DEFAULT_REQUEST_QUEUE_ID: \"ACTOR_DEFAULT_REQUEST_QUEUE_ID\",\n    EVENTS_WEBSOCKET_URL: \"ACTOR_EVENTS_WEBSOCKET_URL\",\n    FULL_NAME: \"ACTOR_FULL_NAME\",\n    ID: \"ACTOR_ID\",\n    INPUT_KEY: \"ACTOR_INPUT_KEY\",\n    MAX_PAID_DATASET_ITEMS: \"ACTOR_MAX_PAID_DATASET_ITEMS\",\n    MAX_TOTAL_CHARGE_USD: \"ACTOR_MAX_TOTAL_CHARGE_USD\",\n    MEMORY_MBYTES: \"ACTOR_MEMORY_MBYTES\",\n    RUN_ID: \"ACTOR_RUN_ID\",\n    STANDBY_PORT: \"ACTOR_STANDBY_PORT\",\n    STANDBY_URL: \"ACTOR_STANDBY_URL\",\n    STARTED_AT: \"ACTOR_STARTED_AT\",\n    TASK_ID: \"ACTOR_TASK_ID\",\n    TIMEOUT_AT: \"ACTOR_TIMEOUT_AT\",\n    WEB_SERVER_PORT: \"ACTOR_WEB_SERVER_PORT\",\n    WEB_SERVER_URL: \"ACTOR_WEB_SERVER_URL\"\n};\nvar INTEGER_ENV_VARS = [\n    // Actor env vars\n    ACTOR_ENV_VARS.MAX_PAID_DATASET_ITEMS,\n    ACTOR_ENV_VARS.MEMORY_MBYTES,\n    ACTOR_ENV_VARS.STANDBY_PORT,\n    ACTOR_ENV_VARS.WEB_SERVER_PORT,\n    // Apify env vars\n    APIFY_ENV_VARS.ACTOR_MAX_PAID_DATASET_ITEMS,\n    APIFY_ENV_VARS.CONTAINER_PORT,\n    APIFY_ENV_VARS.DEDICATED_CPUS,\n    APIFY_ENV_VARS.MEMORY_MBYTES,\n    APIFY_ENV_VARS.METAMORPH_AFTER_SLEEP_MILLIS,\n    APIFY_ENV_VARS.PERSIST_STATE_INTERVAL_MILLIS,\n    APIFY_ENV_VARS.PROXY_PORT,\n    APIFY_ENV_VARS.SYSTEM_INFO_INTERVAL_MILLIS\n];\nvar COMMA_SEPARATED_LIST_ENV_VARS = [\n    ACTOR_ENV_VARS.BUILD_TAGS\n];\nvar ACTOR_BUILD_ARGS = {\n    ACTOR_PATH_IN_DOCKER_CONTEXT: \"ACTOR_PATH_IN_DOCKER_CONTEXT\"\n};\nvar DEFAULT_CONTAINER_PORT = 4321;\nvar DEFAULT_ACTOR_STANDBY_PORT = DEFAULT_CONTAINER_PORT;\nvar LOCAL_STORAGE_SUBDIRS = {\n    datasets: \"datasets\",\n    keyValueStores: \"key_value_stores\",\n    requestQueues: \"request_queues\"\n};\nvar LOCAL_ACTOR_ENV_VARS = {\n    [ACTOR_ENV_VARS.STANDBY_PORT]: DEFAULT_CONTAINER_PORT.toString(),\n    [ACTOR_ENV_VARS.DEFAULT_DATASET_ID]: \"default\",\n    [ACTOR_ENV_VARS.DEFAULT_KEY_VALUE_STORE_ID]: \"default\",\n    [ACTOR_ENV_VARS.DEFAULT_REQUEST_QUEUE_ID]: \"default\",\n    [ACTOR_ENV_VARS.WEB_SERVER_PORT]: DEFAULT_CONTAINER_PORT.toString(),\n    [ACTOR_ENV_VARS.WEB_SERVER_URL]: `http://localhost:${DEFAULT_CONTAINER_PORT}`\n};\nvar LOCAL_APIFY_ENV_VARS = {\n    [APIFY_ENV_VARS.CONTAINER_PORT]: LOCAL_ACTOR_ENV_VARS.ACTOR_WEB_SERVER_PORT,\n    [APIFY_ENV_VARS.CONTAINER_URL]: LOCAL_ACTOR_ENV_VARS.ACTOR_WEB_SERVER_URL,\n    [APIFY_ENV_VARS.DEFAULT_DATASET_ID]: LOCAL_ACTOR_ENV_VARS.ACTOR_DEFAULT_DATASET_ID,\n    [APIFY_ENV_VARS.DEFAULT_KEY_VALUE_STORE_ID]: LOCAL_ACTOR_ENV_VARS.ACTOR_DEFAULT_KEY_VALUE_STORE_ID,\n    [APIFY_ENV_VARS.DEFAULT_REQUEST_QUEUE_ID]: LOCAL_ACTOR_ENV_VARS.ACTOR_DEFAULT_REQUEST_QUEUE_ID,\n    [APIFY_ENV_VARS.PROXY_HOSTNAME]: \"proxy.apify.com\",\n    [APIFY_ENV_VARS.PROXY_PORT]: 8e3.toString()\n};\nvar LOCAL_ENV_VARS = LOCAL_APIFY_ENV_VARS;\nvar KEY_VALUE_STORE_KEYS = {\n    INPUT: \"INPUT\",\n    OUTPUT: \"OUTPUT\"\n};\nvar MAX_PAYLOAD_SIZE_BYTES = 9437184;\nvar ACTOR_CATEGORIES = {\n    AI: \"AI\",\n    AGENTS: \"Agents\",\n    AUTOMATION: \"Automation\",\n    BUSINESS: \"Business\",\n    COVID_19: \"Covid-19\",\n    DEVELOPER_EXAMPLES: \"Developer examples\",\n    DEVELOPER_TOOLS: \"Developer tools\",\n    ECOMMERCE: \"E-commerce\",\n    FOR_CREATORS: \"For creators\",\n    GAMES: \"Games\",\n    JOBS: \"Jobs\",\n    LEAD_GENERATION: \"Lead generation\",\n    MARKETING: \"Marketing\",\n    NEWS: \"News\",\n    SEO_TOOLS: \"SEO tools\",\n    SOCIAL_MEDIA: \"Social media\",\n    TRAVEL: \"Travel\",\n    VIDEOS: \"Videos\",\n    REAL_ESTATE: \"Real estate\",\n    SPORTS: \"Sports\",\n    EDUCATION: \"Education\",\n    INTEGRATIONS: \"Integrations\",\n    OTHER: \"Other\",\n    OPEN_SOURCE: \"Open source\",\n    MCP_SERVERS: \"MCP servers\"\n};\nvar ALL_ACTOR_CATEGORIES = {\n    ...ACTOR_CATEGORIES\n};\nvar VERSION_INT_MAJOR_BASE = 1e7;\nvar VERSION_INT_MINOR_BASE = 1e5;\nvar USER_BASIC_TEXT_XSS_OPTIONS = {\n    whiteList: {\n        a: [\n            \"href\",\n            \"title\",\n            \"target\"\n        ],\n        code: [],\n        strong: [],\n        b: [],\n        br: [],\n        ul: [],\n        li: [],\n        ol: [],\n        i: [],\n        u: [],\n        p: []\n    }\n};\nvar WEBHOOK_EVENT_TYPES = {\n    ACTOR_RUN_CREATED: \"ACTOR.RUN.CREATED\",\n    ACTOR_RUN_SUCCEEDED: \"ACTOR.RUN.SUCCEEDED\",\n    ACTOR_RUN_FAILED: \"ACTOR.RUN.FAILED\",\n    ACTOR_RUN_TIMED_OUT: \"ACTOR.RUN.TIMED_OUT\",\n    ACTOR_RUN_ABORTED: \"ACTOR.RUN.ABORTED\",\n    ACTOR_RUN_RESURRECTED: \"ACTOR.RUN.RESURRECTED\",\n    ACTOR_BUILD_CREATED: \"ACTOR.BUILD.CREATED\",\n    ACTOR_BUILD_SUCCEEDED: \"ACTOR.BUILD.SUCCEEDED\",\n    ACTOR_BUILD_FAILED: \"ACTOR.BUILD.FAILED\",\n    ACTOR_BUILD_TIMED_OUT: \"ACTOR.BUILD.TIMED_OUT\",\n    ACTOR_BUILD_ABORTED: \"ACTOR.BUILD.ABORTED\",\n    TEST: \"TEST\"\n};\nvar WEBHOOK_EVENT_TYPE_GROUPS = {\n    ACTOR_RUN: [\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_CREATED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_SUCCEEDED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_FAILED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_TIMED_OUT,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_ABORTED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_RESURRECTED\n    ],\n    ACTOR_BUILD: [\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_CREATED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_SUCCEEDED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_FAILED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_TIMED_OUT,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_ABORTED\n    ],\n    // If one of these occurs then we can be sure that none other can occur for the same triggerer.\n    ACTOR_RUN_TERMINAL: [\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_SUCCEEDED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_FAILED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_TIMED_OUT,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_ABORTED\n    ],\n    ACTOR_BUILD_TERMINAL: [\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_SUCCEEDED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_FAILED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_TIMED_OUT,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_ABORTED\n    ]\n};\nvar WEBHOOK_DEFAULT_PAYLOAD_TEMPLATE = `{\n    \"userId\": {{userId}},\n    \"createdAt\": {{createdAt}},\n    \"eventType\": {{eventType}},\n    \"eventData\": {{eventData}},\n    \"resource\": {{resource}}\n}`;\nvar WEBHOOK_ALLOWED_PAYLOAD_VARIABLES = /* @__PURE__ */ new Set([\n    \"userId\",\n    \"createdAt\",\n    \"eventType\",\n    \"eventData\",\n    \"resource\"\n]);\nvar MAX_MULTIFILE_BYTES = 3 * 1024 ** 2;\nvar SOURCE_FILE_FORMATS = {\n    TEXT: \"TEXT\",\n    BASE64: \"BASE64\"\n};\nvar PROJECT_STATUSES = {\n    REQUEST: \"REQUEST\",\n    SPECIFICATION: \"SPECIFICATION\",\n    OFFERS: \"OFFERS\",\n    DEPOSIT: \"DEPOSIT\",\n    DEPOSIT_PAID: \"DEPOSIT_PAID\",\n    NEW: \"NEW\",\n    IN_PROGRESS: \"IN_PROGRESS\",\n    QA: \"QA\",\n    CUSTOMER_QA: \"CUSTOMER_QA\",\n    READY_FOR_INVOICE: \"READY_FOR_INVOICE\",\n    INVOICED: \"INVOICED\",\n    PAID: \"PAID\",\n    DELIVERED: \"DELIVERED\",\n    CLOSED: \"CLOSED\",\n    FINISHED: \"FINISHED\"\n};\nvar FINISHED_PROJECT_STATUSES = [\n    PROJECT_STATUSES.READY_FOR_INVOICE,\n    PROJECT_STATUSES.INVOICED,\n    PROJECT_STATUSES.PAID,\n    PROJECT_STATUSES.DELIVERED,\n    PROJECT_STATUSES.FINISHED\n];\nvar MARKETPLACE_USER_ROLES = {\n    DEVELOPER: \"DEVELOPER\",\n    DATA_EXPERT: \"DATA_EXPERT\",\n    CUSTOMER: \"CUSTOMER\"\n};\nvar USER_PERSONA_TYPES = {\n    DEVELOPER: \"DEVELOPER\",\n    USER: \"USER\"\n};\nvar GIT_MAIN_BRANCH = \"main\";\nvar REQUEST_QUEUE_MAX_REQUESTS_PER_BATCH_OPERATION = 25;\nvar ISSUES_STATUS_TYPES = {\n    OPEN: \"OPEN\",\n    CLOSED: \"CLOSED\"\n};\nvar ISSUES_STATUS_ALL = \"ALL\";\nvar STORAGE_GENERAL_ACCESS = {\n    /** Respect the user setting of the storage owner (default behavior). */ FOLLOW_USER_SETTING: \"FOLLOW_USER_SETTING\",\n    /** Only signed-in users with explicit access can read this storage. */ RESTRICTED: \"RESTRICTED\",\n    /** Anyone with a link, or the unique storage ID, can read the storage. */ ANYONE_WITH_ID_CAN_READ: \"ANYONE_WITH_ID_CAN_READ\",\n    /** Anyone with a link, the unique storage ID, or the storage name, can read the storage. */ ANYONE_WITH_NAME_CAN_READ: \"ANYONE_WITH_NAME_CAN_READ\"\n};\nvar RUN_GENERAL_ACCESS = {\n    /** Respect the user setting of the run owner (default behavior). */ FOLLOW_USER_SETTING: \"FOLLOW_USER_SETTING\",\n    /** Only signed-in users with explicit access can read this run. */ RESTRICTED: \"RESTRICTED\",\n    /** Anyone with a link, or the unique run ID, can read the run. */ ANYONE_WITH_ID_CAN_READ: \"ANYONE_WITH_ID_CAN_READ\"\n};\nvar ACTOR_PERMISSION_LEVEL = {\n    /** Full permission Actors have access to all user data in the account. */ FULL_PERMISSIONS: \"FULL_PERMISSIONS\",\n    /**\n   * Limited permission Actors have access only to specific resources:\n   * - default storages\n   * - storages provided via input\n   * - the current run\n   * - ...\n   *\n   * Broadly speaking, limited permission Actors cannot access any account data not related to the current run.\n   * For details refer to the Apify documentation.\n   */ LIMITED_PERMISSIONS: \"LIMITED_PERMISSIONS\"\n};\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0); //# sourceMappingURL=index.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@apify/consts/cjs/index.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@apify/log/cjs/index.cjs":
/*!***********************************************!*\
  !*** ./node_modules/@apify/log/cjs/index.cjs ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value\n    }) : obj[key] = value;\nvar __name = (target, value)=>__defProp(target, \"name\", {\n        value,\n        configurable: true\n    });\nvar __export = (target, all)=>{\n    for(var name in all)__defProp(target, name, {\n        get: all[name],\n        enumerable: true\n    });\n};\nvar __copyProps = (to, from, except, desc)=>{\n    if (from && typeof from === \"object\" || typeof from === \"function\") {\n        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n            get: ()=>from[key],\n            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n        });\n    }\n    return to;\n};\nvar __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM\n    // file that has been converted to a CommonJS file using a Babel-\n    // compatible transform (i.e. \"__esModule\" has not been set), then set\n    // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n    isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", {\n        value: mod,\n        enumerable: true\n    }) : target, mod));\nvar __toCommonJS = (mod)=>__copyProps(__defProp({}, \"__esModule\", {\n        value: true\n    }), mod);\nvar __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n    IS_APIFY_LOGGER_EXCEPTION: ()=>IS_APIFY_LOGGER_EXCEPTION,\n    LEVELS: ()=>LEVELS,\n    LEVEL_TO_STRING: ()=>LEVEL_TO_STRING,\n    Log: ()=>Log,\n    LogFormat: ()=>LogFormat,\n    LogLevel: ()=>LogLevel,\n    Logger: ()=>Logger,\n    LoggerJson: ()=>LoggerJson,\n    LoggerText: ()=>LoggerText,\n    PREFIX_DELIMITER: ()=>PREFIX_DELIMITER,\n    default: ()=>index_default,\n    getFormatFromEnv: ()=>getFormatFromEnv,\n    getLevelFromEnv: ()=>getLevelFromEnv,\n    limitDepth: ()=>limitDepth,\n    truncate: ()=>truncate\n});\nmodule.exports = __toCommonJS(index_exports);\n// src/log_consts.ts\nvar LogLevel = /* @__PURE__ */ ((LogLevel2)=>{\n    LogLevel2[LogLevel2[\"OFF\"] = 0] = \"OFF\";\n    LogLevel2[LogLevel2[\"ERROR\"] = 1] = \"ERROR\";\n    LogLevel2[LogLevel2[\"SOFT_FAIL\"] = 2] = \"SOFT_FAIL\";\n    LogLevel2[LogLevel2[\"WARNING\"] = 3] = \"WARNING\";\n    LogLevel2[LogLevel2[\"INFO\"] = 4] = \"INFO\";\n    LogLevel2[LogLevel2[\"DEBUG\"] = 5] = \"DEBUG\";\n    LogLevel2[LogLevel2[\"PERF\"] = 6] = \"PERF\";\n    return LogLevel2;\n})(LogLevel || {});\nvar LogFormat = /* @__PURE__ */ ((LogFormat2)=>{\n    LogFormat2[\"JSON\"] = \"JSON\";\n    LogFormat2[\"TEXT\"] = \"TEXT\";\n    return LogFormat2;\n})(LogFormat || {});\nvar PREFIX_DELIMITER = \":\";\nvar LEVELS = LogLevel;\nvar LEVEL_TO_STRING = Object.keys(LogLevel).filter((x)=>Number.isNaN(+x));\nvar IS_APIFY_LOGGER_EXCEPTION = Symbol(\"apify.processed_error\");\n// src/log_helpers.ts\nvar import_consts = __webpack_require__(/*! @apify/consts */ \"(rsc)/./node_modules/@apify/consts/cjs/index.cjs\");\nfunction truncate(str, maxLength, suffix = \"...[truncated]\") {\n    maxLength = Math.floor(maxLength);\n    if (suffix.length > maxLength) {\n        throw new Error(\"suffix string cannot be longer than maxLength\");\n    }\n    if (typeof str === \"string\" && str.length > maxLength) {\n        str = str.substr(0, maxLength - suffix.length) + suffix;\n    }\n    return str;\n}\n__name(truncate, \"truncate\");\nfunction getLevelFromEnv() {\n    const envVar = process.env[import_consts.APIFY_ENV_VARS.LOG_LEVEL];\n    if (!envVar) return 4 /* INFO */ ;\n    if (Number.isFinite(+envVar)) return +envVar;\n    if (LogLevel[envVar]) return LogLevel[envVar];\n    return +envVar;\n}\n__name(getLevelFromEnv, \"getLevelFromEnv\");\nfunction getFormatFromEnv() {\n    const envVar = process.env[import_consts.APIFY_ENV_VARS.LOG_FORMAT] || \"TEXT\" /* TEXT */ ;\n    switch(envVar.toLowerCase()){\n        case \"JSON\" /* JSON */ .toLowerCase():\n            return \"JSON\" /* JSON */ ;\n        case \"TEXT\" /* TEXT */ .toLowerCase():\n            return \"TEXT\" /* TEXT */ ;\n        default:\n            console.warn(`Unknown value for environment variable ${import_consts.APIFY_ENV_VARS.LOG_FORMAT}: ${envVar}`);\n            return \"TEXT\" /* TEXT */ ;\n    }\n}\n__name(getFormatFromEnv, \"getFormatFromEnv\");\nfunction limitDepth(record, depth, maxStringLength) {\n    if (typeof record === \"string\") {\n        return maxStringLength && record.length > maxStringLength ? truncate(record, maxStringLength) : record;\n    }\n    if ([\n        \"number\",\n        \"boolean\",\n        \"symbol\",\n        \"bigint\"\n    ].includes(typeof record) || record == null || record instanceof Date) {\n        return record;\n    }\n    if (record instanceof Error) {\n        const { name, message, stack, cause, ...rest } = record;\n        record = {\n            name,\n            message,\n            stack,\n            cause,\n            ...rest,\n            [IS_APIFY_LOGGER_EXCEPTION]: true\n        };\n    }\n    const nextCall = /* @__PURE__ */ __name((rec)=>limitDepth(rec, depth - 1, maxStringLength), \"nextCall\");\n    if (Array.isArray(record)) {\n        return depth ? record.map(nextCall) : \"[array]\";\n    }\n    if (typeof record === \"object\" && record !== null) {\n        const mapObject = /* @__PURE__ */ __name((obj)=>{\n            const res = {};\n            Reflect.ownKeys(obj).forEach((key)=>{\n                res[key] = nextCall(obj[key]);\n            });\n            return res;\n        }, \"mapObject\");\n        return depth ? mapObject(record) : \"[object]\";\n    }\n    if (typeof record === \"function\") {\n        return \"[function]\";\n    }\n    console.log(`WARNING: Object cannot be logged: ${record}`);\n    return void 0;\n}\n__name(limitDepth, \"limitDepth\");\n// src/logger.ts\nvar import_node_events = __webpack_require__(/*! events */ \"events\");\nvar _Logger = class _Logger extends import_node_events.EventEmitter {\n    constructor(options){\n        super();\n        this.options = options;\n    }\n    setOptions(options) {\n        this.options = {\n            ...this.options,\n            ...options\n        };\n    }\n    getOptions() {\n        return this.options;\n    }\n    _outputWithConsole(level, line) {\n        switch(level){\n            case 1 /* ERROR */ :\n                console.error(line);\n                break;\n            case 3 /* WARNING */ :\n                console.warn(line);\n                break;\n            case 5 /* DEBUG */ :\n                console.debug(line);\n                break;\n            default:\n                console.log(line);\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _log(level, message, data, exception, opts = {}) {\n        throw new Error(\"log() method must be implemented!\");\n    }\n    log(level, message, ...args) {\n        const line = this._log(level, message, ...args);\n        this.emit(\"line\", line);\n    }\n};\n__name(_Logger, \"Logger\");\nvar Logger = _Logger;\n// src/logger_json.ts\nvar DEFAULT_OPTIONS = {\n    skipLevelInfo: false,\n    skipTime: false\n};\nvar _LoggerJson = class _LoggerJson extends Logger {\n    constructor(options = {}){\n        super({\n            ...DEFAULT_OPTIONS,\n            ...options\n        });\n    }\n    _log(level, message, data, exception, opts = {}) {\n        const { prefix, suffix } = opts;\n        if (exception) data = {\n            ...data,\n            exception\n        };\n        if (prefix) message = `${prefix}${PREFIX_DELIMITER} ${message}`;\n        if (suffix) message = `${message} ${suffix}`;\n        const rec = {\n            time: !this.options.skipTime ? /* @__PURE__ */ new Date() : void 0,\n            level: this.options.skipLevelInfo && level === 4 /* INFO */  ? void 0 : LogLevel[level],\n            msg: message,\n            ...data\n        };\n        const line = JSON.stringify(rec);\n        this._outputWithConsole(level, line);\n        return line;\n    }\n};\n__name(_LoggerJson, \"LoggerJson\");\nvar LoggerJson = _LoggerJson;\n// src/logger_text.ts\nvar import_ansi_colors2 = __toESM(__webpack_require__(/*! ansi-colors */ \"(rsc)/./node_modules/ansi-colors/index.js\"));\n// src/node_internals.ts\nvar import_ansi_colors = __toESM(__webpack_require__(/*! ansi-colors */ \"(rsc)/./node_modules/ansi-colors/index.js\"));\nfunction identicalSequenceRange(a, b) {\n    for(let i = 0; i < a.length - 3; i++){\n        const pos = b.indexOf(a[i]);\n        if (pos !== -1) {\n            const rest = b.length - pos;\n            if (rest > 3) {\n                let len = 1;\n                const maxLen = Math.min(a.length - i, rest);\n                while(maxLen > len && a[i + len] === b[pos + len]){\n                    len++;\n                }\n                if (len > 3) {\n                    return {\n                        len,\n                        offset: i\n                    };\n                }\n            }\n        }\n    }\n    return {\n        len: 0,\n        offset: 0\n    };\n}\n__name(identicalSequenceRange, \"identicalSequenceRange\");\nfunction getStackString(error) {\n    return error.stack ? String(error.stack) : Error.prototype.toString.call(error);\n}\n__name(getStackString, \"getStackString\");\nfunction getStackFrames(err, stack) {\n    const frames = stack.split(\"\\n\");\n    let cause;\n    try {\n        ({ cause } = err);\n    } catch  {}\n    if (cause != null && typeof cause === \"object\" && IS_APIFY_LOGGER_EXCEPTION in cause) {\n        const causeStack = getStackString(cause);\n        const causeStackStart = causeStack.indexOf(\"\\n    at\");\n        if (causeStackStart !== -1) {\n            const causeFrames = causeStack.slice(causeStackStart + 1).split(\"\\n\");\n            const { len, offset } = identicalSequenceRange(frames, causeFrames);\n            if (len > 0) {\n                const skipped = len - 2;\n                const msg = `    ... ${skipped} lines matching cause stack trace ...`;\n                frames.splice(offset + 1, skipped, import_ansi_colors.default.grey(msg));\n            }\n        }\n    }\n    return frames;\n}\n__name(getStackFrames, \"getStackFrames\");\n// src/logger_text.ts\nvar SHORTEN_LEVELS = {\n    SOFT_FAIL: \"SFAIL\",\n    WARNING: \"WARN\"\n};\nvar LEVEL_TO_COLOR = {\n    [1 /* ERROR */ ]: \"red\",\n    [2 /* SOFT_FAIL */ ]: \"red\",\n    [3 /* WARNING */ ]: \"yellow\",\n    [4 /* INFO */ ]: \"green\",\n    [5 /* DEBUG */ ]: \"blue\",\n    [6 /* PERF */ ]: \"magenta\"\n};\nvar SHORTENED_LOG_LEVELS = LEVEL_TO_STRING.map((level)=>SHORTEN_LEVELS[level] || level);\nvar MAX_LEVEL_LENGTH_SPACES = Math.max(...SHORTENED_LOG_LEVELS.map((l)=>l.length));\nvar getLevelIndent = /* @__PURE__ */ __name((level)=>{\n    let spaces = \"\";\n    for(let i = 0; i < MAX_LEVEL_LENGTH_SPACES - level.length; i++)spaces += \" \";\n    return spaces;\n}, \"getLevelIndent\");\nvar DEFAULT_OPTIONS2 = {\n    skipTime: true\n};\nvar _LoggerText = class _LoggerText extends Logger {\n    constructor(options = {}){\n        super({\n            ...DEFAULT_OPTIONS2,\n            ...options\n        });\n    }\n    _log(level, message, data, exception, opts = {}) {\n        let { prefix, suffix } = opts;\n        let maybeDate = \"\";\n        if (!this.options.skipTime) {\n            maybeDate = `${/* @__PURE__ */ new Date().toISOString().replace(\"Z\", \"\").replace(\"T\", \" \")} `;\n        }\n        const errStack = exception ? this._parseException(exception) : \"\";\n        const color = LEVEL_TO_COLOR[level];\n        const levelStr = SHORTENED_LOG_LEVELS[level];\n        const levelIndent = getLevelIndent(levelStr);\n        const dataStr = !data ? \"\" : ` ${JSON.stringify(data)}`;\n        prefix = prefix ? ` ${prefix}${PREFIX_DELIMITER}` : \"\";\n        suffix = suffix ? ` ${suffix}` : \"\";\n        const line = `${import_ansi_colors2.default.gray(maybeDate)}${import_ansi_colors2.default[color](levelStr)}${levelIndent}${import_ansi_colors2.default.yellow(prefix)} ${message || \"\"}${import_ansi_colors2.default.gray(dataStr)}${import_ansi_colors2.default.yellow(suffix)}${errStack}`;\n        this._outputWithConsole(level, line);\n        return line;\n    }\n    _parseException(exception, indentLevel = 1) {\n        if ([\n            \"string\",\n            \"boolean\",\n            \"number\",\n            \"undefined\",\n            \"bigint\"\n        ].includes(typeof exception)) {\n            return `\n${exception}`;\n        }\n        if (exception === null) {\n            return \"\\nnull\";\n        }\n        if (typeof exception === \"symbol\") {\n            return `\n${exception.toString()}`;\n        }\n        if (typeof exception === \"object\" && IS_APIFY_LOGGER_EXCEPTION in exception) {\n            return this._parseLoggerException(exception, indentLevel);\n        }\n        return `\n${JSON.stringify(exception, null, 2)}`;\n    }\n    _parseLoggerException(exception, indentLevel = 1) {\n        const errDetails = [];\n        if (exception.type) {\n            errDetails.push(`type=${exception.type}`);\n        }\n        if (exception.details) {\n            Object.entries(exception.details).map(([key, val])=>errDetails.push(`${key}=${val}`));\n        }\n        const errorString = exception.stack || exception.reason || exception.message;\n        const isStack = errorString === exception.stack;\n        const errorLines = getStackFrames(exception, errorString);\n        if (isStack) {\n            errorLines[0] = exception.message || errorLines[0];\n        }\n        if (errDetails.length) {\n            errorLines[0] += import_ansi_colors2.default.gray(`(details: ${errDetails.join(\", \")})`);\n        }\n        for(let i = 1; i < errorLines.length; i++){\n            errorLines[i] = import_ansi_colors2.default.gray(errorLines[i]);\n        }\n        if (exception.cause) {\n            const causeString = this._parseException(exception.cause, indentLevel + 1);\n            const causeLines = causeString.trim().split(\"\\n\");\n            errorLines.push(import_ansi_colors2.default.red(`  CAUSE: ${import_ansi_colors2.default.reset(causeLines[0])}`), ...causeLines.slice(1));\n        }\n        return `\n${errorLines.map((line)=>`${\" \".repeat(indentLevel * 2)}${line}`).join(\"\\n\")}`;\n    }\n};\n__name(_LoggerText, \"LoggerText\");\nvar LoggerText = _LoggerText;\n// src/log.ts\nvar getLoggerForFormat = /* @__PURE__ */ __name((format)=>{\n    switch(format){\n        case \"JSON\" /* JSON */ :\n            return new LoggerJson();\n        case \"TEXT\" /* TEXT */ :\n        default:\n            return new LoggerText();\n    }\n}, \"getLoggerForFormat\");\nvar getDefaultOptions = /* @__PURE__ */ __name(()=>({\n        level: getLevelFromEnv(),\n        maxDepth: 4,\n        maxStringLength: 2e3,\n        prefix: null,\n        suffix: null,\n        logger: getLoggerForFormat(getFormatFromEnv()),\n        data: {}\n    }), \"getDefaultOptions\");\nvar _Log = class _Log {\n    constructor(options = {}){\n        /**\n     * Map of available log levels that's useful for easy setting of appropriate log levels.\n     * Each log level is represented internally by a number. Eg. `log.LEVELS.DEBUG === 5`.\n     */ __publicField(this, \"LEVELS\", LogLevel);\n        // for BC\n        __publicField(this, \"options\");\n        __publicField(this, \"warningsOnceLogged\", /* @__PURE__ */ new Set());\n        this.options = {\n            ...getDefaultOptions(),\n            ...options\n        };\n        if (!LogLevel[this.options.level]) throw new Error('Options \"level\" must be one of log.LEVELS enum!');\n        if (typeof this.options.maxDepth !== \"number\") throw new Error('Options \"maxDepth\" must be a number!');\n        if (typeof this.options.maxStringLength !== \"number\") throw new Error('Options \"maxStringLength\" must be a number!');\n        if (this.options.prefix && typeof this.options.prefix !== \"string\") throw new Error('Options \"prefix\" must be a string!');\n        if (this.options.suffix && typeof this.options.suffix !== \"string\") throw new Error('Options \"suffix\" must be a string!');\n        if (typeof this.options.logger !== \"object\") throw new Error('Options \"logger\" must be an object!');\n        if (typeof this.options.data !== \"object\") throw new Error('Options \"data\" must be an object!');\n    }\n    _limitDepth(obj) {\n        return limitDepth(obj, this.options.maxDepth);\n    }\n    /**\n   * Returns the currently selected logging level. This is useful for checking whether a message\n   * will actually be printed to the console before one actually performs a resource intensive operation\n   * to construct the message, such as querying a DB for some metadata that need to be added. If the log\n   * level is not high enough at the moment, it doesn't make sense to execute the query.\n   */ getLevel() {\n        return this.options.level;\n    }\n    /**\n   * Sets the log level to the given value, preventing messages from less important log levels\n   * from being printed to the console. Use in conjunction with the `log.LEVELS` constants such as\n   *\n   * ```\n   * log.setLevel(log.LEVELS.DEBUG);\n   * ```\n   *\n   * Default log level is INFO.\n   */ setLevel(level) {\n        if (!LogLevel[level]) throw new Error('Options \"level\" must be one of log.LEVELS enum!');\n        this.options.level = level;\n    }\n    internal(level, message, data, exception) {\n        if (level > this.options.level) return;\n        data = {\n            ...this.options.data,\n            ...data\n        };\n        data = Reflect.ownKeys(data).length > 0 ? this._limitDepth(data) : void 0;\n        exception = this._limitDepth(exception);\n        this.options.logger.log(level, message, data, exception, {\n            prefix: this.options.prefix,\n            suffix: this.options.suffix\n        });\n    }\n    /**\n   * Configures logger.\n   */ setOptions(options) {\n        this.options = {\n            ...this.options,\n            ...options\n        };\n    }\n    /**\n   * Returns the logger configuration.\n   */ getOptions() {\n        return {\n            ...this.options\n        };\n    }\n    /**\n   * Creates a new instance of logger that inherits settings from a parent logger.\n   */ child(options) {\n        let { prefix } = this.options;\n        if (options.prefix) {\n            prefix = prefix ? `${prefix}${PREFIX_DELIMITER}${options.prefix}` : options.prefix;\n        }\n        const data = options.data ? {\n            ...this.options.data,\n            ...options.data\n        } : this.options.data;\n        const newOptions = {\n            ...this.options,\n            ...options,\n            prefix,\n            data\n        };\n        return new _Log(newOptions);\n    }\n    /**\n   * Logs an `ERROR` message. Use this method to log error messages that are not directly connected\n   * to an exception. For logging exceptions, use the `log.exception` method.\n   */ error(message, data) {\n        this.internal(1 /* ERROR */ , message, data);\n    }\n    /**\n   * Logs an `ERROR` level message with a nicely formatted exception. Note that the exception is the first parameter\n   * here and an additional message is only optional.\n   */ exception(exception, message, data) {\n        this.internal(1 /* ERROR */ , message, data, exception);\n    }\n    softFail(message, data) {\n        this.internal(2 /* SOFT_FAIL */ , message, data);\n    }\n    /**\n   * Logs a `WARNING` level message. Data are stringified and appended to the message.\n   */ warning(message, data) {\n        this.internal(3 /* WARNING */ , message, data);\n    }\n    /**\n   * Logs an `INFO` message. `INFO` is the default log level so info messages will be always logged,\n   * unless the log level is changed. Data are stringified and appended to the message.\n   */ info(message, data) {\n        this.internal(4 /* INFO */ , message, data);\n    }\n    /**\n   * Logs a `DEBUG` message. By default, it will not be written to the console. To see `DEBUG`\n   * messages in the console, set the log level to `DEBUG` either using the `log.setLevel(log.LEVELS.DEBUG)`\n   * method or using the environment variable `APIFY_LOG_LEVEL=DEBUG`. Data are stringified and appended\n   * to the message.\n   */ debug(message, data) {\n        this.internal(5 /* DEBUG */ , message, data);\n    }\n    perf(message, data) {\n        this.internal(6 /* PERF */ , message, data);\n    }\n    /**\n   * Logs a `WARNING` level message only once.\n   */ warningOnce(message) {\n        if (this.warningsOnceLogged.has(message)) return;\n        this.warningsOnceLogged.add(message);\n        this.warning(message);\n    }\n    /**\n   * Logs given message only once as WARNING. It's used to warn user that some feature he is using has been deprecated.\n   */ deprecated(message) {\n        this.warningOnce(message);\n    }\n};\n__name(_Log, \"Log\");\nvar Log = _Log;\n// src/index.ts\nvar log = new Log();\nvar index_default = log;\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0); //# sourceMappingURL=index.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@apify/log/cjs/index.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@apify/utilities/cjs/index.cjs":
/*!*****************************************************!*\
  !*** ./node_modules/@apify/utilities/cjs/index.cjs ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value\n    }) : obj[key] = value;\nvar __name = (target, value)=>__defProp(target, \"name\", {\n        value,\n        configurable: true\n    });\nvar __export = (target, all)=>{\n    for(var name in all)__defProp(target, name, {\n        get: all[name],\n        enumerable: true\n    });\n};\nvar __copyProps = (to, from, except, desc)=>{\n    if (from && typeof from === \"object\" || typeof from === \"function\") {\n        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n            get: ()=>from[key],\n            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n        });\n    }\n    return to;\n};\nvar __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM\n    // file that has been converted to a CommonJS file using a Babel-\n    // compatible transform (i.e. \"__esModule\" has not been set), then set\n    // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n    isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", {\n        value: mod,\n        enumerable: true\n    }) : target, mod));\nvar __toCommonJS = (mod)=>__copyProps(__defProp({}, \"__esModule\", {\n        value: true\n    }), mod);\nvar __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n    CHECK_TYPES: ()=>CHECK_TYPES,\n    CodeHashManager: ()=>CodeHashManager,\n    CodeHashMetaKey: ()=>CodeHashMetaKey,\n    HealthChecker: ()=>HealthChecker,\n    InvalidJsonError: ()=>InvalidJsonError,\n    InvalidVariableError: ()=>InvalidVariableError,\n    JsonVariable: ()=>JsonVariable,\n    ParseJsonlStream: ()=>ParseJsonlStream,\n    RetryableError: ()=>RetryableError,\n    WebhookPayloadTemplate: ()=>WebhookPayloadTemplate,\n    betterClearInterval: ()=>betterClearInterval,\n    betterSetInterval: ()=>betterSetInterval,\n    buildOrVersionNumberIntToStr: ()=>buildOrVersionNumberIntToStr,\n    checkParamPrototypeOrThrow: ()=>checkParamPrototypeOrThrow,\n    concatStreamToBuffer: ()=>concatStreamToBuffer,\n    configureLogger: ()=>configureLogger,\n    createHmacSignature: ()=>createHmacSignature,\n    createStorageContentSignature: ()=>createStorageContentSignature,\n    cryptoRandomObjectId: ()=>cryptoRandomObjectId,\n    dateToString: ()=>dateToString,\n    delayPromise: ()=>delayPromise,\n    deterministicUniqueId: ()=>deterministicUniqueId,\n    escapeForBson: ()=>escapeForBson,\n    escapePropertyName: ()=>escapePropertyName,\n    escapeRegExp: ()=>escapeRegExp,\n    expressErrorHandler: ()=>expressErrorHandler,\n    getOrdinalSuffix: ()=>getOrdinalSuffix,\n    getRandomInt: ()=>getRandomInt,\n    http404Route: ()=>http404Route,\n    isBadForMongo: ()=>isBadForMongo,\n    isBuffer: ()=>isBuffer,\n    isForbiddenUsername: ()=>isForbiddenUsername,\n    isNullOrUndefined: ()=>isNullOrUndefined,\n    isUrlRelative: ()=>isUrlRelative,\n    jsonStringifyExtended: ()=>jsonStringifyExtended,\n    leftpad: ()=>leftpad,\n    markedDecreaseHeadsLevel: ()=>markedDecreaseHeadsLevel,\n    markedSetNofollowLinks: ()=>markedSetNofollowLinks,\n    normalizeUrl: ()=>normalizeUrl,\n    parseDateFromJson: ()=>parseDateFromJson,\n    parseUrl: ()=>parseUrl,\n    privateDecrypt: ()=>privateDecrypt,\n    promisifyServerListen: ()=>promisifyServerListen,\n    publicEncrypt: ()=>publicEncrypt,\n    readStreamToString: ()=>readStreamToString,\n    removeFromArray: ()=>removeFromArray,\n    retryWithExpBackoff: ()=>retryWithExpBackoff,\n    separateImports: ()=>separateImports,\n    sequentializePromises: ()=>sequentializePromises,\n    splitFullName: ()=>splitFullName,\n    timeoutPromise: ()=>timeoutPromise,\n    traverseObject: ()=>traverseObject,\n    truncate: ()=>truncate,\n    unescapeFromBson: ()=>unescapeFromBson,\n    unescapePropertyName: ()=>unescapePropertyName,\n    weightedAverage: ()=>weightedAverage\n});\nmodule.exports = __toCommonJS(index_exports);\n// src/utilities.ts\nvar import_node_crypto = __toESM(__webpack_require__(/*! crypto */ \"crypto\"));\nvar import_consts = __webpack_require__(/*! @apify/consts */ \"(rsc)/./node_modules/@apify/consts/cjs/index.cjs\");\nvar import_log = __toESM(__webpack_require__(/*! @apify/log */ \"(rsc)/./node_modules/@apify/log/cjs/index.cjs\"));\nfunction cryptoRandomObjectId(length = 17) {\n    const chars = \"abcdefghijklmnopqrstuvwxyzABCEDFGHIJKLMNOPQRSTUVWXYZ0123456789\";\n    const bytes = import_node_crypto.default.randomBytes(length);\n    let str = \"\";\n    for(let i = bytes.length - 1; i >= 0; i--){\n        str += chars[(bytes[i] | 0) % chars.length];\n    }\n    return str;\n}\n__name(cryptoRandomObjectId, \"cryptoRandomObjectId\");\nfunction deterministicUniqueId(key, length = 17) {\n    return import_node_crypto.default.createHash(\"sha256\").update(key).digest(\"base64\").replace(/(\\+|\\/|=)/g, \"x\").substr(0, length);\n}\n__name(deterministicUniqueId, \"deterministicUniqueId\");\nfunction getRandomInt(maxExcluded) {\n    maxExcluded = Math.floor(maxExcluded);\n    return Math.floor(Math.random() * maxExcluded);\n}\n__name(getRandomInt, \"getRandomInt\");\nfunction parseDateFromJson(date) {\n    if (typeof date === \"string\") {\n        return new Date(Date.parse(date));\n    }\n    return date;\n}\n__name(parseDateFromJson, \"parseDateFromJson\");\nasync function delayPromise(millis) {\n    return new Promise((resolve)=>{\n        if (millis > 0) {\n            setTimeout(()=>resolve(), millis);\n        } else {\n            resolve();\n        }\n    });\n}\n__name(delayPromise, \"delayPromise\");\nfunction removeFromArray(array, element) {\n    const index = array.indexOf(element);\n    if (index >= 0) {\n        array.splice(index, 1);\n        return true;\n    }\n    return false;\n}\n__name(removeFromArray, \"removeFromArray\");\nfunction http404Route(req, res) {\n    res.status(404);\n    res.send(\"Page not found\");\n}\n__name(http404Route, \"http404Route\");\nfunction expressErrorHandler(err, req, res, next) {\n    import_log.default.warning(\"Client HTTP request failed\", {\n        url: req.url,\n        errMsg: err.message\n    });\n    if (res.headersSent) {\n        next(err);\n        return;\n    }\n    res.status(505);\n    res.send(\"Internal server error\");\n}\n__name(expressErrorHandler, \"expressErrorHandler\");\nfunction betterSetInterval(func, delay) {\n    let scheduleNextRun;\n    let timeoutId;\n    let isRunning = true;\n    const funcWrapper = /* @__PURE__ */ __name(function() {\n        void new Promise((resolve)=>{\n            resolve(func(()=>void 0));\n        }).finally(scheduleNextRun);\n    }, \"funcWrapper\");\n    scheduleNextRun = /* @__PURE__ */ __name(function() {\n        if (isRunning) timeoutId = setTimeout(funcWrapper, delay);\n    }, \"scheduleNextRun\");\n    funcWrapper();\n    return {\n        _betterClearInterval () {\n            isRunning = false;\n            clearTimeout(timeoutId);\n        }\n    };\n}\n__name(betterSetInterval, \"betterSetInterval\");\nfunction betterClearInterval(intervalID) {\n    if (intervalID && intervalID._betterClearInterval) {\n        try {\n            intervalID._betterClearInterval();\n        } catch (e) {\n            import_log.default.exception(e, \"_betterClearInterval() threw an exception!?\");\n        }\n    }\n}\n__name(betterClearInterval, \"betterClearInterval\");\nfunction escapeRegExp(str) {\n    return String(str).replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\n__name(escapeRegExp, \"escapeRegExp\");\nfunction leftpad(str, len, ch = \" \") {\n    str = String(str);\n    let i = -1;\n    if (!ch && ch !== 0) ch = \" \";\n    len -= str.length;\n    while(++i < len){\n        str = ch + str;\n    }\n    return str;\n}\n__name(leftpad, \"leftpad\");\nfunction weightedAverage(val1, weight1, val2, weight2) {\n    return (val1 * weight1 + val2 * weight2) / (weight1 + weight2);\n}\n__name(weightedAverage, \"weightedAverage\");\nvar FORBIDDEN_USERNAMES_REGEXPS = [\n    // App routes\n    \"page-not-found\",\n    \"docs\",\n    \"terms-of-use\",\n    \"about\",\n    \"pricing\",\n    \"privacy-policy\",\n    \"customers\",\n    \"request-form\",\n    \"request-solution\",\n    \"release-notes\",\n    \"jobs\",\n    \"api-reference\",\n    \"video-tutorials\",\n    \"acts\",\n    \"key-value-stores\",\n    \"schedules\",\n    \"account\",\n    \"sign-up\",\n    \"sign-in-discourse\",\n    \"admin\",\n    \"documentation\",\n    \"change-password\",\n    \"enroll-account\",\n    \"forgot-password\",\n    \"reset-password\",\n    \"sign-in\",\n    \"verify-email\",\n    \"live-status\",\n    \"browser-info\",\n    \"webhooks\",\n    \"health-check\",\n    \"api\",\n    \"change-log\",\n    \"dashboard\",\n    \"community\",\n    \"crawlers\",\n    \"ext\",\n    // Various strings\n    \"admin\",\n    \"administration\",\n    \"crawler\",\n    \"act\",\n    \"library\",\n    \"lib\",\n    \"apifier\",\n    \"team\",\n    \"contact\",\n    \"doc\",\n    \"documentation\",\n    \"for-business\",\n    \"for-developers\",\n    \"developers\",\n    \"business\",\n    \"integrations\",\n    \"job\",\n    \"setting\",\n    \"settings\",\n    \"privacy\",\n    \"policy\",\n    \"assets\",\n    \"help\",\n    \"config\",\n    \"configuration\",\n    \"terms\",\n    \"hiring\",\n    \"hire\",\n    \"status\",\n    \"status-page\",\n    \"solutions\",\n    \"support\",\n    \"market\",\n    \"marketplace\",\n    \"download\",\n    \"downloads\",\n    \"username\",\n    \"users\",\n    \"user\",\n    \"login\",\n    \"logout\",\n    \"signin\",\n    \"sign\",\n    \"signup\",\n    \"sign-out\",\n    \"signout\",\n    \"plugins\",\n    \"plug-ins\",\n    \"reset\",\n    \"password\",\n    \"passwords\",\n    \"square\",\n    \"profile-photos\",\n    \"profiles\",\n    \"true\",\n    \"false\",\n    \"js\",\n    \"css\",\n    \"img\",\n    \"images\",\n    \"image\",\n    \"partials\",\n    \"fonts\",\n    \"font\",\n    \"dynamic_templates\",\n    \"app\",\n    \"schedules\",\n    \"community\",\n    \"storage\",\n    \"storages\",\n    \"account\",\n    \"node_modules\",\n    \"bower_components\",\n    \"video\",\n    \"knowledgebase\",\n    \"forum\",\n    \"customers\",\n    \"blog\",\n    \"health-check\",\n    \"health\",\n    \"anim\",\n    \"forum_topics.json\",\n    \"forum_categories.json\",\n    \"me\",\n    \"you\",\n    \"him\",\n    \"she\",\n    \"it\",\n    \"external\",\n    \"actor\",\n    \"crawler\",\n    \"scheduler\",\n    \"api\",\n    \"sdk\",\n    \"puppeteer\",\n    \"webdriver\",\n    \"selenium\",\n    \"(selenium.*webdriver)\",\n    \"undefined\",\n    \"page-analyzer\",\n    \"wp-login.php\",\n    \"welcome.action\",\n    \"echo\",\n    \"proxy\",\n    \"super-proxy\",\n    \"gdpr\",\n    \"case-studies\",\n    \"use-cases\",\n    \"how-to\",\n    \"kb\",\n    \"cookies\",\n    \"cookie-policy\",\n    \"cookies-policy\",\n    \"powered-by\",\n    \"run\",\n    \"runs\",\n    \"actor\",\n    \"actors\",\n    \"act\",\n    \"acts\",\n    \"success-stories\",\n    \"roadmap\",\n    \"join-marketplace\",\n    \"presskit\",\n    \"press-kit\",\n    \"covid-19\",\n    \"covid\",\n    \"covid19\",\n    \"matfyz\",\n    \"ideas\",\n    \"public-actors\",\n    \"resources\",\n    \"partners\",\n    \"affiliate\",\n    \"industries\",\n    \"web-scraping\",\n    \"custom-solutions\",\n    \"solution-provider\",\n    \"alternatives\",\n    \"platform\",\n    \"freelancers\",\n    \"freelancer\",\n    \"partner\",\n    \"preview\",\n    \"templates\",\n    \"data-for-generative-ai\",\n    \"discord\",\n    \"praguecrawl\",\n    \"prague-crawl\",\n    \"bob\",\n    \"ai-agents\",\n    \"reel\",\n    \"video-reel\",\n    \"mcp\",\n    \"model-context-protocol\",\n    \"modelcontextprotocol\",\n    \"apify.com\",\n    \"design-kit\",\n    \"press-kit\",\n    \"scrapers\",\n    \"professional-services\",\n    // Special files\n    \"index\",\n    \"index\\\\.html\",\n    \"(favicon\\\\.[a-z]+)\",\n    \"BingSiteAuth.xml\",\n    \"(google.+\\\\.html)\",\n    \"robots\\\\.txt\",\n    \"(sitemap\\\\.[a-z]+)\",\n    \"(apple-touch-icon.*)\",\n    \"security-whitepaper\\\\.pdf\",\n    \"security\\\\.txt\",\n    // All hidden files\n    \"(\\\\..*)\",\n    // File starting with xxx-\n    \"(xxx-.*)\",\n    // Strings not starting with letter or number\n    \"([^0-9a-z].*)\",\n    // Strings not ending with letter or number\n    \"(.*[^0-9a-z])\",\n    // Strings where there's more than one underscore, comma or dash in row\n    \"(.*[_.\\\\-]{2}.*)\",\n    // Reserved usernames from https://github.com/shouldbee/reserved-usernames/blob/master/reserved-usernames.json\n    \"0\",\n    \"about\",\n    \"access\",\n    \"account\",\n    \"accounts\",\n    \"activate\",\n    \"activities\",\n    \"activity\",\n    \"ad\",\n    \"add\",\n    \"address\",\n    \"adm\",\n    \"admin\",\n    \"administration\",\n    \"administrator\",\n    \"ads\",\n    \"adult\",\n    \"advertising\",\n    \"affiliate\",\n    \"affiliates\",\n    \"ajax\",\n    \"all\",\n    \"alpha\",\n    \"analysis\",\n    \"analytics\",\n    \"android\",\n    \"anon\",\n    \"anonymous\",\n    \"api\",\n    \"app\",\n    \"apps\",\n    \"archive\",\n    \"archives\",\n    \"article\",\n    \"asct\",\n    \"asset\",\n    \"atom\",\n    \"auth\",\n    \"authentication\",\n    \"avatar\",\n    \"backup\",\n    \"balancer-manager\",\n    \"banner\",\n    \"banners\",\n    \"beta\",\n    \"billing\",\n    \"bin\",\n    \"blog\",\n    \"blogs\",\n    \"board\",\n    \"book\",\n    \"bookmark\",\n    \"bot\",\n    \"bots\",\n    \"bug\",\n    \"business\",\n    \"cache\",\n    \"cadastro\",\n    \"calendar\",\n    \"call\",\n    \"campaign\",\n    \"cancel\",\n    \"captcha\",\n    \"career\",\n    \"careers\",\n    \"cart\",\n    \"categories\",\n    \"category\",\n    \"cgi\",\n    \"cgi-bin\",\n    \"changelog\",\n    \"chat\",\n    \"check\",\n    \"checking\",\n    \"checkout\",\n    \"client\",\n    \"cliente\",\n    \"clients\",\n    \"code\",\n    \"codereview\",\n    \"comercial\",\n    \"comment\",\n    \"comments\",\n    \"communities\",\n    \"community\",\n    \"company\",\n    \"compare\",\n    \"compras\",\n    \"config\",\n    \"configuration\",\n    \"connect\",\n    \"contact\",\n    \"contact-us\",\n    \"contact_us\",\n    \"contactus\",\n    \"contest\",\n    \"contribute\",\n    \"corp\",\n    \"create\",\n    \"css\",\n    \"dashboard\",\n    \"data\",\n    \"db\",\n    \"default\",\n    \"delete\",\n    \"demo\",\n    \"design\",\n    \"designer\",\n    \"destroy\",\n    \"dev\",\n    \"devel\",\n    \"developer\",\n    \"developers\",\n    \"diagram\",\n    \"diary\",\n    \"dict\",\n    \"dictionary\",\n    \"die\",\n    \"dir\",\n    \"direct_messages\",\n    \"directory\",\n    \"dist\",\n    \"doc\",\n    \"docs\",\n    \"documentation\",\n    \"domain\",\n    \"download\",\n    \"downloads\",\n    \"ecommerce\",\n    \"edit\",\n    \"editor\",\n    \"edu\",\n    \"education\",\n    \"email\",\n    \"employment\",\n    \"empty\",\n    \"end\",\n    \"enterprise\",\n    \"entries\",\n    \"entry\",\n    \"error\",\n    \"errors\",\n    \"eval\",\n    \"event\",\n    \"exit\",\n    \"explore\",\n    \"facebook\",\n    \"faq\",\n    \"favorite\",\n    \"favorites\",\n    \"feature\",\n    \"features\",\n    \"feed\",\n    \"feedback\",\n    \"feeds\",\n    \"file\",\n    \"files\",\n    \"first\",\n    \"flash\",\n    \"fleet\",\n    \"fleets\",\n    \"flog\",\n    \"follow\",\n    \"followers\",\n    \"following\",\n    \"forgot\",\n    \"form\",\n    \"forum\",\n    \"forums\",\n    \"founder\",\n    \"free\",\n    \"friend\",\n    \"friends\",\n    \"ftp\",\n    \"gadget\",\n    \"gadgets\",\n    \"game\",\n    \"games\",\n    \"get\",\n    \"gift\",\n    \"gifts\",\n    \"gist\",\n    \"github\",\n    \"graph\",\n    \"group\",\n    \"groups\",\n    \"guest\",\n    \"guests\",\n    \"help\",\n    \"home\",\n    \"homepage\",\n    \"host\",\n    \"hosting\",\n    \"hostmaster\",\n    \"hostname\",\n    \"howto\",\n    \"hpg\",\n    \"html\",\n    \"http\",\n    \"httpd\",\n    \"https\",\n    \"i\",\n    \"iamges\",\n    \"icon\",\n    \"icons\",\n    \"id\",\n    \"idea\",\n    \"ideas\",\n    \"image\",\n    \"images\",\n    \"imap\",\n    \"img\",\n    \"index\",\n    \"indice\",\n    \"info\",\n    \"information\",\n    \"inquiry\",\n    \"instagram\",\n    \"intranet\",\n    \"invitations\",\n    \"invite\",\n    \"ipad\",\n    \"iphone\",\n    \"irc\",\n    \"is\",\n    \"issue\",\n    \"issues\",\n    \"it\",\n    \"item\",\n    \"items\",\n    \"java\",\n    \"javascript\",\n    \"job\",\n    \"jobs\",\n    \"join\",\n    \"js\",\n    \"json\",\n    \"jump\",\n    \"knowledgebase\",\n    \"language\",\n    \"languages\",\n    \"last\",\n    \"ldap-status\",\n    \"legal\",\n    \"license\",\n    \"link\",\n    \"links\",\n    \"linux\",\n    \"list\",\n    \"lists\",\n    \"log\",\n    \"log-in\",\n    \"log-out\",\n    \"log_in\",\n    \"log_out\",\n    \"login\",\n    \"logout\",\n    \"logs\",\n    \"m\",\n    \"mac\",\n    \"mail\",\n    \"mail1\",\n    \"mail2\",\n    \"mail3\",\n    \"mail4\",\n    \"mail5\",\n    \"mailer\",\n    \"mailing\",\n    \"maintenance\",\n    \"manager\",\n    \"manual\",\n    \"map\",\n    \"maps\",\n    \"marketing\",\n    \"master\",\n    \"me\",\n    \"media\",\n    \"member\",\n    \"members\",\n    \"message\",\n    \"messages\",\n    \"messenger\",\n    \"microblog\",\n    \"microblogs\",\n    \"mine\",\n    \"mis\",\n    \"mob\",\n    \"mobile\",\n    \"movie\",\n    \"movies\",\n    \"mp3\",\n    \"msg\",\n    \"msn\",\n    \"music\",\n    \"musicas\",\n    \"mx\",\n    \"my\",\n    \"mysql\",\n    \"name\",\n    \"named\",\n    \"nan\",\n    \"navi\",\n    \"navigation\",\n    \"net\",\n    \"network\",\n    \"new\",\n    \"news\",\n    \"newsletter\",\n    \"nick\",\n    \"nickname\",\n    \"notes\",\n    \"noticias\",\n    \"notification\",\n    \"notifications\",\n    \"notify\",\n    \"ns\",\n    \"ns1\",\n    \"ns10\",\n    \"ns2\",\n    \"ns3\",\n    \"ns4\",\n    \"ns5\",\n    \"ns6\",\n    \"ns7\",\n    \"ns8\",\n    \"ns9\",\n    \"null\",\n    \"oauth\",\n    \"oauth_clients\",\n    \"offer\",\n    \"offers\",\n    \"official\",\n    \"old\",\n    \"online\",\n    \"openid\",\n    \"operator\",\n    \"order\",\n    \"orders\",\n    \"organization\",\n    \"organizations\",\n    \"overview\",\n    \"owner\",\n    \"owners\",\n    \"page\",\n    \"pager\",\n    \"pages\",\n    \"panel\",\n    \"password\",\n    \"payment\",\n    \"perl\",\n    \"phone\",\n    \"photo\",\n    \"photoalbum\",\n    \"photos\",\n    \"php\",\n    \"phpmyadmin\",\n    \"phppgadmin\",\n    \"phpredisadmin\",\n    \"pic\",\n    \"pics\",\n    \"ping\",\n    \"plan\",\n    \"plans\",\n    \"plugin\",\n    \"plugins\",\n    \"policy\",\n    \"pop\",\n    \"pop3\",\n    \"popular\",\n    \"portal\",\n    \"post\",\n    \"postfix\",\n    \"postmaster\",\n    \"posts\",\n    \"pr\",\n    \"premium\",\n    \"press\",\n    \"price\",\n    \"pricing\",\n    \"privacy\",\n    \"privacy-policy\",\n    \"privacy_policy\",\n    \"privacypolicy\",\n    \"private\",\n    \"product\",\n    \"products\",\n    \"profile\",\n    \"project\",\n    \"projects\",\n    \"promo\",\n    \"pub\",\n    \"public\",\n    \"purpose\",\n    \"put\",\n    \"python\",\n    \"query\",\n    \"random\",\n    \"ranking\",\n    \"read\",\n    \"readme\",\n    \"recent\",\n    \"recruit\",\n    \"recruitment\",\n    \"register\",\n    \"registration\",\n    \"release\",\n    \"remove\",\n    \"replies\",\n    \"report\",\n    \"reports\",\n    \"repositories\",\n    \"repository\",\n    \"req\",\n    \"request\",\n    \"requests\",\n    \"reset\",\n    \"roc\",\n    \"root\",\n    \"rss\",\n    \"ruby\",\n    \"rule\",\n    \"sag\",\n    \"sale\",\n    \"sales\",\n    \"sample\",\n    \"samples\",\n    \"save\",\n    \"school\",\n    \"script\",\n    \"scripts\",\n    \"search\",\n    \"secure\",\n    \"security\",\n    \"self\",\n    \"send\",\n    \"server\",\n    \"server-info\",\n    \"server-status\",\n    \"service\",\n    \"services\",\n    \"session\",\n    \"sessions\",\n    \"setting\",\n    \"settings\",\n    \"setup\",\n    \"share\",\n    \"shop\",\n    \"show\",\n    \"sign-in\",\n    \"sign-up\",\n    \"sign_in\",\n    \"sign_up\",\n    \"signin\",\n    \"signout\",\n    \"signup\",\n    \"site\",\n    \"sitemap\",\n    \"sites\",\n    \"smartphone\",\n    \"smtp\",\n    \"soporte\",\n    \"source\",\n    \"spec\",\n    \"special\",\n    \"sql\",\n    \"src\",\n    \"ssh\",\n    \"ssl\",\n    \"ssladmin\",\n    \"ssladministrator\",\n    \"sslwebmaster\",\n    \"staff\",\n    \"stage\",\n    \"staging\",\n    \"start\",\n    \"stat\",\n    \"state\",\n    \"static\",\n    \"stats\",\n    \"status\",\n    \"store\",\n    \"stores\",\n    \"stories\",\n    \"style\",\n    \"styleguide\",\n    \"stylesheet\",\n    \"stylesheets\",\n    \"subdomain\",\n    \"subscribe\",\n    \"subscription\",\n    \"subscriptions\",\n    \"suporte\",\n    \"support\",\n    \"svn\",\n    \"swf\",\n    \"sys\",\n    \"sysadmin\",\n    \"sysadministrator\",\n    \"system\",\n    \"tablet\",\n    \"tablets\",\n    \"tag\",\n    \"talk\",\n    \"task\",\n    \"tasks\",\n    \"team\",\n    \"teams\",\n    \"tech\",\n    \"telnet\",\n    \"term\",\n    \"terms\",\n    \"terms-of-service\",\n    \"terms_of_service\",\n    \"termsofservice\",\n    \"test\",\n    \"test1\",\n    \"test2\",\n    \"test3\",\n    \"teste\",\n    \"testing\",\n    \"tests\",\n    \"theme\",\n    \"themes\",\n    \"thread\",\n    \"threads\",\n    \"tmp\",\n    \"todo\",\n    \"tool\",\n    \"tools\",\n    \"top\",\n    \"topic\",\n    \"topics\",\n    \"tos\",\n    \"tour\",\n    \"translations\",\n    \"trends\",\n    \"tutorial\",\n    \"tux\",\n    \"tv\",\n    \"twitter\",\n    \"undef\",\n    \"unfollow\",\n    \"unsubscribe\",\n    \"update\",\n    \"upload\",\n    \"uploads\",\n    \"url\",\n    \"usage\",\n    \"user\",\n    \"username\",\n    \"users\",\n    \"usuario\",\n    \"vendas\",\n    \"ver\",\n    \"version\",\n    \"video\",\n    \"videos\",\n    \"visitor\",\n    \"watch\",\n    \"weather\",\n    \"web\",\n    \"webhook\",\n    \"webhooks\",\n    \"webmail\",\n    \"webmaster\",\n    \"website\",\n    \"websites\",\n    \"welcome\",\n    \"widget\",\n    \"widgets\",\n    \"wiki\",\n    \"win\",\n    \"windows\",\n    \"word\",\n    \"work\",\n    \"works\",\n    \"workshop\",\n    \"ww\",\n    \"wws\",\n    \"www\",\n    \"www1\",\n    \"www2\",\n    \"www3\",\n    \"www4\",\n    \"www5\",\n    \"www6\",\n    \"www7\",\n    \"wwws\",\n    \"wwww\",\n    \"xfn\",\n    \"xml\",\n    \"xmpp\",\n    \"xpg\",\n    \"xxx\",\n    \"yaml\",\n    \"year\",\n    \"yml\",\n    \"you\",\n    \"yourdomain\",\n    \"yourname\",\n    \"yoursite\",\n    \"yourusername\"\n];\nvar FORBIDDEN_REGEXP = new RegExp(`^(${import_consts.ANONYMOUS_USERNAME}|${FORBIDDEN_USERNAMES_REGEXPS.join(\"|\")})$`, \"i\");\nfunction isForbiddenUsername(username) {\n    return !!username.match(import_consts.APIFY_ID_REGEX) || !!username.match(FORBIDDEN_REGEXP);\n}\n__name(isForbiddenUsername, \"isForbiddenUsername\");\nasync function sequentializePromises(promises) {\n    if (!promises.length) return [];\n    const results = [];\n    for (const promiseOrFunc of promises){\n        const promise = promiseOrFunc instanceof Function ? promiseOrFunc() : promiseOrFunc;\n        results.push(await promise);\n    }\n    return results;\n}\n__name(sequentializePromises, \"sequentializePromises\");\nfunction checkParamPrototypeOrThrow(paramVal, paramName, prototypes, prototypeName, isOptional = false) {\n    if (isOptional && (paramVal === void 0 || paramVal === null)) return;\n    const hasCorrectPrototype = prototypes instanceof Array ? prototypes.some((prototype)=>paramVal instanceof prototype) : paramVal instanceof prototypes;\n    if (!hasCorrectPrototype) throw new Error(`Parameter \"${paramName}\" must be an instance of ${prototypeName}`);\n}\n__name(checkParamPrototypeOrThrow, \"checkParamPrototypeOrThrow\");\nfunction promisifyServerListen(server) {\n    return async (port)=>{\n        return new Promise((resolve, reject)=>{\n            const onError = /* @__PURE__ */ __name((err)=>{\n                removeListeners();\n                reject(err);\n            }, \"onError\");\n            const onListening = /* @__PURE__ */ __name(()=>{\n                removeListeners();\n                resolve();\n            }, \"onListening\");\n            const removeListeners = /* @__PURE__ */ __name(()=>{\n                server.removeListener(\"error\", onError);\n                server.removeListener(\"listening\", onListening);\n            }, \"removeListeners\");\n            server.on(\"error\", onError);\n            server.on(\"listening\", onListening);\n            server.listen(port);\n        });\n    };\n}\n__name(promisifyServerListen, \"promisifyServerListen\");\nfunction configureLogger(givenLog, isProduction) {\n    if (isProduction) {\n        givenLog.setOptions({\n            level: import_log.LogLevel.INFO,\n            logger: new import_log.LoggerJson()\n        });\n    } else {\n        givenLog.setOptions({\n            level: import_log.LogLevel.DEBUG\n        });\n    }\n}\n__name(configureLogger, \"configureLogger\");\nasync function timeoutPromise(promise, timeoutMillis, errorMessage = \"Promise has timed-out\") {\n    return new Promise((resolve, reject)=>{\n        let timeout;\n        let hasFulfilled = false;\n        const callback = /* @__PURE__ */ __name((err, result)=>{\n            if (hasFulfilled) return;\n            clearTimeout(timeout);\n            hasFulfilled = true;\n            if (err) {\n                reject(err);\n                return;\n            }\n            resolve(result);\n        }, \"callback\");\n        promise.then((result)=>callback(null, result), callback);\n        timeout = setTimeout(()=>callback(new Error(errorMessage)), timeoutMillis);\n    });\n}\n__name(timeoutPromise, \"timeoutPromise\");\n// src/utilities.client.ts\nvar import_consts2 = __webpack_require__(/*! @apify/consts */ \"(rsc)/./node_modules/@apify/consts/cjs/index.cjs\");\nfunction isNullOrUndefined(obj) {\n    return obj == null;\n}\n__name(isNullOrUndefined, \"isNullOrUndefined\");\nfunction isBuffer(obj) {\n    return obj != null && obj.constructor != null && typeof obj.constructor.isBuffer === \"function\" && obj.constructor.isBuffer(obj);\n}\n__name(isBuffer, \"isBuffer\");\nfunction dateToString(date, middleT) {\n    if (!(date instanceof Date)) {\n        return \"\";\n    }\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n    const hours = date.getHours();\n    const minutes = date.getMinutes();\n    const seconds = date.getSeconds();\n    const millis = date.getMilliseconds();\n    const pad = /* @__PURE__ */ __name((num)=>num < 10 ? `0${num}` : num, \"pad\");\n    const datePart = `${year}-${pad(month)}-${pad(day)}`;\n    const millisPart = millis < 10 ? `00${millis}` : millis < 100 ? `0${millis}` : millis;\n    const timePart = `${pad(hours)}:${pad(minutes)}:${pad(seconds)}.${millisPart}`;\n    return `${datePart}${middleT ? \"T\" : \" \"}${timePart}`;\n}\n__name(dateToString, \"dateToString\");\nfunction truncate(str, maxLength, suffix = \"...[truncated]\") {\n    maxLength = Math.floor(maxLength);\n    if (suffix.length > maxLength) {\n        throw new Error(\"suffix string cannot be longer than maxLength\");\n    }\n    if (typeof str === \"string\" && str.length > maxLength) {\n        str = str.substr(0, maxLength - suffix.length) + suffix;\n    }\n    return str;\n}\n__name(truncate, \"truncate\");\nfunction getOrdinalSuffix(num) {\n    const s = [\n        \"th\",\n        \"st\",\n        \"nd\",\n        \"rd\"\n    ];\n    const v = num % 100;\n    return s[(v - 20) % 10] || s[v] || s[0];\n}\n__name(getOrdinalSuffix, \"getOrdinalSuffix\");\nfunction parseUrl(str) {\n    if (typeof str !== \"string\") return {};\n    const o = {\n        strictMode: false,\n        key: [\n            \"source\",\n            \"protocol\",\n            \"authority\",\n            \"userInfo\",\n            \"user\",\n            \"password\",\n            \"host\",\n            \"port\",\n            \"relative\",\n            \"path\",\n            \"directory\",\n            \"file\",\n            \"query\",\n            \"fragment\"\n        ],\n        q: {\n            name: \"queryKey\",\n            parser: /(?:^|&)([^&=]*)=?([^&]*)/g\n        },\n        parser: {\n            strict: /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?))?((((?:[^?#\\/]*\\/)*)([^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n            // eslint-disable-line max-len,no-useless-escape\n            loose: /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/\n        }\n    };\n    const m = o.parser[o.strictMode ? \"strict\" : \"loose\"].exec(str);\n    const uri = {};\n    let i = o.key.length;\n    while(i--)uri[o.key[i]] = m[i] || \"\";\n    uri[o.q.name] = {};\n    uri[o.key[12]].replace(o.q.parser, ($0, $1, $2)=>{\n        if ($1) uri[o.q.name][$1] = $2;\n    });\n    uri.fragmentKey = {};\n    if (uri.fragment) {\n        uri.fragment.replace(o.q.parser, ($0, $1, $2)=>{\n            if ($1) uri.fragmentKey[$1] = $2;\n        });\n    }\n    return uri;\n}\n__name(parseUrl, \"parseUrl\");\nfunction normalizeUrl(url, keepFragment) {\n    if (typeof url !== \"string\" || !url.length) {\n        return null;\n    }\n    let urlObj;\n    try {\n        urlObj = new URL(url.trim());\n    } catch  {\n        return null;\n    }\n    const { searchParams } = urlObj;\n    for (const key of [\n        ...searchParams.keys()\n    ]){\n        if (key.startsWith(\"utm_\")) {\n            searchParams.delete(key);\n        }\n    }\n    searchParams.sort();\n    const protocol = urlObj.protocol.toLowerCase();\n    const host = urlObj.host.toLowerCase();\n    const path = urlObj.pathname.replace(/\\/$/, \"\");\n    const search = searchParams.toString() ? `?${searchParams}` : \"\";\n    const hash = keepFragment ? urlObj.hash : \"\";\n    return `${protocol}//${host}${path}${search}${hash}`;\n}\n__name(normalizeUrl, \"normalizeUrl\");\nfunction markedSetNofollowLinks(href, title, text, referrerHostname) {\n    let urlParsed;\n    try {\n        urlParsed = new URL(href);\n    } catch  {}\n    const isApifyLink = urlParsed && /(\\.|^)apify\\.com$/i.test(urlParsed.hostname);\n    const isSameHostname = !referrerHostname || urlParsed && urlParsed.hostname === referrerHostname;\n    if (isApifyLink && isSameHostname) {\n        return `<a href=\"${href}\">${title || text}</a>`;\n    }\n    if (isApifyLink) {\n        return `<a rel=\"noopener noreferrer\" target=\"_blank\" href=\"${href}\">${title || text}</a>`;\n    }\n    return `<a rel=\"noopener noreferrer nofollow\" target=\"_blank\" href=\"${href}\">${title || text}</a>`;\n}\n__name(markedSetNofollowLinks, \"markedSetNofollowLinks\");\nfunction markedDecreaseHeadsLevel(text, level) {\n    level += 1;\n    return `<h${level}>${text}</h${level}>`;\n}\n__name(markedDecreaseHeadsLevel, \"markedDecreaseHeadsLevel\");\nfunction buildOrVersionNumberIntToStr(int) {\n    if (typeof int !== \"number\" || !(int >= 0)) return null;\n    const major = Math.floor(int / import_consts2.VERSION_INT_MAJOR_BASE);\n    const remainder = int % import_consts2.VERSION_INT_MAJOR_BASE;\n    const minor = Math.floor(remainder / import_consts2.VERSION_INT_MINOR_BASE);\n    const build = remainder % import_consts2.VERSION_INT_MINOR_BASE;\n    let str = `${major}.${minor}`;\n    if (build > 0) str += `.${build}`;\n    return str;\n}\n__name(buildOrVersionNumberIntToStr, \"buildOrVersionNumberIntToStr\");\nvar ESCAPE_DOT = \"．\";\nvar ESCAPE_DOLLAR = \"＄\";\nvar ESCAPE_TO_BSON = \"ｔｏＢＳＯＮ\";\nvar ESCAPE_TO_STRING = \"ｔｏＳｔｒｉｎｇ\";\nvar ESCAPE_BSON_TYPE = \"＿ｂｓｏｎｔｙｐｅ\";\nvar ESCAPE_NULL = \"\";\nvar REGEXP_IS_ESCAPED = new RegExp(`(${ESCAPE_DOT}|^${ESCAPE_DOLLAR}|^${ESCAPE_TO_BSON}$|^${ESCAPE_BSON_TYPE}|^${ESCAPE_TO_STRING}$)`);\nvar REGEXP_DOT = new RegExp(ESCAPE_DOT, \"g\");\nvar REGEXP_DOLLAR = new RegExp(`^${ESCAPE_DOLLAR}`);\nvar REGEXP_TO_BSON = new RegExp(`^${ESCAPE_TO_BSON}$`);\nvar REGEXP_TO_STRING = new RegExp(`^${ESCAPE_TO_STRING}$`);\nvar REGEXP_BSON_TYPE = new RegExp(`^${ESCAPE_BSON_TYPE}$`);\nfunction escapePropertyName(name) {\n    if (/(\\.|^\\$|^toBSON$|^_bsontype$|^toString$|\\0)/.test(name)) {\n        name = name.replace(/\\./g, ESCAPE_DOT);\n        name = name.replace(/^\\$/, ESCAPE_DOLLAR);\n        name = name.replace(/^toBSON$/, ESCAPE_TO_BSON);\n        name = name.replace(/^toString$/, ESCAPE_TO_STRING);\n        name = name.replace(/^_bsontype$/, ESCAPE_BSON_TYPE);\n        name = name.replace(/\\0/g, ESCAPE_NULL);\n    }\n    return name;\n}\n__name(escapePropertyName, \"escapePropertyName\");\nfunction unescapePropertyName(name) {\n    if (REGEXP_IS_ESCAPED.test(name)) {\n        name = name.replace(REGEXP_DOT, \".\");\n        name = name.replace(REGEXP_DOLLAR, \"$\");\n        name = name.replace(REGEXP_TO_BSON, \"toBSON\");\n        name = name.replace(REGEXP_TO_STRING, \"toString\");\n        name = name.replace(REGEXP_BSON_TYPE, \"_bsontype\");\n    }\n    return name;\n}\n__name(unescapePropertyName, \"unescapePropertyName\");\nfunction traverseObject(obj, clone, transformFunc) {\n    if (obj === null || typeof obj !== \"object\" || Object.prototype.toString.call(obj) === \"[object Date]\" || isBuffer(obj)) return obj;\n    let result;\n    if (Array.isArray(obj)) {\n        result = clone ? new Array(obj.length) : obj;\n        for(let i = 0; i < obj.length; i++){\n            const val = traverseObject(obj[i], clone, transformFunc);\n            if (clone) result[i] = val;\n        }\n        return result;\n    }\n    result = clone ? {} : obj;\n    for(const key in obj){\n        const val = traverseObject(obj[key], clone, transformFunc);\n        const [transformedKey, transformedVal] = transformFunc(key, val);\n        if (key === transformedKey) {\n            if (clone || val !== transformedVal) result[key] = transformedVal;\n        } else {\n            result[transformedKey] = transformedVal;\n            if (!clone) delete obj[key];\n        }\n    }\n    return result;\n}\n__name(traverseObject, \"traverseObject\");\nfunction escapeForBson(obj, clone = false) {\n    return traverseObject(obj, clone, (key, value)=>[\n            escapePropertyName(key),\n            value\n        ]);\n}\n__name(escapeForBson, \"escapeForBson\");\nfunction unescapeFromBson(obj, clone = false) {\n    return traverseObject(obj, clone, (key, value)=>[\n            unescapePropertyName(key),\n            value\n        ]);\n}\n__name(unescapeFromBson, \"unescapeFromBson\");\nfunction isBadForMongo(obj) {\n    let isBad = false;\n    try {\n        traverseObject(obj, false, (key, value)=>{\n            const escapedKey = escapePropertyName(key);\n            if (key !== escapedKey) {\n                isBad = true;\n                throw new Error();\n            }\n            return [\n                key,\n                value\n            ];\n        });\n    } catch (e) {\n        if (!isBad) throw e;\n    }\n    return isBad;\n}\n__name(isBadForMongo, \"isBadForMongo\");\nvar _JsonVariable = class _JsonVariable {\n    constructor(name){\n        this.name = name;\n    }\n    getToken() {\n        return `{{${this.name}}}`;\n    }\n};\n__name(_JsonVariable, \"JsonVariable\");\nvar JsonVariable = _JsonVariable;\nfunction jsonStringifyExtended(value, replacer, space = 0) {\n    if (replacer && !(replacer instanceof Function)) throw new Error('Parameter \"replacer\" of jsonStringifyExtended() must be a function!');\n    const replacements = {};\n    const extendedReplacer = /* @__PURE__ */ __name((key, val)=>{\n        val = replacer ? replacer(key, val) : val;\n        if (val instanceof Function) return val.toString();\n        if (val instanceof JsonVariable) {\n            const randomToken = `<<<REPLACEMENT_TOKEN::${Math.random()}>>>`;\n            replacements[randomToken] = val.getToken();\n            return randomToken;\n        }\n        return val;\n    }, \"extendedReplacer\");\n    let stringifiedValue = JSON.stringify(value, extendedReplacer, space);\n    Object.entries(replacements).forEach(([replacementToken, replacementValue])=>{\n        stringifiedValue = stringifiedValue.replace(`\"${replacementToken}\"`, replacementValue);\n    });\n    return stringifiedValue;\n}\n__name(jsonStringifyExtended, \"jsonStringifyExtended\");\nfunction splitFullName(fullName) {\n    if (typeof fullName !== \"string\") return [\n        null,\n        null\n    ];\n    const names = (fullName || \"\").trim().split(\" \");\n    const nonEmptyNames = names.filter((val)=>val);\n    if (nonEmptyNames.length === 0) {\n        return [\n            null,\n            null\n        ];\n    }\n    if (nonEmptyNames.length === 1) {\n        return [\n            null,\n            nonEmptyNames[0]\n        ];\n    }\n    return [\n        names[0],\n        nonEmptyNames.slice(1).join(\" \")\n    ];\n}\n__name(splitFullName, \"splitFullName\");\nfunction isUrlRelative(url) {\n    return import_consts2.RELATIVE_URL_REGEX.test(url);\n}\n__name(isUrlRelative, \"isUrlRelative\");\n// src/exponential_backoff.ts\nvar import_log2 = __toESM(__webpack_require__(/*! @apify/log */ \"(rsc)/./node_modules/@apify/log/cjs/index.cjs\"));\nvar _RetryableError = class _RetryableError extends Error {\n    constructor(error, ...args){\n        super(...args);\n        __publicField(this, \"error\");\n        this.error = error;\n    }\n};\n__name(_RetryableError, \"RetryableError\");\nvar RetryableError = _RetryableError;\nasync function retryWithExpBackoff(params = {}) {\n    const { func, expBackoffMillis, expBackoffMaxRepeats } = params;\n    if (typeof func !== \"function\") {\n        throw new Error('Parameter \"func\" should be a function.');\n    }\n    if (typeof expBackoffMillis !== \"number\") {\n        throw new Error('Parameter \"expBackoffMillis\" should be a number.');\n    }\n    if (typeof expBackoffMaxRepeats !== \"number\") {\n        throw new Error('Parameter \"expBackoffMaxRepeats\" should be a number.');\n    }\n    for(let i = 0;; i++){\n        let error;\n        try {\n            return await func();\n        } catch (e) {\n            error = e;\n        }\n        if (!(error instanceof RetryableError)) {\n            throw error;\n        }\n        if (i >= expBackoffMaxRepeats - 1) {\n            throw error.error;\n        }\n        const waitMillis = expBackoffMillis * 2 ** i;\n        const rand = /* @__PURE__ */ __name((from, to)=>from + Math.floor(Math.random() * (to - from + 1)), \"rand\");\n        const randomizedWaitMillis = rand(waitMillis, waitMillis * 2);\n        if (i === Math.round(expBackoffMaxRepeats / 2)) {\n            import_log2.default.warning(`Retry failed ${i} times and will be repeated in ${randomizedWaitMillis}ms`, {\n                originalError: error.error.message,\n                errorDetails: Reflect.get(error.error, \"details\")\n            });\n        }\n        await delayPromise(randomizedWaitMillis);\n    }\n}\n__name(retryWithExpBackoff, \"retryWithExpBackoff\");\n// src/health_checker.ts\nvar CHECK_TYPES = /* @__PURE__ */ ((CHECK_TYPES2)=>{\n    CHECK_TYPES2[\"MONGODB_PING\"] = \"MONGODB_PING\";\n    CHECK_TYPES2[\"MONGODB_READ\"] = \"MONGODB_READ\";\n    CHECK_TYPES2[\"MONGODB_WRITE\"] = \"MONGODB_WRITE\";\n    CHECK_TYPES2[\"REDIS\"] = \"REDIS\";\n    CHECK_TYPES2[\"REDIS_PING\"] = \"REDIS_PING\";\n    CHECK_TYPES2[\"REDIS_WRITE\"] = \"REDIS_WRITE\";\n    return CHECK_TYPES2;\n})(CHECK_TYPES || {});\nvar _HealthChecker = class _HealthChecker {\n    constructor(options){\n        this.options = options;\n        __publicField(this, \"checks\");\n        __publicField(this, \"redisPrefix\");\n        __publicField(this, \"redisTtlSecs\");\n        __publicField(this, \"checkTimeoutMillis\");\n        __publicField(this, \"mongoDbWriteTestCollection\");\n        __publicField(this, \"mongoDbWriteTestRemoveOlderThanSecs\");\n        const { checks, redisPrefix = \"health-check\", redisTtlSecs = 15, checkTimeoutMillis = 15e3, mongoDbWriteTestCollection = \"healthCheckPlayground\", mongoDbWriteTestRemoveOlderThanSecs = 15 } = options;\n        if (!Array.isArray(checks)) throw new Error('Parameter \"check\" must be an array');\n        checks.map((check)=>this._validateCheck(check));\n        this.checks = checks;\n        this.redisPrefix = redisPrefix;\n        this.redisTtlSecs = redisTtlSecs;\n        this.checkTimeoutMillis = checkTimeoutMillis;\n        this.mongoDbWriteTestCollection = mongoDbWriteTestCollection;\n        this.mongoDbWriteTestRemoveOlderThanSecs = mongoDbWriteTestRemoveOlderThanSecs;\n    }\n    async ensureIsHealthy() {\n        for (const check of this.checks){\n            try {\n                const checkPromise = this._performCheck(check);\n                await timeoutPromise(checkPromise, this.checkTimeoutMillis, \"Check has timed-out\");\n            } catch (_err) {\n                const err = _err;\n                throw new Error(`Health check test \"${check.type}\" failed with an error: ${err.message}\"`);\n            }\n        }\n    }\n    _validateCheck(check) {\n        if (!(check.type in CHECK_TYPES)) throw new Error(`Check type \"${check.type}\" is invalid`);\n        if (typeof check.client !== \"object\") throw new Error(`Check client must be an object got \"${typeof check.client}\" instead`);\n    }\n    async _performCheck(check) {\n        switch(check.type){\n            case \"MONGODB_PING\" /* MONGODB_PING */ :\n                return this._testMongoDbPing(check);\n            case \"MONGODB_READ\" /* MONGODB_READ */ :\n                return this._testMongoDbRead(check);\n            case \"MONGODB_WRITE\" /* MONGODB_WRITE */ :\n                return this._testMongoDbWrite(check);\n            case \"REDIS_PING\" /* REDIS_PING */ :\n                return this._testRedisPing(check);\n            case \"REDIS\" /* REDIS */ :\n            case \"REDIS_WRITE\" /* REDIS_WRITE */ :\n                return this._testRedisWrite(check);\n            default:\n                throw new Error(\"Unknown check type\");\n        }\n    }\n    async _testMongoDbPing({ client }) {\n        const response = await client.command({\n            ping: 1\n        });\n        if (response.ok !== 1) throw new Error(`Got ${response.ok} instead of 1!`);\n    }\n    async _testMongoDbRead({ client }) {\n        const response = await client.listCollections().toArray();\n        if (!Array.isArray(response)) throw new Error(`Got ${typeof response} instead of an array!`);\n    }\n    async _testMongoDbWrite({ client }) {\n        const id = cryptoRandomObjectId();\n        const collection = client.collection(this.mongoDbWriteTestCollection);\n        await collection.deleteMany({\n            createdAt: {\n                $lt: new Date(Date.now() - this.mongoDbWriteTestRemoveOlderThanSecs * 1e3)\n            }\n        });\n        await collection.insertOne({\n            _id: id,\n            createdAt: /* @__PURE__ */ new Date()\n        });\n        const retrieved = await collection.findOne({\n            _id: id\n        });\n        if (!retrieved) throw new Error(`Item with ID \"${id}\" not found!`);\n    }\n    async _testRedisPing({ client }) {\n        const response = await client.ping();\n        if (response !== \"PONG\") throw new Error(`Got \"${response}\" instead of \"PONG\"!`);\n    }\n    async _testRedisWrite({ client }) {\n        const key = `${this.redisPrefix}:${cryptoRandomObjectId()}`;\n        const expected = \"OK\";\n        await client.set(key, expected, \"EX\", this.redisTtlSecs);\n        const given = await client.get(key);\n        if (given !== expected) throw new Error(`Returned value \"${given}\" is not equal to \"${expected}\"!`);\n    }\n};\n__name(_HealthChecker, \"HealthChecker\");\n__publicField(_HealthChecker, \"CHECK_TYPES\", CHECK_TYPES);\nvar HealthChecker = _HealthChecker;\n// src/parse_jsonl_stream.ts\nvar import_node_stream = __webpack_require__(/*! stream */ \"stream\");\nvar _ParseJsonlStream = class _ParseJsonlStream extends import_node_stream.Transform {\n    constructor(){\n        super(...arguments);\n        __publicField(this, \"pendingChunk\", null);\n    }\n    parseLineAndEmitObject(line) {\n        line = line.trim();\n        if (!line) {\n            return;\n        }\n        try {\n            const obj = JSON.parse(line);\n            this.emit(\"object\", obj);\n        } catch (e) {\n            throw new Error(`Cannot parse JSON stream data ('${String(line)}'): ${String(e)}`);\n        }\n    }\n    _transform(chunk, encoding, callback) {\n        let allData;\n        if (this.pendingChunk) {\n            allData = this.pendingChunk + chunk;\n            this.pendingChunk = null;\n        } else {\n            allData = chunk;\n        }\n        const lines = allData.toString().split(\"\\n\");\n        if (lines[lines.length - 1] !== \"\") {\n            this.pendingChunk = lines.pop();\n        }\n        try {\n            for(let i = 0; i < lines.length; i++){\n                this.parseLineAndEmitObject(lines[i]);\n            }\n        } catch (err) {\n            callback(err, null);\n            return;\n        }\n        callback(null, chunk);\n    }\n    // This function is called right after stream.end() is called by the writer.\n    // It just tries to process the pending chunk and returns an error if that fails.\n    _flush(callback) {\n        if (this.pendingChunk) {\n            try {\n                this.parseLineAndEmitObject(this.pendingChunk);\n                this.pendingChunk = null;\n            } catch (err) {\n                callback(err, null);\n                return;\n            }\n        }\n        callback();\n    }\n};\n__name(_ParseJsonlStream, \"ParseJsonlStream\");\nvar ParseJsonlStream = _ParseJsonlStream;\n// src/streams_utilities.ts\nasync function concatStreamToBuffer(stream) {\n    return new Promise((resolve, reject)=>{\n        const chunks = [];\n        stream.on(\"data\", (chunk)=>{\n            chunks.push(chunk);\n        }).on(\"error\", (e)=>reject(e)).on(\"end\", ()=>{\n            const buffer = Buffer.concat(chunks);\n            return resolve(buffer);\n        });\n    });\n}\n__name(concatStreamToBuffer, \"concatStreamToBuffer\");\nasync function readStreamToString(stream, encoding) {\n    const buffer = await concatStreamToBuffer(stream);\n    return buffer.toString(encoding);\n}\n__name(readStreamToString, \"readStreamToString\");\n// src/webhook_payload_template.ts\nvar _WebhookPayloadTemplateError = class _WebhookPayloadTemplateError extends Error {\n    constructor(message){\n        super(message);\n        this.name = this.constructor.name;\n        if (typeof Error.captureStackTrace === \"function\") {\n            Error.captureStackTrace(this, this.constructor);\n        }\n    }\n};\n__name(_WebhookPayloadTemplateError, \"WebhookPayloadTemplateError\");\nvar WebhookPayloadTemplateError = _WebhookPayloadTemplateError;\nvar _InvalidJsonError = class _InvalidJsonError extends WebhookPayloadTemplateError {\n    constructor(originalError){\n        super(originalError.message);\n    }\n};\n__name(_InvalidJsonError, \"InvalidJsonError\");\nvar InvalidJsonError = _InvalidJsonError;\nvar _InvalidVariableError = class _InvalidVariableError extends Error {\n    constructor(variable){\n        super(`Invalid payload template variable: ${variable}`);\n    }\n};\n__name(_InvalidVariableError, \"InvalidVariableError\");\nvar InvalidVariableError = _InvalidVariableError;\nvar _WebhookPayloadTemplate = class _WebhookPayloadTemplate {\n    constructor(template, allowedVariables = null, context = {}){\n        this.template = template;\n        this.allowedVariables = allowedVariables;\n        this.context = context;\n        __publicField(this, \"payload\");\n        __publicField(this, \"replacedVariables\", []);\n        this.payload = template;\n    }\n    /**\n   * Parse existing webhook payload template string into an object, replacing\n   * template variables using the provided context.\n   *\n   * Parse also validates the template structure, so it can be used\n   * to check validity of the template JSON and usage of allowedVariables.\n   */ static parse(payloadTemplate, allowedVariables = null, context = {}, options = {}) {\n        const type = typeof payloadTemplate;\n        if (type !== \"string\") throw new Error(`Cannot parse a ${type} payload template.`);\n        const template = new _WebhookPayloadTemplate(payloadTemplate, allowedVariables, context);\n        const data = template._parse();\n        if (options.interpolateStrings) {\n            return template._interpolate(data);\n        }\n        return data;\n    }\n    /**\n   * Stringify an object into a webhook payload template.\n   * Values created using `getTemplateVariable('foo.bar')`\n   * will be stringified to `{{foo.bar}}` template variable.\n   */ static stringify(objectTemplate, replacer, indent = 2) {\n        const type = typeof objectTemplate;\n        if (!objectTemplate || type !== \"object\") throw new Error(`Cannot stringify a ${type} payload template.`);\n        return jsonStringifyExtended(objectTemplate, replacer, indent);\n    }\n    /**\n   * Produces an instance of a template variable that can be used\n   * in objects and will be stringified into `{{variableName}}` syntax.\n   *\n   * **Example:**\n   * ```js\n   * const resourceVariable = WebhookPayloadTemplate.getVariable('resource');\n   * const objectTemplate = {\n   *     foo: 'foo',\n   *     bar: ['bar'],\n   *     res: resourceVariable,\n   * }\n   *\n   * const payloadTemplate = WebhookPayloadTemplate.stringify(objectTemplate);\n   * ```\n   *\n   * **Produces:**\n   * ```json\n   * {\n   *     \"foo\": \"foo\",\n   *     \"bar\": [\"bar\"],\n   *     \"res\": {{resource}},\n   * }\n   * ```\n   */ static getVariable(variableName) {\n        return new JsonVariable(variableName);\n    }\n    _parse() {\n        let currentIndex = 0;\n        while(true){\n            try {\n                return JSON.parse(this.payload);\n            } catch (err) {\n                const position = this._findPositionOfNextVariable(currentIndex);\n                if (!position) {\n                    throw new InvalidJsonError(err);\n                }\n                if (!position.isInsideString) {\n                    this._replaceVariable(position);\n                }\n                currentIndex = position.openBraceIndex + 1;\n            }\n        }\n    }\n    _interpolate(value) {\n        if (typeof value === \"string\") {\n            return this._interpolateString(value);\n        }\n        if (Array.isArray(value)) {\n            return this._interpolateArray(value);\n        }\n        if (typeof value === \"object\" && value !== null) {\n            return this._interpolateObject(value);\n        }\n        return value;\n    }\n    _interpolateString(value) {\n        if (value.match(/^\\{\\{([a-zA-Z0-9.]+)\\}\\}$/)) {\n            const variableName = value.substring(2, value.length - 2);\n            this._validateVariableName(variableName);\n            return this._getVariableValue(variableName);\n        }\n        return value.replace(/\\{\\{([a-zA-Z0-9.]+)\\}\\}/g, (match, variableName)=>{\n            this._validateVariableName(variableName);\n            const variableValue = this._getVariableValue(variableName);\n            return `${variableValue}`;\n        });\n    }\n    _interpolateObject(value) {\n        const result = {};\n        Object.entries(value).forEach(([key, v])=>{\n            result[key] = this._interpolate(v);\n        });\n        return result;\n    }\n    _interpolateArray(value) {\n        return value.map(this._interpolate.bind(this));\n    }\n    _findPositionOfNextVariable(startIndex = 0) {\n        const openBraceIndex = this.payload.indexOf(\"{{\", startIndex);\n        const closeBraceIndex = this.payload.indexOf(\"}}\", openBraceIndex) + 1;\n        const someVariableMaybeExists = openBraceIndex > -1 && closeBraceIndex > -1;\n        if (!someVariableMaybeExists) return null;\n        const isInsideString = this._isVariableInsideString(openBraceIndex);\n        return {\n            isInsideString,\n            openBraceIndex,\n            closeBraceIndex\n        };\n    }\n    _isVariableInsideString(openBraceIndex) {\n        const unescapedQuoteCount = this._countUnescapedDoubleQuotesUpToIndex(openBraceIndex);\n        return unescapedQuoteCount % 2 === 1;\n    }\n    _countUnescapedDoubleQuotesUpToIndex(index) {\n        const payloadSection = this.payload.substring(0, index);\n        let unescapedQuoteCount = 0;\n        for(let i = 0; i < payloadSection.length; i++){\n            const char = payloadSection[i];\n            const prevChar = payloadSection[i - 1];\n            if (char === '\"' && prevChar !== \"\\\\\") {\n                unescapedQuoteCount++;\n            }\n        }\n        return unescapedQuoteCount;\n    }\n    _replaceVariable({ openBraceIndex, closeBraceIndex }) {\n        const variableName = this.payload.substring(openBraceIndex + 2, closeBraceIndex - 1);\n        this._validateVariableName(variableName);\n        const replacement = this._getVariableReplacement(variableName);\n        this.replacedVariables.push({\n            variableName,\n            replacement\n        });\n        this.payload = this.payload.substring(0, openBraceIndex) + replacement + this.payload.substring(closeBraceIndex + 1);\n    }\n    _validateVariableName(variableName) {\n        if (this.allowedVariables === null) return;\n        const [variable] = variableName.split(\".\");\n        const isVariableValid = this.allowedVariables.has(variable);\n        if (!isVariableValid) throw new InvalidVariableError(variableName);\n    }\n    _getVariableValue(variableName) {\n        const [variable, ...properties] = variableName.split(\".\");\n        const context = this.context[variable];\n        const value = properties.reduce((ctx, prop)=>{\n            if (!ctx || typeof ctx !== \"object\") return null;\n            return ctx[prop];\n        }, context);\n        return value;\n    }\n    _getVariableReplacement(variableName) {\n        const value = this._getVariableValue(variableName);\n        return value ? JSON.stringify(value) : null;\n    }\n};\n__name(_WebhookPayloadTemplate, \"WebhookPayloadTemplate\");\nvar WebhookPayloadTemplate = _WebhookPayloadTemplate;\n// src/crypto.ts\nvar import_node_crypto2 = __toESM(__webpack_require__(/*! crypto */ \"crypto\"));\nvar ENCRYPTION_ALGORITHM = \"aes-256-gcm\";\nvar ENCRYPTION_KEY_LENGTH = 32;\nvar ENCRYPTION_IV_LENGTH = 16;\nvar ENCRYPTION_AUTH_TAG_LENGTH = 16;\nfunction publicEncrypt({ publicKey, value }) {\n    const key = cryptoRandomObjectId(ENCRYPTION_KEY_LENGTH);\n    const initVector = cryptoRandomObjectId(ENCRYPTION_IV_LENGTH);\n    const cipher = import_node_crypto2.default.createCipheriv(ENCRYPTION_ALGORITHM, key, initVector);\n    const bufferFromValue = Buffer.from(value, \"utf-8\");\n    const bufferFromKey = Buffer.from(key, \"utf-8\");\n    const bufferFromInitVector = Buffer.from(initVector, \"utf-8\");\n    const passwordBuffer = Buffer.concat([\n        bufferFromKey,\n        bufferFromInitVector\n    ]);\n    const encryptedValue = Buffer.concat([\n        cipher.update(bufferFromValue),\n        cipher.final(),\n        cipher.getAuthTag()\n    ]);\n    const encryptedPassword = import_node_crypto2.default.publicEncrypt(publicKey, passwordBuffer);\n    return {\n        encryptedPassword: encryptedPassword.toString(\"base64\"),\n        encryptedValue: encryptedValue.toString(\"base64\")\n    };\n}\n__name(publicEncrypt, \"publicEncrypt\");\nfunction privateDecrypt({ privateKey, encryptedPassword, encryptedValue }) {\n    const encryptedValueBuffer = Buffer.from(encryptedValue, \"base64\");\n    const encryptedPasswordBuffer = Buffer.from(encryptedPassword, \"base64\");\n    const passwordBuffer = import_node_crypto2.default.privateDecrypt(privateKey, encryptedPasswordBuffer);\n    if (passwordBuffer.length !== ENCRYPTION_KEY_LENGTH + ENCRYPTION_IV_LENGTH) {\n        throw new Error(\"privateDecrypt: Decryption failed, invalid password length!\");\n    }\n    const authTagBuffer = encryptedValueBuffer.slice(encryptedValueBuffer.length - ENCRYPTION_AUTH_TAG_LENGTH);\n    const encryptedDataBuffer = encryptedValueBuffer.slice(0, encryptedValueBuffer.length - ENCRYPTION_AUTH_TAG_LENGTH);\n    const encryptionKeyBuffer = passwordBuffer.slice(0, ENCRYPTION_KEY_LENGTH);\n    const initVectorBuffer = passwordBuffer.slice(ENCRYPTION_KEY_LENGTH);\n    const decipher = import_node_crypto2.default.createDecipheriv(ENCRYPTION_ALGORITHM, encryptionKeyBuffer, initVectorBuffer);\n    decipher.setAuthTag(authTagBuffer);\n    return Buffer.concat([\n        decipher.update(encryptedDataBuffer),\n        decipher.final()\n    ]).toString(\"utf-8\");\n}\n__name(privateDecrypt, \"privateDecrypt\");\n// src/url_params_utils.ts\nfunction separateImports(code) {\n    const lines = code.split(\"\\n\");\n    return {\n        code: lines.filter((line)=>!line.trim().startsWith(\"import\")).join(\"\\n\"),\n        imports: lines.filter((line)=>line.trim().startsWith(\"import\")).join(\"\\n\")\n    };\n}\n__name(separateImports, \"separateImports\");\n// src/code_hash_manager.ts\nvar import_node_crypto3 = __webpack_require__(/*! crypto */ \"crypto\");\nvar CodeHashMetaKey = /* @__PURE__ */ ((CodeHashMetaKey2)=>{\n    CodeHashMetaKey2[\"VERSION\"] = \"v\";\n    CodeHashMetaKey2[\"USER\"] = \"u\";\n    return CodeHashMetaKey2;\n})(CodeHashMetaKey || {});\nvar _CodeHashManager = class _CodeHashManager {\n    constructor(secret){\n        this.secret = secret;\n    }\n    /**\n   * Encodes object (e.g. input for actor) to a string hash and uses the `secret` to sign the hash.\n   */ encode(data, userId) {\n        const meta = {\n            [\"u\" /* USER */ ]: userId,\n            [\"v\" /* VERSION */ ]: _CodeHashManager.VERSION\n        };\n        const metaBase64 = this.toBase64(JSON.stringify(meta));\n        const inputBase64 = this.toBase64(JSON.stringify(data));\n        const dataToSign = [\n            metaBase64,\n            inputBase64\n        ].join(_CodeHashManager.SECTION_SEPARATOR);\n        const signature = this.generateSignature(dataToSign);\n        const signatureBase64 = this.toBase64(signature);\n        const parts = [\n            metaBase64,\n            inputBase64,\n            signatureBase64\n        ];\n        return parts.join(_CodeHashManager.SECTION_SEPARATOR);\n    }\n    decode(urlHash) {\n        const parts = urlHash.split(_CodeHashManager.SECTION_SEPARATOR);\n        const dataToSign = parts.slice(0, 2).join(_CodeHashManager.SECTION_SEPARATOR);\n        const meta = JSON.parse(this.fromBase64(parts[0]).toString());\n        const data = JSON.parse(this.fromBase64(parts[1]).toString());\n        const signature = this.fromBase64(parts[2]);\n        const expectedSignature = this.generateSignature(dataToSign);\n        const isSignatureValid = (0, import_node_crypto3.timingSafeEqual)(signature, expectedSignature);\n        return {\n            data,\n            meta: {\n                userId: meta[\"u\" /* USER */ ],\n                version: meta[\"v\" /* VERSION */ ],\n                isSignatureValid\n            }\n        };\n    }\n    toBase64(data) {\n        return Buffer.from(data).toString(\"base64url\");\n    }\n    fromBase64(encoded) {\n        return Buffer.from(encoded, \"base64url\");\n    }\n    generateSignature(data) {\n        return (0, import_node_crypto3.createHmac)(\"sha256\", this.secret).update(data).digest();\n    }\n};\n__name(_CodeHashManager, \"CodeHashManager\");\n__publicField(_CodeHashManager, \"SECTION_SEPARATOR\", \".\");\n__publicField(_CodeHashManager, \"VERSION\", 1);\nvar CodeHashManager = _CodeHashManager;\n// src/hmac.ts\nvar import_node_crypto4 = __toESM(__webpack_require__(/*! crypto */ \"crypto\"));\nvar CHARSET = \"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\".split(\"\");\nfunction encodeBase62(num) {\n    if (num === 0n) {\n        return CHARSET[0];\n    }\n    let res = \"\";\n    while(num > 0n){\n        res = CHARSET[Number(num % 62n)] + res;\n        num /= 62n;\n    }\n    return res;\n}\n__name(encodeBase62, \"encodeBase62\");\nfunction createHmacSignature(secretKey, message) {\n    const signature = import_node_crypto4.default.createHmac(\"sha256\", secretKey).update(message).digest(\"hex\").substring(0, 30);\n    return encodeBase62(BigInt(`0x${signature}`));\n}\n__name(createHmacSignature, \"createHmacSignature\");\n// src/storages.ts\nfunction createStorageContentSignature({ resourceId, urlSigningSecretKey, expiresInMillis, version = 0 }) {\n    const expiresAt = expiresInMillis ? /* @__PURE__ */ new Date().getTime() + expiresInMillis : 0;\n    const hmac = createHmacSignature(urlSigningSecretKey, `${version}.${expiresAt}.${resourceId}`);\n    return Buffer.from(`${version}.${expiresAt}.${hmac}`).toString(\"base64url\");\n}\n__name(createStorageContentSignature, \"createStorageContentSignature\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0); /*!\n * This module contains various server utility and helper functions.\n * Note that it automatically exports functions from utilities.client.js\n *\n * Author: Jan Curn (<EMAIL>)\n * Copyright(c) 2015 Apify. All rights reserved.\n *\n */  /*!\n * This module contains various client-side utility and helper functions.\n *\n * Author: Jan Curn (<EMAIL>)\n * Copyright(c) 2016 Apify. All rights reserved.\n *\n */  //# sourceMappingURL=index.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@apify/utilities/cjs/index.cjs\n");

/***/ })

};
;