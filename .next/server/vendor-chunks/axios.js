"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/axios";
exports.ids = ["vendor-chunks/axios"];
exports.modules = {

/***/ "(rsc)/./node_modules/axios/dist/node/axios.cjs":
/*!************************************************!*\
  !*** ./node_modules/axios/dist/node/axios.cjs ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*! Axios v1.11.0 Copyright (c) 2025 Matt Zabriskie and contributors */ \nconst FormData$1 = __webpack_require__(/*! form-data */ \"(rsc)/./node_modules/form-data/lib/form_data.js\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst proxyFromEnv = __webpack_require__(/*! proxy-from-env */ \"(rsc)/./node_modules/proxy-from-env/index.js\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst followRedirects = __webpack_require__(/*! follow-redirects */ \"(rsc)/./node_modules/follow-redirects/index.js\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst stream = __webpack_require__(/*! stream */ \"stream\");\nconst events = __webpack_require__(/*! events */ \"events\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === \"object\" && \"default\" in e ? e : {\n        \"default\": e\n    };\n}\nconst FormData__default = /*#__PURE__*/ _interopDefaultLegacy(FormData$1);\nconst crypto__default = /*#__PURE__*/ _interopDefaultLegacy(crypto);\nconst url__default = /*#__PURE__*/ _interopDefaultLegacy(url);\nconst proxyFromEnv__default = /*#__PURE__*/ _interopDefaultLegacy(proxyFromEnv);\nconst http__default = /*#__PURE__*/ _interopDefaultLegacy(http);\nconst https__default = /*#__PURE__*/ _interopDefaultLegacy(https);\nconst util__default = /*#__PURE__*/ _interopDefaultLegacy(util);\nconst followRedirects__default = /*#__PURE__*/ _interopDefaultLegacy(followRedirects);\nconst zlib__default = /*#__PURE__*/ _interopDefaultLegacy(zlib);\nconst stream__default = /*#__PURE__*/ _interopDefaultLegacy(stream);\nfunction bind(fn, thisArg) {\n    return function wrap() {\n        return fn.apply(thisArg, arguments);\n    };\n}\n// utils is a library of generic helper functions non-specific to axios\nconst { toString } = Object.prototype;\nconst { getPrototypeOf } = Object;\nconst { iterator, toStringTag } = Symbol;\nconst kindOf = ((cache)=>(thing)=>{\n        const str = toString.call(thing);\n        return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());\n    })(Object.create(null));\nconst kindOfTest = (type)=>{\n    type = type.toLowerCase();\n    return (thing)=>kindOf(thing) === type;\n};\nconst typeOfTest = (type)=>(thing)=>typeof thing === type;\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n *\n * @returns {boolean} True if value is an Array, otherwise false\n */ const { isArray } = Array;\n/**\n * Determine if a value is undefined\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if the value is undefined, otherwise false\n */ const isUndefined = typeOfTest(\"undefined\");\n/**\n * Determine if a value is a Buffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Buffer, otherwise false\n */ function isBuffer(val) {\n    return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);\n}\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */ const isArrayBuffer = kindOfTest(\"ArrayBuffer\");\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */ function isArrayBufferView(val) {\n    let result;\n    if (typeof ArrayBuffer !== \"undefined\" && ArrayBuffer.isView) {\n        result = ArrayBuffer.isView(val);\n    } else {\n        result = val && val.buffer && isArrayBuffer(val.buffer);\n    }\n    return result;\n}\n/**\n * Determine if a value is a String\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a String, otherwise false\n */ const isString = typeOfTest(\"string\");\n/**\n * Determine if a value is a Function\n *\n * @param {*} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */ const isFunction = typeOfTest(\"function\");\n/**\n * Determine if a value is a Number\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Number, otherwise false\n */ const isNumber = typeOfTest(\"number\");\n/**\n * Determine if a value is an Object\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an Object, otherwise false\n */ const isObject = (thing)=>thing !== null && typeof thing === \"object\";\n/**\n * Determine if a value is a Boolean\n *\n * @param {*} thing The value to test\n * @returns {boolean} True if value is a Boolean, otherwise false\n */ const isBoolean = (thing)=>thing === true || thing === false;\n/**\n * Determine if a value is a plain Object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a plain Object, otherwise false\n */ const isPlainObject = (val)=>{\n    if (kindOf(val) !== \"object\") {\n        return false;\n    }\n    const prototype = getPrototypeOf(val);\n    return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(toStringTag in val) && !(iterator in val);\n};\n/**\n * Determine if a value is an empty object (safely handles Buffers)\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is an empty object, otherwise false\n */ const isEmptyObject = (val)=>{\n    // Early return for non-objects or Buffers to prevent RangeError\n    if (!isObject(val) || isBuffer(val)) {\n        return false;\n    }\n    try {\n        return Object.keys(val).length === 0 && Object.getPrototypeOf(val) === Object.prototype;\n    } catch (e) {\n        // Fallback for any other objects that might cause RangeError with Object.keys()\n        return false;\n    }\n};\n/**\n * Determine if a value is a Date\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Date, otherwise false\n */ const isDate = kindOfTest(\"Date\");\n/**\n * Determine if a value is a File\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */ const isFile = kindOfTest(\"File\");\n/**\n * Determine if a value is a Blob\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Blob, otherwise false\n */ const isBlob = kindOfTest(\"Blob\");\n/**\n * Determine if a value is a FileList\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */ const isFileList = kindOfTest(\"FileList\");\n/**\n * Determine if a value is a Stream\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Stream, otherwise false\n */ const isStream = (val)=>isObject(val) && isFunction(val.pipe);\n/**\n * Determine if a value is a FormData\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an FormData, otherwise false\n */ const isFormData = (thing)=>{\n    let kind;\n    return thing && (typeof FormData === \"function\" && thing instanceof FormData || isFunction(thing.append) && ((kind = kindOf(thing)) === \"formdata\" || // detect form-data instance\n    kind === \"object\" && isFunction(thing.toString) && thing.toString() === \"[object FormData]\"));\n};\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */ const isURLSearchParams = kindOfTest(\"URLSearchParams\");\nconst [isReadableStream, isRequest, isResponse, isHeaders] = [\n    \"ReadableStream\",\n    \"Request\",\n    \"Response\",\n    \"Headers\"\n].map(kindOfTest);\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n *\n * @returns {String} The String freed of excess whitespace\n */ const trim = (str)=>str.trim ? str.trim() : str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, \"\");\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n *\n * @param {Boolean} [allOwnKeys = false]\n * @returns {any}\n */ function forEach(obj, fn, { allOwnKeys = false } = {}) {\n    // Don't bother if no value provided\n    if (obj === null || typeof obj === \"undefined\") {\n        return;\n    }\n    let i;\n    let l;\n    // Force an array if not already something iterable\n    if (typeof obj !== \"object\") {\n        /*eslint no-param-reassign:0*/ obj = [\n            obj\n        ];\n    }\n    if (isArray(obj)) {\n        // Iterate over array values\n        for(i = 0, l = obj.length; i < l; i++){\n            fn.call(null, obj[i], i, obj);\n        }\n    } else {\n        // Buffer check\n        if (isBuffer(obj)) {\n            return;\n        }\n        // Iterate over object keys\n        const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);\n        const len = keys.length;\n        let key;\n        for(i = 0; i < len; i++){\n            key = keys[i];\n            fn.call(null, obj[key], key, obj);\n        }\n    }\n}\nfunction findKey(obj, key) {\n    if (isBuffer(obj)) {\n        return null;\n    }\n    key = key.toLowerCase();\n    const keys = Object.keys(obj);\n    let i = keys.length;\n    let _key;\n    while(i-- > 0){\n        _key = keys[i];\n        if (key === _key.toLowerCase()) {\n            return _key;\n        }\n    }\n    return null;\n}\nconst _global = (()=>{\n    /*eslint no-undef:0*/ if (typeof globalThis !== \"undefined\") return globalThis;\n    return typeof self !== \"undefined\" ? self :  false ? 0 : global;\n})();\nconst isContextDefined = (context)=>!isUndefined(context) && context !== _global;\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n *\n * @returns {Object} Result of all merge properties\n */ function merge() {\n    const { caseless } = isContextDefined(this) && this || {};\n    const result = {};\n    const assignValue = (val, key)=>{\n        const targetKey = caseless && findKey(result, key) || key;\n        if (isPlainObject(result[targetKey]) && isPlainObject(val)) {\n            result[targetKey] = merge(result[targetKey], val);\n        } else if (isPlainObject(val)) {\n            result[targetKey] = merge({}, val);\n        } else if (isArray(val)) {\n            result[targetKey] = val.slice();\n        } else {\n            result[targetKey] = val;\n        }\n    };\n    for(let i = 0, l = arguments.length; i < l; i++){\n        arguments[i] && forEach(arguments[i], assignValue);\n    }\n    return result;\n}\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n *\n * @param {Boolean} [allOwnKeys]\n * @returns {Object} The resulting value of object a\n */ const extend = (a, b, thisArg, { allOwnKeys } = {})=>{\n    forEach(b, (val, key)=>{\n        if (thisArg && isFunction(val)) {\n            a[key] = bind(val, thisArg);\n        } else {\n            a[key] = val;\n        }\n    }, {\n        allOwnKeys\n    });\n    return a;\n};\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n *\n * @returns {string} content value without BOM\n */ const stripBOM = (content)=>{\n    if (content.charCodeAt(0) === 0xFEFF) {\n        content = content.slice(1);\n    }\n    return content;\n};\n/**\n * Inherit the prototype methods from one constructor into another\n * @param {function} constructor\n * @param {function} superConstructor\n * @param {object} [props]\n * @param {object} [descriptors]\n *\n * @returns {void}\n */ const inherits = (constructor, superConstructor, props, descriptors)=>{\n    constructor.prototype = Object.create(superConstructor.prototype, descriptors);\n    constructor.prototype.constructor = constructor;\n    Object.defineProperty(constructor, \"super\", {\n        value: superConstructor.prototype\n    });\n    props && Object.assign(constructor.prototype, props);\n};\n/**\n * Resolve object with deep prototype chain to a flat object\n * @param {Object} sourceObj source object\n * @param {Object} [destObj]\n * @param {Function|Boolean} [filter]\n * @param {Function} [propFilter]\n *\n * @returns {Object}\n */ const toFlatObject = (sourceObj, destObj, filter, propFilter)=>{\n    let props;\n    let i;\n    let prop;\n    const merged = {};\n    destObj = destObj || {};\n    // eslint-disable-next-line no-eq-null,eqeqeq\n    if (sourceObj == null) return destObj;\n    do {\n        props = Object.getOwnPropertyNames(sourceObj);\n        i = props.length;\n        while(i-- > 0){\n            prop = props[i];\n            if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {\n                destObj[prop] = sourceObj[prop];\n                merged[prop] = true;\n            }\n        }\n        sourceObj = filter !== false && getPrototypeOf(sourceObj);\n    }while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);\n    return destObj;\n};\n/**\n * Determines whether a string ends with the characters of a specified string\n *\n * @param {String} str\n * @param {String} searchString\n * @param {Number} [position= 0]\n *\n * @returns {boolean}\n */ const endsWith = (str, searchString, position)=>{\n    str = String(str);\n    if (position === undefined || position > str.length) {\n        position = str.length;\n    }\n    position -= searchString.length;\n    const lastIndex = str.indexOf(searchString, position);\n    return lastIndex !== -1 && lastIndex === position;\n};\n/**\n * Returns new array from array like object or null if failed\n *\n * @param {*} [thing]\n *\n * @returns {?Array}\n */ const toArray = (thing)=>{\n    if (!thing) return null;\n    if (isArray(thing)) return thing;\n    let i = thing.length;\n    if (!isNumber(i)) return null;\n    const arr = new Array(i);\n    while(i-- > 0){\n        arr[i] = thing[i];\n    }\n    return arr;\n};\n/**\n * Checking if the Uint8Array exists and if it does, it returns a function that checks if the\n * thing passed in is an instance of Uint8Array\n *\n * @param {TypedArray}\n *\n * @returns {Array}\n */ // eslint-disable-next-line func-names\nconst isTypedArray = ((TypedArray)=>{\n    // eslint-disable-next-line func-names\n    return (thing)=>{\n        return TypedArray && thing instanceof TypedArray;\n    };\n})(typeof Uint8Array !== \"undefined\" && getPrototypeOf(Uint8Array));\n/**\n * For each entry in the object, call the function with the key and value.\n *\n * @param {Object<any, any>} obj - The object to iterate over.\n * @param {Function} fn - The function to call for each entry.\n *\n * @returns {void}\n */ const forEachEntry = (obj, fn)=>{\n    const generator = obj && obj[iterator];\n    const _iterator = generator.call(obj);\n    let result;\n    while((result = _iterator.next()) && !result.done){\n        const pair = result.value;\n        fn.call(obj, pair[0], pair[1]);\n    }\n};\n/**\n * It takes a regular expression and a string, and returns an array of all the matches\n *\n * @param {string} regExp - The regular expression to match against.\n * @param {string} str - The string to search.\n *\n * @returns {Array<boolean>}\n */ const matchAll = (regExp, str)=>{\n    let matches;\n    const arr = [];\n    while((matches = regExp.exec(str)) !== null){\n        arr.push(matches);\n    }\n    return arr;\n};\n/* Checking if the kindOfTest function returns true when passed an HTMLFormElement. */ const isHTMLForm = kindOfTest(\"HTMLFormElement\");\nconst toCamelCase = (str)=>{\n    return str.toLowerCase().replace(/[-_\\s]([a-z\\d])(\\w*)/g, function replacer(m, p1, p2) {\n        return p1.toUpperCase() + p2;\n    });\n};\n/* Creating a function that will check if an object has a property. */ const hasOwnProperty = (({ hasOwnProperty })=>(obj, prop)=>hasOwnProperty.call(obj, prop))(Object.prototype);\n/**\n * Determine if a value is a RegExp object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a RegExp object, otherwise false\n */ const isRegExp = kindOfTest(\"RegExp\");\nconst reduceDescriptors = (obj, reducer)=>{\n    const descriptors = Object.getOwnPropertyDescriptors(obj);\n    const reducedDescriptors = {};\n    forEach(descriptors, (descriptor, name)=>{\n        let ret;\n        if ((ret = reducer(descriptor, name, obj)) !== false) {\n            reducedDescriptors[name] = ret || descriptor;\n        }\n    });\n    Object.defineProperties(obj, reducedDescriptors);\n};\n/**\n * Makes all methods read-only\n * @param {Object} obj\n */ const freezeMethods = (obj)=>{\n    reduceDescriptors(obj, (descriptor, name)=>{\n        // skip restricted props in strict mode\n        if (isFunction(obj) && [\n            \"arguments\",\n            \"caller\",\n            \"callee\"\n        ].indexOf(name) !== -1) {\n            return false;\n        }\n        const value = obj[name];\n        if (!isFunction(value)) return;\n        descriptor.enumerable = false;\n        if (\"writable\" in descriptor) {\n            descriptor.writable = false;\n            return;\n        }\n        if (!descriptor.set) {\n            descriptor.set = ()=>{\n                throw Error(\"Can not rewrite read-only method '\" + name + \"'\");\n            };\n        }\n    });\n};\nconst toObjectSet = (arrayOrString, delimiter)=>{\n    const obj = {};\n    const define = (arr)=>{\n        arr.forEach((value)=>{\n            obj[value] = true;\n        });\n    };\n    isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));\n    return obj;\n};\nconst noop = ()=>{};\nconst toFiniteNumber = (value, defaultValue)=>{\n    return value != null && Number.isFinite(value = +value) ? value : defaultValue;\n};\n/**\n * If the thing is a FormData object, return true, otherwise return false.\n *\n * @param {unknown} thing - The thing to check.\n *\n * @returns {boolean}\n */ function isSpecCompliantForm(thing) {\n    return !!(thing && isFunction(thing.append) && thing[toStringTag] === \"FormData\" && thing[iterator]);\n}\nconst toJSONObject = (obj)=>{\n    const stack = new Array(10);\n    const visit = (source, i)=>{\n        if (isObject(source)) {\n            if (stack.indexOf(source) >= 0) {\n                return;\n            }\n            //Buffer check\n            if (isBuffer(source)) {\n                return source;\n            }\n            if (!(\"toJSON\" in source)) {\n                stack[i] = source;\n                const target = isArray(source) ? [] : {};\n                forEach(source, (value, key)=>{\n                    const reducedValue = visit(value, i + 1);\n                    !isUndefined(reducedValue) && (target[key] = reducedValue);\n                });\n                stack[i] = undefined;\n                return target;\n            }\n        }\n        return source;\n    };\n    return visit(obj, 0);\n};\nconst isAsyncFn = kindOfTest(\"AsyncFunction\");\nconst isThenable = (thing)=>thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);\n// original code\n// https://github.com/DigitalBrainJS/AxiosPromise/blob/16deab13710ec09779922131f3fa5954320f83ab/lib/utils.js#L11-L34\nconst _setImmediate = ((setImmediateSupported, postMessageSupported)=>{\n    if (setImmediateSupported) {\n        return setImmediate;\n    }\n    return postMessageSupported ? ((token, callbacks)=>{\n        _global.addEventListener(\"message\", ({ source, data })=>{\n            if (source === _global && data === token) {\n                callbacks.length && callbacks.shift()();\n            }\n        }, false);\n        return (cb)=>{\n            callbacks.push(cb);\n            _global.postMessage(token, \"*\");\n        };\n    })(`axios@${Math.random()}`, []) : (cb)=>setTimeout(cb);\n})(typeof setImmediate === \"function\", isFunction(_global.postMessage));\nconst asap = typeof queueMicrotask !== \"undefined\" ? queueMicrotask.bind(_global) : typeof process !== \"undefined\" && process.nextTick || _setImmediate;\n// *********************\nconst isIterable = (thing)=>thing != null && isFunction(thing[iterator]);\nconst utils$1 = {\n    isArray,\n    isArrayBuffer,\n    isBuffer,\n    isFormData,\n    isArrayBufferView,\n    isString,\n    isNumber,\n    isBoolean,\n    isObject,\n    isPlainObject,\n    isEmptyObject,\n    isReadableStream,\n    isRequest,\n    isResponse,\n    isHeaders,\n    isUndefined,\n    isDate,\n    isFile,\n    isBlob,\n    isRegExp,\n    isFunction,\n    isStream,\n    isURLSearchParams,\n    isTypedArray,\n    isFileList,\n    forEach,\n    merge,\n    extend,\n    trim,\n    stripBOM,\n    inherits,\n    toFlatObject,\n    kindOf,\n    kindOfTest,\n    endsWith,\n    toArray,\n    forEachEntry,\n    matchAll,\n    isHTMLForm,\n    hasOwnProperty,\n    hasOwnProp: hasOwnProperty,\n    reduceDescriptors,\n    freezeMethods,\n    toObjectSet,\n    toCamelCase,\n    noop,\n    toFiniteNumber,\n    findKey,\n    global: _global,\n    isContextDefined,\n    isSpecCompliantForm,\n    toJSONObject,\n    isAsyncFn,\n    isThenable,\n    setImmediate: _setImmediate,\n    asap,\n    isIterable\n};\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n *\n * @returns {Error} The created error.\n */ function AxiosError(message, code, config, request, response) {\n    Error.call(this);\n    if (Error.captureStackTrace) {\n        Error.captureStackTrace(this, this.constructor);\n    } else {\n        this.stack = new Error().stack;\n    }\n    this.message = message;\n    this.name = \"AxiosError\";\n    code && (this.code = code);\n    config && (this.config = config);\n    request && (this.request = request);\n    if (response) {\n        this.response = response;\n        this.status = response.status ? response.status : null;\n    }\n}\nutils$1.inherits(AxiosError, Error, {\n    toJSON: function toJSON() {\n        return {\n            // Standard\n            message: this.message,\n            name: this.name,\n            // Microsoft\n            description: this.description,\n            number: this.number,\n            // Mozilla\n            fileName: this.fileName,\n            lineNumber: this.lineNumber,\n            columnNumber: this.columnNumber,\n            stack: this.stack,\n            // Axios\n            config: utils$1.toJSONObject(this.config),\n            code: this.code,\n            status: this.status\n        };\n    }\n});\nconst prototype$1 = AxiosError.prototype;\nconst descriptors = {};\n[\n    \"ERR_BAD_OPTION_VALUE\",\n    \"ERR_BAD_OPTION\",\n    \"ECONNABORTED\",\n    \"ETIMEDOUT\",\n    \"ERR_NETWORK\",\n    \"ERR_FR_TOO_MANY_REDIRECTS\",\n    \"ERR_DEPRECATED\",\n    \"ERR_BAD_RESPONSE\",\n    \"ERR_BAD_REQUEST\",\n    \"ERR_CANCELED\",\n    \"ERR_NOT_SUPPORT\",\n    \"ERR_INVALID_URL\"\n].forEach((code)=>{\n    descriptors[code] = {\n        value: code\n    };\n});\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype$1, \"isAxiosError\", {\n    value: true\n});\n// eslint-disable-next-line func-names\nAxiosError.from = (error, code, config, request, response, customProps)=>{\n    const axiosError = Object.create(prototype$1);\n    utils$1.toFlatObject(error, axiosError, function filter(obj) {\n        return obj !== Error.prototype;\n    }, (prop)=>{\n        return prop !== \"isAxiosError\";\n    });\n    AxiosError.call(axiosError, error.message, code, config, request, response);\n    axiosError.cause = error;\n    axiosError.name = error.name;\n    customProps && Object.assign(axiosError, customProps);\n    return axiosError;\n};\n/**\n * Determines if the given thing is a array or js object.\n *\n * @param {string} thing - The object or array to be visited.\n *\n * @returns {boolean}\n */ function isVisitable(thing) {\n    return utils$1.isPlainObject(thing) || utils$1.isArray(thing);\n}\n/**\n * It removes the brackets from the end of a string\n *\n * @param {string} key - The key of the parameter.\n *\n * @returns {string} the key without the brackets.\n */ function removeBrackets(key) {\n    return utils$1.endsWith(key, \"[]\") ? key.slice(0, -2) : key;\n}\n/**\n * It takes a path, a key, and a boolean, and returns a string\n *\n * @param {string} path - The path to the current key.\n * @param {string} key - The key of the current object being iterated over.\n * @param {string} dots - If true, the key will be rendered with dots instead of brackets.\n *\n * @returns {string} The path to the current key.\n */ function renderKey(path, key, dots) {\n    if (!path) return key;\n    return path.concat(key).map(function each(token, i) {\n        // eslint-disable-next-line no-param-reassign\n        token = removeBrackets(token);\n        return !dots && i ? \"[\" + token + \"]\" : token;\n    }).join(dots ? \".\" : \"\");\n}\n/**\n * If the array is an array and none of its elements are visitable, then it's a flat array.\n *\n * @param {Array<any>} arr - The array to check\n *\n * @returns {boolean}\n */ function isFlatArray(arr) {\n    return utils$1.isArray(arr) && !arr.some(isVisitable);\n}\nconst predicates = utils$1.toFlatObject(utils$1, {}, null, function filter(prop) {\n    return /^is[A-Z]/.test(prop);\n});\n/**\n * Convert a data object to FormData\n *\n * @param {Object} obj\n * @param {?Object} [formData]\n * @param {?Object} [options]\n * @param {Function} [options.visitor]\n * @param {Boolean} [options.metaTokens = true]\n * @param {Boolean} [options.dots = false]\n * @param {?Boolean} [options.indexes = false]\n *\n * @returns {Object}\n **/ /**\n * It converts an object into a FormData object\n *\n * @param {Object<any, any>} obj - The object to convert to form data.\n * @param {string} formData - The FormData object to append to.\n * @param {Object<string, any>} options\n *\n * @returns\n */ function toFormData(obj, formData, options) {\n    if (!utils$1.isObject(obj)) {\n        throw new TypeError(\"target must be an object\");\n    }\n    // eslint-disable-next-line no-param-reassign\n    formData = formData || new (FormData__default[\"default\"] || FormData)();\n    // eslint-disable-next-line no-param-reassign\n    options = utils$1.toFlatObject(options, {\n        metaTokens: true,\n        dots: false,\n        indexes: false\n    }, false, function defined(option, source) {\n        // eslint-disable-next-line no-eq-null,eqeqeq\n        return !utils$1.isUndefined(source[option]);\n    });\n    const metaTokens = options.metaTokens;\n    // eslint-disable-next-line no-use-before-define\n    const visitor = options.visitor || defaultVisitor;\n    const dots = options.dots;\n    const indexes = options.indexes;\n    const _Blob = options.Blob || typeof Blob !== \"undefined\" && Blob;\n    const useBlob = _Blob && utils$1.isSpecCompliantForm(formData);\n    if (!utils$1.isFunction(visitor)) {\n        throw new TypeError(\"visitor must be a function\");\n    }\n    function convertValue(value) {\n        if (value === null) return \"\";\n        if (utils$1.isDate(value)) {\n            return value.toISOString();\n        }\n        if (utils$1.isBoolean(value)) {\n            return value.toString();\n        }\n        if (!useBlob && utils$1.isBlob(value)) {\n            throw new AxiosError(\"Blob is not supported. Use a Buffer instead.\");\n        }\n        if (utils$1.isArrayBuffer(value) || utils$1.isTypedArray(value)) {\n            return useBlob && typeof Blob === \"function\" ? new Blob([\n                value\n            ]) : Buffer.from(value);\n        }\n        return value;\n    }\n    /**\n   * Default visitor.\n   *\n   * @param {*} value\n   * @param {String|Number} key\n   * @param {Array<String|Number>} path\n   * @this {FormData}\n   *\n   * @returns {boolean} return true to visit the each prop of the value recursively\n   */ function defaultVisitor(value, key, path) {\n        let arr = value;\n        if (value && !path && typeof value === \"object\") {\n            if (utils$1.endsWith(key, \"{}\")) {\n                // eslint-disable-next-line no-param-reassign\n                key = metaTokens ? key : key.slice(0, -2);\n                // eslint-disable-next-line no-param-reassign\n                value = JSON.stringify(value);\n            } else if (utils$1.isArray(value) && isFlatArray(value) || (utils$1.isFileList(value) || utils$1.endsWith(key, \"[]\")) && (arr = utils$1.toArray(value))) {\n                // eslint-disable-next-line no-param-reassign\n                key = removeBrackets(key);\n                arr.forEach(function each(el, index) {\n                    !(utils$1.isUndefined(el) || el === null) && formData.append(// eslint-disable-next-line no-nested-ternary\n                    indexes === true ? renderKey([\n                        key\n                    ], index, dots) : indexes === null ? key : key + \"[]\", convertValue(el));\n                });\n                return false;\n            }\n        }\n        if (isVisitable(value)) {\n            return true;\n        }\n        formData.append(renderKey(path, key, dots), convertValue(value));\n        return false;\n    }\n    const stack = [];\n    const exposedHelpers = Object.assign(predicates, {\n        defaultVisitor,\n        convertValue,\n        isVisitable\n    });\n    function build(value, path) {\n        if (utils$1.isUndefined(value)) return;\n        if (stack.indexOf(value) !== -1) {\n            throw Error(\"Circular reference detected in \" + path.join(\".\"));\n        }\n        stack.push(value);\n        utils$1.forEach(value, function each(el, key) {\n            const result = !(utils$1.isUndefined(el) || el === null) && visitor.call(formData, el, utils$1.isString(key) ? key.trim() : key, path, exposedHelpers);\n            if (result === true) {\n                build(el, path ? path.concat(key) : [\n                    key\n                ]);\n            }\n        });\n        stack.pop();\n    }\n    if (!utils$1.isObject(obj)) {\n        throw new TypeError(\"data must be an object\");\n    }\n    build(obj);\n    return formData;\n}\n/**\n * It encodes a string by replacing all characters that are not in the unreserved set with\n * their percent-encoded equivalents\n *\n * @param {string} str - The string to encode.\n *\n * @returns {string} The encoded string.\n */ function encode$1(str) {\n    const charMap = {\n        \"!\": \"%21\",\n        \"'\": \"%27\",\n        \"(\": \"%28\",\n        \")\": \"%29\",\n        \"~\": \"%7E\",\n        \"%20\": \"+\",\n        \"%00\": \"\\x00\"\n    };\n    return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {\n        return charMap[match];\n    });\n}\n/**\n * It takes a params object and converts it to a FormData object\n *\n * @param {Object<string, any>} params - The parameters to be converted to a FormData object.\n * @param {Object<string, any>} options - The options object passed to the Axios constructor.\n *\n * @returns {void}\n */ function AxiosURLSearchParams(params, options) {\n    this._pairs = [];\n    params && toFormData(params, this, options);\n}\nconst prototype = AxiosURLSearchParams.prototype;\nprototype.append = function append(name, value) {\n    this._pairs.push([\n        name,\n        value\n    ]);\n};\nprototype.toString = function toString(encoder) {\n    const _encode = encoder ? function(value) {\n        return encoder.call(this, value, encode$1);\n    } : encode$1;\n    return this._pairs.map(function each(pair) {\n        return _encode(pair[0]) + \"=\" + _encode(pair[1]);\n    }, \"\").join(\"&\");\n};\n/**\n * It replaces all instances of the characters `:`, `$`, `,`, `+`, `[`, and `]` with their\n * URI encoded counterparts\n *\n * @param {string} val The value to be encoded.\n *\n * @returns {string} The encoded value.\n */ function encode(val) {\n    return encodeURIComponent(val).replace(/%3A/gi, \":\").replace(/%24/g, \"$\").replace(/%2C/gi, \",\").replace(/%20/g, \"+\").replace(/%5B/gi, \"[\").replace(/%5D/gi, \"]\");\n}\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @param {?(object|Function)} options\n *\n * @returns {string} The formatted url\n */ function buildURL(url, params, options) {\n    /*eslint no-param-reassign:0*/ if (!params) {\n        return url;\n    }\n    const _encode = options && options.encode || encode;\n    if (utils$1.isFunction(options)) {\n        options = {\n            serialize: options\n        };\n    }\n    const serializeFn = options && options.serialize;\n    let serializedParams;\n    if (serializeFn) {\n        serializedParams = serializeFn(params, options);\n    } else {\n        serializedParams = utils$1.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams(params, options).toString(_encode);\n    }\n    if (serializedParams) {\n        const hashmarkIndex = url.indexOf(\"#\");\n        if (hashmarkIndex !== -1) {\n            url = url.slice(0, hashmarkIndex);\n        }\n        url += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + serializedParams;\n    }\n    return url;\n}\nclass InterceptorManager {\n    constructor(){\n        this.handlers = [];\n    }\n    /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */ use(fulfilled, rejected, options) {\n        this.handlers.push({\n            fulfilled,\n            rejected,\n            synchronous: options ? options.synchronous : false,\n            runWhen: options ? options.runWhen : null\n        });\n        return this.handlers.length - 1;\n    }\n    /**\n   * Remove an interceptor from the stack\n   *\n   * @param {Number} id The ID that was returned by `use`\n   *\n   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n   */ eject(id) {\n        if (this.handlers[id]) {\n            this.handlers[id] = null;\n        }\n    }\n    /**\n   * Clear all interceptors from the stack\n   *\n   * @returns {void}\n   */ clear() {\n        if (this.handlers) {\n            this.handlers = [];\n        }\n    }\n    /**\n   * Iterate over all the registered interceptors\n   *\n   * This method is particularly useful for skipping over any\n   * interceptors that may have become `null` calling `eject`.\n   *\n   * @param {Function} fn The function to call for each interceptor\n   *\n   * @returns {void}\n   */ forEach(fn) {\n        utils$1.forEach(this.handlers, function forEachHandler(h) {\n            if (h !== null) {\n                fn(h);\n            }\n        });\n    }\n}\nconst InterceptorManager$1 = InterceptorManager;\nconst transitionalDefaults = {\n    silentJSONParsing: true,\n    forcedJSONParsing: true,\n    clarifyTimeoutError: false\n};\nconst URLSearchParams = url__default[\"default\"].URLSearchParams;\nconst ALPHA = \"abcdefghijklmnopqrstuvwxyz\";\nconst DIGIT = \"0123456789\";\nconst ALPHABET = {\n    DIGIT,\n    ALPHA,\n    ALPHA_DIGIT: ALPHA + ALPHA.toUpperCase() + DIGIT\n};\nconst generateString = (size = 16, alphabet = ALPHABET.ALPHA_DIGIT)=>{\n    let str = \"\";\n    const { length } = alphabet;\n    const randomValues = new Uint32Array(size);\n    crypto__default[\"default\"].randomFillSync(randomValues);\n    for(let i = 0; i < size; i++){\n        str += alphabet[randomValues[i] % length];\n    }\n    return str;\n};\nconst platform$1 = {\n    isNode: true,\n    classes: {\n        URLSearchParams,\n        FormData: FormData__default[\"default\"],\n        Blob: typeof Blob !== \"undefined\" && Blob || null\n    },\n    ALPHABET,\n    generateString,\n    protocols: [\n        \"http\",\n        \"https\",\n        \"file\",\n        \"data\"\n    ]\n};\nconst hasBrowserEnv =  false && 0;\nconst _navigator = typeof navigator === \"object\" && navigator || undefined;\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n *\n * @returns {boolean}\n */ const hasStandardBrowserEnv = hasBrowserEnv && (!_navigator || [\n    \"ReactNative\",\n    \"NativeScript\",\n    \"NS\"\n].indexOf(_navigator.product) < 0);\n/**\n * Determine if we're running in a standard browser webWorker environment\n *\n * Although the `isStandardBrowserEnv` method indicates that\n * `allows axios to run in a web worker`, the WebWorker will still be\n * filtered out due to its judgment standard\n * `typeof window !== 'undefined' && typeof document !== 'undefined'`.\n * This leads to a problem when axios post `FormData` in webWorker\n */ const hasStandardBrowserWebWorkerEnv = (()=>{\n    return typeof WorkerGlobalScope !== \"undefined\" && // eslint-disable-next-line no-undef\n    self instanceof WorkerGlobalScope && typeof self.importScripts === \"function\";\n})();\nconst origin = hasBrowserEnv && window.location.href || \"http://localhost\";\nconst utils = /*#__PURE__*/ Object.freeze({\n    __proto__: null,\n    hasBrowserEnv: hasBrowserEnv,\n    hasStandardBrowserWebWorkerEnv: hasStandardBrowserWebWorkerEnv,\n    hasStandardBrowserEnv: hasStandardBrowserEnv,\n    navigator: _navigator,\n    origin: origin\n});\nconst platform = {\n    ...utils,\n    ...platform$1\n};\nfunction toURLEncodedForm(data, options) {\n    return toFormData(data, new platform.classes.URLSearchParams(), {\n        visitor: function(value, key, path, helpers) {\n            if (platform.isNode && utils$1.isBuffer(value)) {\n                this.append(key, value.toString(\"base64\"));\n                return false;\n            }\n            return helpers.defaultVisitor.apply(this, arguments);\n        },\n        ...options\n    });\n}\n/**\n * It takes a string like `foo[x][y][z]` and returns an array like `['foo', 'x', 'y', 'z']\n *\n * @param {string} name - The name of the property to get.\n *\n * @returns An array of strings.\n */ function parsePropPath(name) {\n    // foo[x][y][z]\n    // foo.x.y.z\n    // foo-x-y-z\n    // foo x y z\n    return utils$1.matchAll(/\\w+|\\[(\\w*)]/g, name).map((match)=>{\n        return match[0] === \"[]\" ? \"\" : match[1] || match[0];\n    });\n}\n/**\n * Convert an array to an object.\n *\n * @param {Array<any>} arr - The array to convert to an object.\n *\n * @returns An object with the same keys and values as the array.\n */ function arrayToObject(arr) {\n    const obj = {};\n    const keys = Object.keys(arr);\n    let i;\n    const len = keys.length;\n    let key;\n    for(i = 0; i < len; i++){\n        key = keys[i];\n        obj[key] = arr[key];\n    }\n    return obj;\n}\n/**\n * It takes a FormData object and returns a JavaScript object\n *\n * @param {string} formData The FormData object to convert to JSON.\n *\n * @returns {Object<string, any> | null} The converted object.\n */ function formDataToJSON(formData) {\n    function buildPath(path, value, target, index) {\n        let name = path[index++];\n        if (name === \"__proto__\") return true;\n        const isNumericKey = Number.isFinite(+name);\n        const isLast = index >= path.length;\n        name = !name && utils$1.isArray(target) ? target.length : name;\n        if (isLast) {\n            if (utils$1.hasOwnProp(target, name)) {\n                target[name] = [\n                    target[name],\n                    value\n                ];\n            } else {\n                target[name] = value;\n            }\n            return !isNumericKey;\n        }\n        if (!target[name] || !utils$1.isObject(target[name])) {\n            target[name] = [];\n        }\n        const result = buildPath(path, value, target[name], index);\n        if (result && utils$1.isArray(target[name])) {\n            target[name] = arrayToObject(target[name]);\n        }\n        return !isNumericKey;\n    }\n    if (utils$1.isFormData(formData) && utils$1.isFunction(formData.entries)) {\n        const obj = {};\n        utils$1.forEachEntry(formData, (name, value)=>{\n            buildPath(parsePropPath(name), value, obj, 0);\n        });\n        return obj;\n    }\n    return null;\n}\n/**\n * It takes a string, tries to parse it, and if it fails, it returns the stringified version\n * of the input\n *\n * @param {any} rawValue - The value to be stringified.\n * @param {Function} parser - A function that parses a string into a JavaScript object.\n * @param {Function} encoder - A function that takes a value and returns a string.\n *\n * @returns {string} A stringified version of the rawValue.\n */ function stringifySafely(rawValue, parser, encoder) {\n    if (utils$1.isString(rawValue)) {\n        try {\n            (parser || JSON.parse)(rawValue);\n            return utils$1.trim(rawValue);\n        } catch (e) {\n            if (e.name !== \"SyntaxError\") {\n                throw e;\n            }\n        }\n    }\n    return (encoder || JSON.stringify)(rawValue);\n}\nconst defaults = {\n    transitional: transitionalDefaults,\n    adapter: [\n        \"xhr\",\n        \"http\",\n        \"fetch\"\n    ],\n    transformRequest: [\n        function transformRequest(data, headers) {\n            const contentType = headers.getContentType() || \"\";\n            const hasJSONContentType = contentType.indexOf(\"application/json\") > -1;\n            const isObjectPayload = utils$1.isObject(data);\n            if (isObjectPayload && utils$1.isHTMLForm(data)) {\n                data = new FormData(data);\n            }\n            const isFormData = utils$1.isFormData(data);\n            if (isFormData) {\n                return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;\n            }\n            if (utils$1.isArrayBuffer(data) || utils$1.isBuffer(data) || utils$1.isStream(data) || utils$1.isFile(data) || utils$1.isBlob(data) || utils$1.isReadableStream(data)) {\n                return data;\n            }\n            if (utils$1.isArrayBufferView(data)) {\n                return data.buffer;\n            }\n            if (utils$1.isURLSearchParams(data)) {\n                headers.setContentType(\"application/x-www-form-urlencoded;charset=utf-8\", false);\n                return data.toString();\n            }\n            let isFileList;\n            if (isObjectPayload) {\n                if (contentType.indexOf(\"application/x-www-form-urlencoded\") > -1) {\n                    return toURLEncodedForm(data, this.formSerializer).toString();\n                }\n                if ((isFileList = utils$1.isFileList(data)) || contentType.indexOf(\"multipart/form-data\") > -1) {\n                    const _FormData = this.env && this.env.FormData;\n                    return toFormData(isFileList ? {\n                        \"files[]\": data\n                    } : data, _FormData && new _FormData(), this.formSerializer);\n                }\n            }\n            if (isObjectPayload || hasJSONContentType) {\n                headers.setContentType(\"application/json\", false);\n                return stringifySafely(data);\n            }\n            return data;\n        }\n    ],\n    transformResponse: [\n        function transformResponse(data) {\n            const transitional = this.transitional || defaults.transitional;\n            const forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n            const JSONRequested = this.responseType === \"json\";\n            if (utils$1.isResponse(data) || utils$1.isReadableStream(data)) {\n                return data;\n            }\n            if (data && utils$1.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {\n                const silentJSONParsing = transitional && transitional.silentJSONParsing;\n                const strictJSONParsing = !silentJSONParsing && JSONRequested;\n                try {\n                    return JSON.parse(data);\n                } catch (e) {\n                    if (strictJSONParsing) {\n                        if (e.name === \"SyntaxError\") {\n                            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n                        }\n                        throw e;\n                    }\n                }\n            }\n            return data;\n        }\n    ],\n    /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */ timeout: 0,\n    xsrfCookieName: \"XSRF-TOKEN\",\n    xsrfHeaderName: \"X-XSRF-TOKEN\",\n    maxContentLength: -1,\n    maxBodyLength: -1,\n    env: {\n        FormData: platform.classes.FormData,\n        Blob: platform.classes.Blob\n    },\n    validateStatus: function validateStatus(status) {\n        return status >= 200 && status < 300;\n    },\n    headers: {\n        common: {\n            \"Accept\": \"application/json, text/plain, */*\",\n            \"Content-Type\": undefined\n        }\n    }\n};\nutils$1.forEach([\n    \"delete\",\n    \"get\",\n    \"head\",\n    \"post\",\n    \"put\",\n    \"patch\"\n], (method)=>{\n    defaults.headers[method] = {};\n});\nconst defaults$1 = defaults;\n// RawAxiosHeaders whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nconst ignoreDuplicateOf = utils$1.toObjectSet([\n    \"age\",\n    \"authorization\",\n    \"content-length\",\n    \"content-type\",\n    \"etag\",\n    \"expires\",\n    \"from\",\n    \"host\",\n    \"if-modified-since\",\n    \"if-unmodified-since\",\n    \"last-modified\",\n    \"location\",\n    \"max-forwards\",\n    \"proxy-authorization\",\n    \"referer\",\n    \"retry-after\",\n    \"user-agent\"\n]);\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} rawHeaders Headers needing to be parsed\n *\n * @returns {Object} Headers parsed into an object\n */ const parseHeaders = (rawHeaders)=>{\n    const parsed = {};\n    let key;\n    let val;\n    let i;\n    rawHeaders && rawHeaders.split(\"\\n\").forEach(function parser(line) {\n        i = line.indexOf(\":\");\n        key = line.substring(0, i).trim().toLowerCase();\n        val = line.substring(i + 1).trim();\n        if (!key || parsed[key] && ignoreDuplicateOf[key]) {\n            return;\n        }\n        if (key === \"set-cookie\") {\n            if (parsed[key]) {\n                parsed[key].push(val);\n            } else {\n                parsed[key] = [\n                    val\n                ];\n            }\n        } else {\n            parsed[key] = parsed[key] ? parsed[key] + \", \" + val : val;\n        }\n    });\n    return parsed;\n};\nconst $internals = Symbol(\"internals\");\nfunction normalizeHeader(header) {\n    return header && String(header).trim().toLowerCase();\n}\nfunction normalizeValue(value) {\n    if (value === false || value == null) {\n        return value;\n    }\n    return utils$1.isArray(value) ? value.map(normalizeValue) : String(value);\n}\nfunction parseTokens(str) {\n    const tokens = Object.create(null);\n    const tokensRE = /([^\\s,;=]+)\\s*(?:=\\s*([^,;]+))?/g;\n    let match;\n    while(match = tokensRE.exec(str)){\n        tokens[match[1]] = match[2];\n    }\n    return tokens;\n}\nconst isValidHeaderName = (str)=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());\nfunction matchHeaderValue(context, value, header, filter, isHeaderNameFilter) {\n    if (utils$1.isFunction(filter)) {\n        return filter.call(this, value, header);\n    }\n    if (isHeaderNameFilter) {\n        value = header;\n    }\n    if (!utils$1.isString(value)) return;\n    if (utils$1.isString(filter)) {\n        return value.indexOf(filter) !== -1;\n    }\n    if (utils$1.isRegExp(filter)) {\n        return filter.test(value);\n    }\n}\nfunction formatHeader(header) {\n    return header.trim().toLowerCase().replace(/([a-z\\d])(\\w*)/g, (w, char, str)=>{\n        return char.toUpperCase() + str;\n    });\n}\nfunction buildAccessors(obj, header) {\n    const accessorName = utils$1.toCamelCase(\" \" + header);\n    [\n        \"get\",\n        \"set\",\n        \"has\"\n    ].forEach((methodName)=>{\n        Object.defineProperty(obj, methodName + accessorName, {\n            value: function(arg1, arg2, arg3) {\n                return this[methodName].call(this, header, arg1, arg2, arg3);\n            },\n            configurable: true\n        });\n    });\n}\nclass AxiosHeaders {\n    constructor(headers){\n        headers && this.set(headers);\n    }\n    set(header, valueOrRewrite, rewrite) {\n        const self1 = this;\n        function setHeader(_value, _header, _rewrite) {\n            const lHeader = normalizeHeader(_header);\n            if (!lHeader) {\n                throw new Error(\"header name must be a non-empty string\");\n            }\n            const key = utils$1.findKey(self1, lHeader);\n            if (!key || self1[key] === undefined || _rewrite === true || _rewrite === undefined && self1[key] !== false) {\n                self1[key || _header] = normalizeValue(_value);\n            }\n        }\n        const setHeaders = (headers, _rewrite)=>utils$1.forEach(headers, (_value, _header)=>setHeader(_value, _header, _rewrite));\n        if (utils$1.isPlainObject(header) || header instanceof this.constructor) {\n            setHeaders(header, valueOrRewrite);\n        } else if (utils$1.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {\n            setHeaders(parseHeaders(header), valueOrRewrite);\n        } else if (utils$1.isObject(header) && utils$1.isIterable(header)) {\n            let obj = {}, dest, key;\n            for (const entry of header){\n                if (!utils$1.isArray(entry)) {\n                    throw TypeError(\"Object iterator must return a key-value pair\");\n                }\n                obj[key = entry[0]] = (dest = obj[key]) ? utils$1.isArray(dest) ? [\n                    ...dest,\n                    entry[1]\n                ] : [\n                    dest,\n                    entry[1]\n                ] : entry[1];\n            }\n            setHeaders(obj, valueOrRewrite);\n        } else {\n            header != null && setHeader(valueOrRewrite, header, rewrite);\n        }\n        return this;\n    }\n    get(header, parser) {\n        header = normalizeHeader(header);\n        if (header) {\n            const key = utils$1.findKey(this, header);\n            if (key) {\n                const value = this[key];\n                if (!parser) {\n                    return value;\n                }\n                if (parser === true) {\n                    return parseTokens(value);\n                }\n                if (utils$1.isFunction(parser)) {\n                    return parser.call(this, value, key);\n                }\n                if (utils$1.isRegExp(parser)) {\n                    return parser.exec(value);\n                }\n                throw new TypeError(\"parser must be boolean|regexp|function\");\n            }\n        }\n    }\n    has(header, matcher) {\n        header = normalizeHeader(header);\n        if (header) {\n            const key = utils$1.findKey(this, header);\n            return !!(key && this[key] !== undefined && (!matcher || matchHeaderValue(this, this[key], key, matcher)));\n        }\n        return false;\n    }\n    delete(header, matcher) {\n        const self1 = this;\n        let deleted = false;\n        function deleteHeader(_header) {\n            _header = normalizeHeader(_header);\n            if (_header) {\n                const key = utils$1.findKey(self1, _header);\n                if (key && (!matcher || matchHeaderValue(self1, self1[key], key, matcher))) {\n                    delete self1[key];\n                    deleted = true;\n                }\n            }\n        }\n        if (utils$1.isArray(header)) {\n            header.forEach(deleteHeader);\n        } else {\n            deleteHeader(header);\n        }\n        return deleted;\n    }\n    clear(matcher) {\n        const keys = Object.keys(this);\n        let i = keys.length;\n        let deleted = false;\n        while(i--){\n            const key = keys[i];\n            if (!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {\n                delete this[key];\n                deleted = true;\n            }\n        }\n        return deleted;\n    }\n    normalize(format) {\n        const self1 = this;\n        const headers = {};\n        utils$1.forEach(this, (value, header)=>{\n            const key = utils$1.findKey(headers, header);\n            if (key) {\n                self1[key] = normalizeValue(value);\n                delete self1[header];\n                return;\n            }\n            const normalized = format ? formatHeader(header) : String(header).trim();\n            if (normalized !== header) {\n                delete self1[header];\n            }\n            self1[normalized] = normalizeValue(value);\n            headers[normalized] = true;\n        });\n        return this;\n    }\n    concat(...targets) {\n        return this.constructor.concat(this, ...targets);\n    }\n    toJSON(asStrings) {\n        const obj = Object.create(null);\n        utils$1.forEach(this, (value, header)=>{\n            value != null && value !== false && (obj[header] = asStrings && utils$1.isArray(value) ? value.join(\", \") : value);\n        });\n        return obj;\n    }\n    [Symbol.iterator]() {\n        return Object.entries(this.toJSON())[Symbol.iterator]();\n    }\n    toString() {\n        return Object.entries(this.toJSON()).map(([header, value])=>header + \": \" + value).join(\"\\n\");\n    }\n    getSetCookie() {\n        return this.get(\"set-cookie\") || [];\n    }\n    get [Symbol.toStringTag]() {\n        return \"AxiosHeaders\";\n    }\n    static from(thing) {\n        return thing instanceof this ? thing : new this(thing);\n    }\n    static concat(first, ...targets) {\n        const computed = new this(first);\n        targets.forEach((target)=>computed.set(target));\n        return computed;\n    }\n    static accessor(header) {\n        const internals = this[$internals] = this[$internals] = {\n            accessors: {}\n        };\n        const accessors = internals.accessors;\n        const prototype = this.prototype;\n        function defineAccessor(_header) {\n            const lHeader = normalizeHeader(_header);\n            if (!accessors[lHeader]) {\n                buildAccessors(prototype, _header);\n                accessors[lHeader] = true;\n            }\n        }\n        utils$1.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);\n        return this;\n    }\n}\nAxiosHeaders.accessor([\n    \"Content-Type\",\n    \"Content-Length\",\n    \"Accept\",\n    \"Accept-Encoding\",\n    \"User-Agent\",\n    \"Authorization\"\n]);\n// reserved names hotfix\nutils$1.reduceDescriptors(AxiosHeaders.prototype, ({ value }, key)=>{\n    let mapped = key[0].toUpperCase() + key.slice(1); // map `set` => `Set`\n    return {\n        get: ()=>value,\n        set (headerValue) {\n            this[mapped] = headerValue;\n        }\n    };\n});\nutils$1.freezeMethods(AxiosHeaders);\nconst AxiosHeaders$1 = AxiosHeaders;\n/**\n * Transform the data for a request or a response\n *\n * @param {Array|Function} fns A single function or Array of functions\n * @param {?Object} response The response object\n *\n * @returns {*} The resulting transformed data\n */ function transformData(fns, response) {\n    const config = this || defaults$1;\n    const context = response || config;\n    const headers = AxiosHeaders$1.from(context.headers);\n    let data = context.data;\n    utils$1.forEach(fns, function transform(fn) {\n        data = fn.call(config, data, headers.normalize(), response ? response.status : undefined);\n    });\n    headers.normalize();\n    return data;\n}\nfunction isCancel(value) {\n    return !!(value && value.__CANCEL__);\n}\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @param {string=} message The message.\n * @param {Object=} config The config.\n * @param {Object=} request The request.\n *\n * @returns {CanceledError} The created error.\n */ function CanceledError(message, config, request) {\n    // eslint-disable-next-line no-eq-null,eqeqeq\n    AxiosError.call(this, message == null ? \"canceled\" : message, AxiosError.ERR_CANCELED, config, request);\n    this.name = \"CanceledError\";\n}\nutils$1.inherits(CanceledError, AxiosError, {\n    __CANCEL__: true\n});\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n *\n * @returns {object} The response.\n */ function settle(resolve, reject, response) {\n    const validateStatus = response.config.validateStatus;\n    if (!response.status || !validateStatus || validateStatus(response.status)) {\n        resolve(response);\n    } else {\n        reject(new AxiosError(\"Request failed with status code \" + response.status, [\n            AxiosError.ERR_BAD_REQUEST,\n            AxiosError.ERR_BAD_RESPONSE\n        ][Math.floor(response.status / 100) - 4], response.config, response.request, response));\n    }\n}\n/**\n * Determines whether the specified URL is absolute\n *\n * @param {string} url The URL to test\n *\n * @returns {boolean} True if the specified URL is absolute, otherwise false\n */ function isAbsoluteURL(url) {\n    // A URL is considered absolute if it begins with \"<scheme>://\" or \"//\" (protocol-relative URL).\n    // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed\n    // by any combination of letters, digits, plus, period, or hyphen.\n    return /^([a-z][a-z\\d+\\-.]*:)?\\/\\//i.test(url);\n}\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n *\n * @returns {string} The combined URL\n */ function combineURLs(baseURL, relativeURL) {\n    return relativeURL ? baseURL.replace(/\\/?\\/$/, \"\") + \"/\" + relativeURL.replace(/^\\/+/, \"\") : baseURL;\n}\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n *\n * @returns {string} The combined full path\n */ function buildFullPath(baseURL, requestedURL, allowAbsoluteUrls) {\n    let isRelativeUrl = !isAbsoluteURL(requestedURL);\n    if (baseURL && (isRelativeUrl || allowAbsoluteUrls == false)) {\n        return combineURLs(baseURL, requestedURL);\n    }\n    return requestedURL;\n}\nconst VERSION = \"1.11.0\";\nfunction parseProtocol(url) {\n    const match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n    return match && match[1] || \"\";\n}\nconst DATA_URL_PATTERN = /^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\\s\\S]*)$/;\n/**\n * Parse data uri to a Buffer or Blob\n *\n * @param {String} uri\n * @param {?Boolean} asBlob\n * @param {?Object} options\n * @param {?Function} options.Blob\n *\n * @returns {Buffer|Blob}\n */ function fromDataURI(uri, asBlob, options) {\n    const _Blob = options && options.Blob || platform.classes.Blob;\n    const protocol = parseProtocol(uri);\n    if (asBlob === undefined && _Blob) {\n        asBlob = true;\n    }\n    if (protocol === \"data\") {\n        uri = protocol.length ? uri.slice(protocol.length + 1) : uri;\n        const match = DATA_URL_PATTERN.exec(uri);\n        if (!match) {\n            throw new AxiosError(\"Invalid URL\", AxiosError.ERR_INVALID_URL);\n        }\n        const mime = match[1];\n        const isBase64 = match[2];\n        const body = match[3];\n        const buffer = Buffer.from(decodeURIComponent(body), isBase64 ? \"base64\" : \"utf8\");\n        if (asBlob) {\n            if (!_Blob) {\n                throw new AxiosError(\"Blob is not supported\", AxiosError.ERR_NOT_SUPPORT);\n            }\n            return new _Blob([\n                buffer\n            ], {\n                type: mime\n            });\n        }\n        return buffer;\n    }\n    throw new AxiosError(\"Unsupported protocol \" + protocol, AxiosError.ERR_NOT_SUPPORT);\n}\nconst kInternals = Symbol(\"internals\");\nclass AxiosTransformStream extends stream__default[\"default\"].Transform {\n    constructor(options){\n        options = utils$1.toFlatObject(options, {\n            maxRate: 0,\n            chunkSize: 64 * 1024,\n            minChunkSize: 100,\n            timeWindow: 500,\n            ticksRate: 2,\n            samplesCount: 15\n        }, null, (prop, source)=>{\n            return !utils$1.isUndefined(source[prop]);\n        });\n        super({\n            readableHighWaterMark: options.chunkSize\n        });\n        const internals = this[kInternals] = {\n            timeWindow: options.timeWindow,\n            chunkSize: options.chunkSize,\n            maxRate: options.maxRate,\n            minChunkSize: options.minChunkSize,\n            bytesSeen: 0,\n            isCaptured: false,\n            notifiedBytesLoaded: 0,\n            ts: Date.now(),\n            bytes: 0,\n            onReadCallback: null\n        };\n        this.on(\"newListener\", (event)=>{\n            if (event === \"progress\") {\n                if (!internals.isCaptured) {\n                    internals.isCaptured = true;\n                }\n            }\n        });\n    }\n    _read(size) {\n        const internals = this[kInternals];\n        if (internals.onReadCallback) {\n            internals.onReadCallback();\n        }\n        return super._read(size);\n    }\n    _transform(chunk, encoding, callback) {\n        const internals = this[kInternals];\n        const maxRate = internals.maxRate;\n        const readableHighWaterMark = this.readableHighWaterMark;\n        const timeWindow = internals.timeWindow;\n        const divider = 1000 / timeWindow;\n        const bytesThreshold = maxRate / divider;\n        const minChunkSize = internals.minChunkSize !== false ? Math.max(internals.minChunkSize, bytesThreshold * 0.01) : 0;\n        const pushChunk = (_chunk, _callback)=>{\n            const bytes = Buffer.byteLength(_chunk);\n            internals.bytesSeen += bytes;\n            internals.bytes += bytes;\n            internals.isCaptured && this.emit(\"progress\", internals.bytesSeen);\n            if (this.push(_chunk)) {\n                process.nextTick(_callback);\n            } else {\n                internals.onReadCallback = ()=>{\n                    internals.onReadCallback = null;\n                    process.nextTick(_callback);\n                };\n            }\n        };\n        const transformChunk = (_chunk, _callback)=>{\n            const chunkSize = Buffer.byteLength(_chunk);\n            let chunkRemainder = null;\n            let maxChunkSize = readableHighWaterMark;\n            let bytesLeft;\n            let passed = 0;\n            if (maxRate) {\n                const now = Date.now();\n                if (!internals.ts || (passed = now - internals.ts) >= timeWindow) {\n                    internals.ts = now;\n                    bytesLeft = bytesThreshold - internals.bytes;\n                    internals.bytes = bytesLeft < 0 ? -bytesLeft : 0;\n                    passed = 0;\n                }\n                bytesLeft = bytesThreshold - internals.bytes;\n            }\n            if (maxRate) {\n                if (bytesLeft <= 0) {\n                    // next time window\n                    return setTimeout(()=>{\n                        _callback(null, _chunk);\n                    }, timeWindow - passed);\n                }\n                if (bytesLeft < maxChunkSize) {\n                    maxChunkSize = bytesLeft;\n                }\n            }\n            if (maxChunkSize && chunkSize > maxChunkSize && chunkSize - maxChunkSize > minChunkSize) {\n                chunkRemainder = _chunk.subarray(maxChunkSize);\n                _chunk = _chunk.subarray(0, maxChunkSize);\n            }\n            pushChunk(_chunk, chunkRemainder ? ()=>{\n                process.nextTick(_callback, null, chunkRemainder);\n            } : _callback);\n        };\n        transformChunk(chunk, function transformNextChunk(err, _chunk) {\n            if (err) {\n                return callback(err);\n            }\n            if (_chunk) {\n                transformChunk(_chunk, transformNextChunk);\n            } else {\n                callback(null);\n            }\n        });\n    }\n}\nconst AxiosTransformStream$1 = AxiosTransformStream;\nconst { asyncIterator } = Symbol;\nconst readBlob = async function*(blob) {\n    if (blob.stream) {\n        yield* blob.stream();\n    } else if (blob.arrayBuffer) {\n        yield await blob.arrayBuffer();\n    } else if (blob[asyncIterator]) {\n        yield* blob[asyncIterator]();\n    } else {\n        yield blob;\n    }\n};\nconst readBlob$1 = readBlob;\nconst BOUNDARY_ALPHABET = platform.ALPHABET.ALPHA_DIGIT + \"-_\";\nconst textEncoder = typeof TextEncoder === \"function\" ? new TextEncoder() : new util__default[\"default\"].TextEncoder();\nconst CRLF = \"\\r\\n\";\nconst CRLF_BYTES = textEncoder.encode(CRLF);\nconst CRLF_BYTES_COUNT = 2;\nclass FormDataPart {\n    constructor(name, value){\n        const { escapeName } = this.constructor;\n        const isStringValue = utils$1.isString(value);\n        let headers = `Content-Disposition: form-data; name=\"${escapeName(name)}\"${!isStringValue && value.name ? `; filename=\"${escapeName(value.name)}\"` : \"\"}${CRLF}`;\n        if (isStringValue) {\n            value = textEncoder.encode(String(value).replace(/\\r?\\n|\\r\\n?/g, CRLF));\n        } else {\n            headers += `Content-Type: ${value.type || \"application/octet-stream\"}${CRLF}`;\n        }\n        this.headers = textEncoder.encode(headers + CRLF);\n        this.contentLength = isStringValue ? value.byteLength : value.size;\n        this.size = this.headers.byteLength + this.contentLength + CRLF_BYTES_COUNT;\n        this.name = name;\n        this.value = value;\n    }\n    async *encode() {\n        yield this.headers;\n        const { value } = this;\n        if (utils$1.isTypedArray(value)) {\n            yield value;\n        } else {\n            yield* readBlob$1(value);\n        }\n        yield CRLF_BYTES;\n    }\n    static escapeName(name) {\n        return String(name).replace(/[\\r\\n\"]/g, (match)=>({\n                \"\\r\": \"%0D\",\n                \"\\n\": \"%0A\",\n                '\"': \"%22\"\n            })[match]);\n    }\n}\nconst formDataToStream = (form, headersHandler, options)=>{\n    const { tag = \"form-data-boundary\", size = 25, boundary = tag + \"-\" + platform.generateString(size, BOUNDARY_ALPHABET) } = options || {};\n    if (!utils$1.isFormData(form)) {\n        throw TypeError(\"FormData instance required\");\n    }\n    if (boundary.length < 1 || boundary.length > 70) {\n        throw Error(\"boundary must be 10-70 characters long\");\n    }\n    const boundaryBytes = textEncoder.encode(\"--\" + boundary + CRLF);\n    const footerBytes = textEncoder.encode(\"--\" + boundary + \"--\" + CRLF);\n    let contentLength = footerBytes.byteLength;\n    const parts = Array.from(form.entries()).map(([name, value])=>{\n        const part = new FormDataPart(name, value);\n        contentLength += part.size;\n        return part;\n    });\n    contentLength += boundaryBytes.byteLength * parts.length;\n    contentLength = utils$1.toFiniteNumber(contentLength);\n    const computedHeaders = {\n        \"Content-Type\": `multipart/form-data; boundary=${boundary}`\n    };\n    if (Number.isFinite(contentLength)) {\n        computedHeaders[\"Content-Length\"] = contentLength;\n    }\n    headersHandler && headersHandler(computedHeaders);\n    return stream.Readable.from(async function*() {\n        for (const part of parts){\n            yield boundaryBytes;\n            yield* part.encode();\n        }\n        yield footerBytes;\n    }());\n};\nconst formDataToStream$1 = formDataToStream;\nclass ZlibHeaderTransformStream extends stream__default[\"default\"].Transform {\n    __transform(chunk, encoding, callback) {\n        this.push(chunk);\n        callback();\n    }\n    _transform(chunk, encoding, callback) {\n        if (chunk.length !== 0) {\n            this._transform = this.__transform;\n            // Add Default Compression headers if no zlib headers are present\n            if (chunk[0] !== 120) {\n                const header = Buffer.alloc(2);\n                header[0] = 120; // Hex: 78\n                header[1] = 156; // Hex: 9C \n                this.push(header, encoding);\n            }\n        }\n        this.__transform(chunk, encoding, callback);\n    }\n}\nconst ZlibHeaderTransformStream$1 = ZlibHeaderTransformStream;\nconst callbackify = (fn, reducer)=>{\n    return utils$1.isAsyncFn(fn) ? function(...args) {\n        const cb = args.pop();\n        fn.apply(this, args).then((value)=>{\n            try {\n                reducer ? cb(null, ...reducer(value)) : cb(null, value);\n            } catch (err) {\n                cb(err);\n            }\n        }, cb);\n    } : fn;\n};\nconst callbackify$1 = callbackify;\n/**\n * Calculate data maxRate\n * @param {Number} [samplesCount= 10]\n * @param {Number} [min= 1000]\n * @returns {Function}\n */ function speedometer(samplesCount, min) {\n    samplesCount = samplesCount || 10;\n    const bytes = new Array(samplesCount);\n    const timestamps = new Array(samplesCount);\n    let head = 0;\n    let tail = 0;\n    let firstSampleTS;\n    min = min !== undefined ? min : 1000;\n    return function push(chunkLength) {\n        const now = Date.now();\n        const startedAt = timestamps[tail];\n        if (!firstSampleTS) {\n            firstSampleTS = now;\n        }\n        bytes[head] = chunkLength;\n        timestamps[head] = now;\n        let i = tail;\n        let bytesCount = 0;\n        while(i !== head){\n            bytesCount += bytes[i++];\n            i = i % samplesCount;\n        }\n        head = (head + 1) % samplesCount;\n        if (head === tail) {\n            tail = (tail + 1) % samplesCount;\n        }\n        if (now - firstSampleTS < min) {\n            return;\n        }\n        const passed = startedAt && now - startedAt;\n        return passed ? Math.round(bytesCount * 1000 / passed) : undefined;\n    };\n}\n/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */ function throttle(fn, freq) {\n    let timestamp = 0;\n    let threshold = 1000 / freq;\n    let lastArgs;\n    let timer;\n    const invoke = (args, now = Date.now())=>{\n        timestamp = now;\n        lastArgs = null;\n        if (timer) {\n            clearTimeout(timer);\n            timer = null;\n        }\n        fn(...args);\n    };\n    const throttled = (...args)=>{\n        const now = Date.now();\n        const passed = now - timestamp;\n        if (passed >= threshold) {\n            invoke(args, now);\n        } else {\n            lastArgs = args;\n            if (!timer) {\n                timer = setTimeout(()=>{\n                    timer = null;\n                    invoke(lastArgs);\n                }, threshold - passed);\n            }\n        }\n    };\n    const flush = ()=>lastArgs && invoke(lastArgs);\n    return [\n        throttled,\n        flush\n    ];\n}\nconst progressEventReducer = (listener, isDownloadStream, freq = 3)=>{\n    let bytesNotified = 0;\n    const _speedometer = speedometer(50, 250);\n    return throttle((e)=>{\n        const loaded = e.loaded;\n        const total = e.lengthComputable ? e.total : undefined;\n        const progressBytes = loaded - bytesNotified;\n        const rate = _speedometer(progressBytes);\n        const inRange = loaded <= total;\n        bytesNotified = loaded;\n        const data = {\n            loaded,\n            total,\n            progress: total ? loaded / total : undefined,\n            bytes: progressBytes,\n            rate: rate ? rate : undefined,\n            estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n            event: e,\n            lengthComputable: total != null,\n            [isDownloadStream ? \"download\" : \"upload\"]: true\n        };\n        listener(data);\n    }, freq);\n};\nconst progressEventDecorator = (total, throttled)=>{\n    const lengthComputable = total != null;\n    return [\n        (loaded)=>throttled[0]({\n                lengthComputable,\n                total,\n                loaded\n            }),\n        throttled[1]\n    ];\n};\nconst asyncDecorator = (fn)=>(...args)=>utils$1.asap(()=>fn(...args));\nconst zlibOptions = {\n    flush: zlib__default[\"default\"].constants.Z_SYNC_FLUSH,\n    finishFlush: zlib__default[\"default\"].constants.Z_SYNC_FLUSH\n};\nconst brotliOptions = {\n    flush: zlib__default[\"default\"].constants.BROTLI_OPERATION_FLUSH,\n    finishFlush: zlib__default[\"default\"].constants.BROTLI_OPERATION_FLUSH\n};\nconst isBrotliSupported = utils$1.isFunction(zlib__default[\"default\"].createBrotliDecompress);\nconst { http: httpFollow, https: httpsFollow } = followRedirects__default[\"default\"];\nconst isHttps = /https:?/;\nconst supportedProtocols = platform.protocols.map((protocol)=>{\n    return protocol + \":\";\n});\nconst flushOnFinish = (stream, [throttled, flush])=>{\n    stream.on(\"end\", flush).on(\"error\", flush);\n    return throttled;\n};\n/**\n * If the proxy or config beforeRedirects functions are defined, call them with the options\n * object.\n *\n * @param {Object<string, any>} options - The options object that was passed to the request.\n *\n * @returns {Object<string, any>}\n */ function dispatchBeforeRedirect(options, responseDetails) {\n    if (options.beforeRedirects.proxy) {\n        options.beforeRedirects.proxy(options);\n    }\n    if (options.beforeRedirects.config) {\n        options.beforeRedirects.config(options, responseDetails);\n    }\n}\n/**\n * If the proxy or config afterRedirects functions are defined, call them with the options\n *\n * @param {http.ClientRequestArgs} options\n * @param {AxiosProxyConfig} configProxy configuration from Axios options object\n * @param {string} location\n *\n * @returns {http.ClientRequestArgs}\n */ function setProxy(options, configProxy, location) {\n    let proxy = configProxy;\n    if (!proxy && proxy !== false) {\n        const proxyUrl = proxyFromEnv__default[\"default\"].getProxyForUrl(location);\n        if (proxyUrl) {\n            proxy = new URL(proxyUrl);\n        }\n    }\n    if (proxy) {\n        // Basic proxy authorization\n        if (proxy.username) {\n            proxy.auth = (proxy.username || \"\") + \":\" + (proxy.password || \"\");\n        }\n        if (proxy.auth) {\n            // Support proxy auth object form\n            if (proxy.auth.username || proxy.auth.password) {\n                proxy.auth = (proxy.auth.username || \"\") + \":\" + (proxy.auth.password || \"\");\n            }\n            const base64 = Buffer.from(proxy.auth, \"utf8\").toString(\"base64\");\n            options.headers[\"Proxy-Authorization\"] = \"Basic \" + base64;\n        }\n        options.headers.host = options.hostname + (options.port ? \":\" + options.port : \"\");\n        const proxyHost = proxy.hostname || proxy.host;\n        options.hostname = proxyHost;\n        // Replace 'host' since options is not a URL object\n        options.host = proxyHost;\n        options.port = proxy.port;\n        options.path = location;\n        if (proxy.protocol) {\n            options.protocol = proxy.protocol.includes(\":\") ? proxy.protocol : `${proxy.protocol}:`;\n        }\n    }\n    options.beforeRedirects.proxy = function beforeRedirect(redirectOptions) {\n        // Configure proxy for redirected request, passing the original config proxy to apply\n        // the exact same logic as if the redirected request was performed by axios directly.\n        setProxy(redirectOptions, configProxy, redirectOptions.href);\n    };\n}\nconst isHttpAdapterSupported = typeof process !== \"undefined\" && utils$1.kindOf(process) === \"process\";\n// temporary hotfix\nconst wrapAsync = (asyncExecutor)=>{\n    return new Promise((resolve, reject)=>{\n        let onDone;\n        let isDone;\n        const done = (value, isRejected)=>{\n            if (isDone) return;\n            isDone = true;\n            onDone && onDone(value, isRejected);\n        };\n        const _resolve = (value)=>{\n            done(value);\n            resolve(value);\n        };\n        const _reject = (reason)=>{\n            done(reason, true);\n            reject(reason);\n        };\n        asyncExecutor(_resolve, _reject, (onDoneHandler)=>onDone = onDoneHandler).catch(_reject);\n    });\n};\nconst resolveFamily = ({ address, family })=>{\n    if (!utils$1.isString(address)) {\n        throw TypeError(\"address must be a string\");\n    }\n    return {\n        address,\n        family: family || (address.indexOf(\".\") < 0 ? 6 : 4)\n    };\n};\nconst buildAddressEntry = (address, family)=>resolveFamily(utils$1.isObject(address) ? address : {\n        address,\n        family\n    });\n/*eslint consistent-return:0*/ const httpAdapter = isHttpAdapterSupported && function httpAdapter(config) {\n    return wrapAsync(async function dispatchHttpRequest(resolve, reject, onDone) {\n        let { data, lookup, family } = config;\n        const { responseType, responseEncoding } = config;\n        const method = config.method.toUpperCase();\n        let isDone;\n        let rejected = false;\n        let req;\n        if (lookup) {\n            const _lookup = callbackify$1(lookup, (value)=>utils$1.isArray(value) ? value : [\n                    value\n                ]);\n            // hotfix to support opt.all option which is required for node 20.x\n            lookup = (hostname, opt, cb)=>{\n                _lookup(hostname, opt, (err, arg0, arg1)=>{\n                    if (err) {\n                        return cb(err);\n                    }\n                    const addresses = utils$1.isArray(arg0) ? arg0.map((addr)=>buildAddressEntry(addr)) : [\n                        buildAddressEntry(arg0, arg1)\n                    ];\n                    opt.all ? cb(err, addresses) : cb(err, addresses[0].address, addresses[0].family);\n                });\n            };\n        }\n        // temporary internal emitter until the AxiosRequest class will be implemented\n        const emitter = new events.EventEmitter();\n        const onFinished = ()=>{\n            if (config.cancelToken) {\n                config.cancelToken.unsubscribe(abort);\n            }\n            if (config.signal) {\n                config.signal.removeEventListener(\"abort\", abort);\n            }\n            emitter.removeAllListeners();\n        };\n        onDone((value, isRejected)=>{\n            isDone = true;\n            if (isRejected) {\n                rejected = true;\n                onFinished();\n            }\n        });\n        function abort(reason) {\n            emitter.emit(\"abort\", !reason || reason.type ? new CanceledError(null, config, req) : reason);\n        }\n        emitter.once(\"abort\", reject);\n        if (config.cancelToken || config.signal) {\n            config.cancelToken && config.cancelToken.subscribe(abort);\n            if (config.signal) {\n                config.signal.aborted ? abort() : config.signal.addEventListener(\"abort\", abort);\n            }\n        }\n        // Parse url\n        const fullPath = buildFullPath(config.baseURL, config.url, config.allowAbsoluteUrls);\n        const parsed = new URL(fullPath, platform.hasBrowserEnv ? platform.origin : undefined);\n        const protocol = parsed.protocol || supportedProtocols[0];\n        if (protocol === \"data:\") {\n            let convertedData;\n            if (method !== \"GET\") {\n                return settle(resolve, reject, {\n                    status: 405,\n                    statusText: \"method not allowed\",\n                    headers: {},\n                    config\n                });\n            }\n            try {\n                convertedData = fromDataURI(config.url, responseType === \"blob\", {\n                    Blob: config.env && config.env.Blob\n                });\n            } catch (err) {\n                throw AxiosError.from(err, AxiosError.ERR_BAD_REQUEST, config);\n            }\n            if (responseType === \"text\") {\n                convertedData = convertedData.toString(responseEncoding);\n                if (!responseEncoding || responseEncoding === \"utf8\") {\n                    convertedData = utils$1.stripBOM(convertedData);\n                }\n            } else if (responseType === \"stream\") {\n                convertedData = stream__default[\"default\"].Readable.from(convertedData);\n            }\n            return settle(resolve, reject, {\n                data: convertedData,\n                status: 200,\n                statusText: \"OK\",\n                headers: new AxiosHeaders$1(),\n                config\n            });\n        }\n        if (supportedProtocols.indexOf(protocol) === -1) {\n            return reject(new AxiosError(\"Unsupported protocol \" + protocol, AxiosError.ERR_BAD_REQUEST, config));\n        }\n        const headers = AxiosHeaders$1.from(config.headers).normalize();\n        // Set User-Agent (required by some servers)\n        // See https://github.com/axios/axios/issues/69\n        // User-Agent is specified; handle case where no UA header is desired\n        // Only set header if it hasn't been set in config\n        headers.set(\"User-Agent\", \"axios/\" + VERSION, false);\n        const { onUploadProgress, onDownloadProgress } = config;\n        const maxRate = config.maxRate;\n        let maxUploadRate = undefined;\n        let maxDownloadRate = undefined;\n        // support for spec compliant FormData objects\n        if (utils$1.isSpecCompliantForm(data)) {\n            const userBoundary = headers.getContentType(/boundary=([-_\\w\\d]{10,70})/i);\n            data = formDataToStream$1(data, (formHeaders)=>{\n                headers.set(formHeaders);\n            }, {\n                tag: `axios-${VERSION}-boundary`,\n                boundary: userBoundary && userBoundary[1] || undefined\n            });\n        // support for https://www.npmjs.com/package/form-data api\n        } else if (utils$1.isFormData(data) && utils$1.isFunction(data.getHeaders)) {\n            headers.set(data.getHeaders());\n            if (!headers.hasContentLength()) {\n                try {\n                    const knownLength = await util__default[\"default\"].promisify(data.getLength).call(data);\n                    Number.isFinite(knownLength) && knownLength >= 0 && headers.setContentLength(knownLength);\n                /*eslint no-empty:0*/ } catch (e) {}\n            }\n        } else if (utils$1.isBlob(data) || utils$1.isFile(data)) {\n            data.size && headers.setContentType(data.type || \"application/octet-stream\");\n            headers.setContentLength(data.size || 0);\n            data = stream__default[\"default\"].Readable.from(readBlob$1(data));\n        } else if (data && !utils$1.isStream(data)) {\n            if (Buffer.isBuffer(data)) ;\n            else if (utils$1.isArrayBuffer(data)) {\n                data = Buffer.from(new Uint8Array(data));\n            } else if (utils$1.isString(data)) {\n                data = Buffer.from(data, \"utf-8\");\n            } else {\n                return reject(new AxiosError(\"Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream\", AxiosError.ERR_BAD_REQUEST, config));\n            }\n            // Add Content-Length header if data exists\n            headers.setContentLength(data.length, false);\n            if (config.maxBodyLength > -1 && data.length > config.maxBodyLength) {\n                return reject(new AxiosError(\"Request body larger than maxBodyLength limit\", AxiosError.ERR_BAD_REQUEST, config));\n            }\n        }\n        const contentLength = utils$1.toFiniteNumber(headers.getContentLength());\n        if (utils$1.isArray(maxRate)) {\n            maxUploadRate = maxRate[0];\n            maxDownloadRate = maxRate[1];\n        } else {\n            maxUploadRate = maxDownloadRate = maxRate;\n        }\n        if (data && (onUploadProgress || maxUploadRate)) {\n            if (!utils$1.isStream(data)) {\n                data = stream__default[\"default\"].Readable.from(data, {\n                    objectMode: false\n                });\n            }\n            data = stream__default[\"default\"].pipeline([\n                data,\n                new AxiosTransformStream$1({\n                    maxRate: utils$1.toFiniteNumber(maxUploadRate)\n                })\n            ], utils$1.noop);\n            onUploadProgress && data.on(\"progress\", flushOnFinish(data, progressEventDecorator(contentLength, progressEventReducer(asyncDecorator(onUploadProgress), false, 3))));\n        }\n        // HTTP basic authentication\n        let auth = undefined;\n        if (config.auth) {\n            const username = config.auth.username || \"\";\n            const password = config.auth.password || \"\";\n            auth = username + \":\" + password;\n        }\n        if (!auth && parsed.username) {\n            const urlUsername = parsed.username;\n            const urlPassword = parsed.password;\n            auth = urlUsername + \":\" + urlPassword;\n        }\n        auth && headers.delete(\"authorization\");\n        let path;\n        try {\n            path = buildURL(parsed.pathname + parsed.search, config.params, config.paramsSerializer).replace(/^\\?/, \"\");\n        } catch (err) {\n            const customErr = new Error(err.message);\n            customErr.config = config;\n            customErr.url = config.url;\n            customErr.exists = true;\n            return reject(customErr);\n        }\n        headers.set(\"Accept-Encoding\", \"gzip, compress, deflate\" + (isBrotliSupported ? \", br\" : \"\"), false);\n        const options = {\n            path,\n            method: method,\n            headers: headers.toJSON(),\n            agents: {\n                http: config.httpAgent,\n                https: config.httpsAgent\n            },\n            auth,\n            protocol,\n            family,\n            beforeRedirect: dispatchBeforeRedirect,\n            beforeRedirects: {}\n        };\n        // cacheable-lookup integration hotfix\n        !utils$1.isUndefined(lookup) && (options.lookup = lookup);\n        if (config.socketPath) {\n            options.socketPath = config.socketPath;\n        } else {\n            options.hostname = parsed.hostname.startsWith(\"[\") ? parsed.hostname.slice(1, -1) : parsed.hostname;\n            options.port = parsed.port;\n            setProxy(options, config.proxy, protocol + \"//\" + parsed.hostname + (parsed.port ? \":\" + parsed.port : \"\") + options.path);\n        }\n        let transport;\n        const isHttpsRequest = isHttps.test(options.protocol);\n        options.agent = isHttpsRequest ? config.httpsAgent : config.httpAgent;\n        if (config.transport) {\n            transport = config.transport;\n        } else if (config.maxRedirects === 0) {\n            transport = isHttpsRequest ? https__default[\"default\"] : http__default[\"default\"];\n        } else {\n            if (config.maxRedirects) {\n                options.maxRedirects = config.maxRedirects;\n            }\n            if (config.beforeRedirect) {\n                options.beforeRedirects.config = config.beforeRedirect;\n            }\n            transport = isHttpsRequest ? httpsFollow : httpFollow;\n        }\n        if (config.maxBodyLength > -1) {\n            options.maxBodyLength = config.maxBodyLength;\n        } else {\n            // follow-redirects does not skip comparison, so it should always succeed for axios -1 unlimited\n            options.maxBodyLength = Infinity;\n        }\n        if (config.insecureHTTPParser) {\n            options.insecureHTTPParser = config.insecureHTTPParser;\n        }\n        // Create the request\n        req = transport.request(options, function handleResponse(res) {\n            if (req.destroyed) return;\n            const streams = [\n                res\n            ];\n            const responseLength = +res.headers[\"content-length\"];\n            if (onDownloadProgress || maxDownloadRate) {\n                const transformStream = new AxiosTransformStream$1({\n                    maxRate: utils$1.toFiniteNumber(maxDownloadRate)\n                });\n                onDownloadProgress && transformStream.on(\"progress\", flushOnFinish(transformStream, progressEventDecorator(responseLength, progressEventReducer(asyncDecorator(onDownloadProgress), true, 3))));\n                streams.push(transformStream);\n            }\n            // decompress the response body transparently if required\n            let responseStream = res;\n            // return the last request in case of redirects\n            const lastRequest = res.req || req;\n            // if decompress disabled we should not decompress\n            if (config.decompress !== false && res.headers[\"content-encoding\"]) {\n                // if no content, but headers still say that it is encoded,\n                // remove the header not confuse downstream operations\n                if (method === \"HEAD\" || res.statusCode === 204) {\n                    delete res.headers[\"content-encoding\"];\n                }\n                switch((res.headers[\"content-encoding\"] || \"\").toLowerCase()){\n                    /*eslint default-case:0*/ case \"gzip\":\n                    case \"x-gzip\":\n                    case \"compress\":\n                    case \"x-compress\":\n                        // add the unzipper to the body stream processing pipeline\n                        streams.push(zlib__default[\"default\"].createUnzip(zlibOptions));\n                        // remove the content-encoding in order to not confuse downstream operations\n                        delete res.headers[\"content-encoding\"];\n                        break;\n                    case \"deflate\":\n                        streams.push(new ZlibHeaderTransformStream$1());\n                        // add the unzipper to the body stream processing pipeline\n                        streams.push(zlib__default[\"default\"].createUnzip(zlibOptions));\n                        // remove the content-encoding in order to not confuse downstream operations\n                        delete res.headers[\"content-encoding\"];\n                        break;\n                    case \"br\":\n                        if (isBrotliSupported) {\n                            streams.push(zlib__default[\"default\"].createBrotliDecompress(brotliOptions));\n                            delete res.headers[\"content-encoding\"];\n                        }\n                }\n            }\n            responseStream = streams.length > 1 ? stream__default[\"default\"].pipeline(streams, utils$1.noop) : streams[0];\n            const offListeners = stream__default[\"default\"].finished(responseStream, ()=>{\n                offListeners();\n                onFinished();\n            });\n            const response = {\n                status: res.statusCode,\n                statusText: res.statusMessage,\n                headers: new AxiosHeaders$1(res.headers),\n                config,\n                request: lastRequest\n            };\n            if (responseType === \"stream\") {\n                response.data = responseStream;\n                settle(resolve, reject, response);\n            } else {\n                const responseBuffer = [];\n                let totalResponseBytes = 0;\n                responseStream.on(\"data\", function handleStreamData(chunk) {\n                    responseBuffer.push(chunk);\n                    totalResponseBytes += chunk.length;\n                    // make sure the content length is not over the maxContentLength if specified\n                    if (config.maxContentLength > -1 && totalResponseBytes > config.maxContentLength) {\n                        // stream.destroy() emit aborted event before calling reject() on Node.js v16\n                        rejected = true;\n                        responseStream.destroy();\n                        reject(new AxiosError(\"maxContentLength size of \" + config.maxContentLength + \" exceeded\", AxiosError.ERR_BAD_RESPONSE, config, lastRequest));\n                    }\n                });\n                responseStream.on(\"aborted\", function handlerStreamAborted() {\n                    if (rejected) {\n                        return;\n                    }\n                    const err = new AxiosError(\"stream has been aborted\", AxiosError.ERR_BAD_RESPONSE, config, lastRequest);\n                    responseStream.destroy(err);\n                    reject(err);\n                });\n                responseStream.on(\"error\", function handleStreamError(err) {\n                    if (req.destroyed) return;\n                    reject(AxiosError.from(err, null, config, lastRequest));\n                });\n                responseStream.on(\"end\", function handleStreamEnd() {\n                    try {\n                        let responseData = responseBuffer.length === 1 ? responseBuffer[0] : Buffer.concat(responseBuffer);\n                        if (responseType !== \"arraybuffer\") {\n                            responseData = responseData.toString(responseEncoding);\n                            if (!responseEncoding || responseEncoding === \"utf8\") {\n                                responseData = utils$1.stripBOM(responseData);\n                            }\n                        }\n                        response.data = responseData;\n                    } catch (err) {\n                        return reject(AxiosError.from(err, null, config, response.request, response));\n                    }\n                    settle(resolve, reject, response);\n                });\n            }\n            emitter.once(\"abort\", (err)=>{\n                if (!responseStream.destroyed) {\n                    responseStream.emit(\"error\", err);\n                    responseStream.destroy();\n                }\n            });\n        });\n        emitter.once(\"abort\", (err)=>{\n            reject(err);\n            req.destroy(err);\n        });\n        // Handle errors\n        req.on(\"error\", function handleRequestError(err) {\n            // @todo remove\n            // if (req.aborted && err.code !== AxiosError.ERR_FR_TOO_MANY_REDIRECTS) return;\n            reject(AxiosError.from(err, null, config, req));\n        });\n        // set tcp keep alive to prevent drop connection by peer\n        req.on(\"socket\", function handleRequestSocket(socket) {\n            // default interval of sending ack packet is 1 minute\n            socket.setKeepAlive(true, 1000 * 60);\n        });\n        // Handle request timeout\n        if (config.timeout) {\n            // This is forcing a int timeout to avoid problems if the `req` interface doesn't handle other types.\n            const timeout = parseInt(config.timeout, 10);\n            if (Number.isNaN(timeout)) {\n                reject(new AxiosError(\"error trying to parse `config.timeout` to int\", AxiosError.ERR_BAD_OPTION_VALUE, config, req));\n                return;\n            }\n            // Sometime, the response will be very slow, and does not respond, the connect event will be block by event loop system.\n            // And timer callback will be fired, and abort() will be invoked before connection, then get \"socket hang up\" and code ECONNRESET.\n            // At this time, if we have a large number of request, nodejs will hang up some socket on background. and the number will up and up.\n            // And then these socket which be hang up will devouring CPU little by little.\n            // ClientRequest.setTimeout will be fired on the specify milliseconds, and can make sure that abort() will be fired after connect.\n            req.setTimeout(timeout, function handleRequestTimeout() {\n                if (isDone) return;\n                let timeoutErrorMessage = config.timeout ? \"timeout of \" + config.timeout + \"ms exceeded\" : \"timeout exceeded\";\n                const transitional = config.transitional || transitionalDefaults;\n                if (config.timeoutErrorMessage) {\n                    timeoutErrorMessage = config.timeoutErrorMessage;\n                }\n                reject(new AxiosError(timeoutErrorMessage, transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED, config, req));\n                abort();\n            });\n        }\n        // Send the request\n        if (utils$1.isStream(data)) {\n            let ended = false;\n            let errored = false;\n            data.on(\"end\", ()=>{\n                ended = true;\n            });\n            data.once(\"error\", (err)=>{\n                errored = true;\n                req.destroy(err);\n            });\n            data.on(\"close\", ()=>{\n                if (!ended && !errored) {\n                    abort(new CanceledError(\"Request stream has been aborted\", config, req));\n                }\n            });\n            data.pipe(req);\n        } else {\n            req.end(data);\n        }\n    });\n};\nconst isURLSameOrigin = platform.hasStandardBrowserEnv ? ((origin, isMSIE)=>(url)=>{\n        url = new URL(url, platform.origin);\n        return origin.protocol === url.protocol && origin.host === url.host && (isMSIE || origin.port === url.port);\n    })(new URL(platform.origin), platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)) : ()=>true;\nconst cookies = platform.hasStandardBrowserEnv ? // Standard browser envs support document.cookie\n{\n    write (name, value, expires, path, domain, secure) {\n        const cookie = [\n            name + \"=\" + encodeURIComponent(value)\n        ];\n        utils$1.isNumber(expires) && cookie.push(\"expires=\" + new Date(expires).toGMTString());\n        utils$1.isString(path) && cookie.push(\"path=\" + path);\n        utils$1.isString(domain) && cookie.push(\"domain=\" + domain);\n        secure === true && cookie.push(\"secure\");\n        document.cookie = cookie.join(\"; \");\n    },\n    read (name) {\n        const match = document.cookie.match(new RegExp(\"(^|;\\\\s*)(\" + name + \")=([^;]*)\"));\n        return match ? decodeURIComponent(match[3]) : null;\n    },\n    remove (name) {\n        this.write(name, \"\", Date.now() - 86400000);\n    }\n} : // Non-standard browser env (web workers, react-native) lack needed support.\n{\n    write () {},\n    read () {\n        return null;\n    },\n    remove () {}\n};\nconst headersToObject = (thing)=>thing instanceof AxiosHeaders$1 ? {\n        ...thing\n    } : thing;\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */ function mergeConfig(config1, config2) {\n    // eslint-disable-next-line no-param-reassign\n    config2 = config2 || {};\n    const config = {};\n    function getMergedValue(target, source, prop, caseless) {\n        if (utils$1.isPlainObject(target) && utils$1.isPlainObject(source)) {\n            return utils$1.merge.call({\n                caseless\n            }, target, source);\n        } else if (utils$1.isPlainObject(source)) {\n            return utils$1.merge({}, source);\n        } else if (utils$1.isArray(source)) {\n            return source.slice();\n        }\n        return source;\n    }\n    // eslint-disable-next-line consistent-return\n    function mergeDeepProperties(a, b, prop, caseless) {\n        if (!utils$1.isUndefined(b)) {\n            return getMergedValue(a, b, prop, caseless);\n        } else if (!utils$1.isUndefined(a)) {\n            return getMergedValue(undefined, a, prop, caseless);\n        }\n    }\n    // eslint-disable-next-line consistent-return\n    function valueFromConfig2(a, b) {\n        if (!utils$1.isUndefined(b)) {\n            return getMergedValue(undefined, b);\n        }\n    }\n    // eslint-disable-next-line consistent-return\n    function defaultToConfig2(a, b) {\n        if (!utils$1.isUndefined(b)) {\n            return getMergedValue(undefined, b);\n        } else if (!utils$1.isUndefined(a)) {\n            return getMergedValue(undefined, a);\n        }\n    }\n    // eslint-disable-next-line consistent-return\n    function mergeDirectKeys(a, b, prop) {\n        if (prop in config2) {\n            return getMergedValue(a, b);\n        } else if (prop in config1) {\n            return getMergedValue(undefined, a);\n        }\n    }\n    const mergeMap = {\n        url: valueFromConfig2,\n        method: valueFromConfig2,\n        data: valueFromConfig2,\n        baseURL: defaultToConfig2,\n        transformRequest: defaultToConfig2,\n        transformResponse: defaultToConfig2,\n        paramsSerializer: defaultToConfig2,\n        timeout: defaultToConfig2,\n        timeoutMessage: defaultToConfig2,\n        withCredentials: defaultToConfig2,\n        withXSRFToken: defaultToConfig2,\n        adapter: defaultToConfig2,\n        responseType: defaultToConfig2,\n        xsrfCookieName: defaultToConfig2,\n        xsrfHeaderName: defaultToConfig2,\n        onUploadProgress: defaultToConfig2,\n        onDownloadProgress: defaultToConfig2,\n        decompress: defaultToConfig2,\n        maxContentLength: defaultToConfig2,\n        maxBodyLength: defaultToConfig2,\n        beforeRedirect: defaultToConfig2,\n        transport: defaultToConfig2,\n        httpAgent: defaultToConfig2,\n        httpsAgent: defaultToConfig2,\n        cancelToken: defaultToConfig2,\n        socketPath: defaultToConfig2,\n        responseEncoding: defaultToConfig2,\n        validateStatus: mergeDirectKeys,\n        headers: (a, b, prop)=>mergeDeepProperties(headersToObject(a), headersToObject(b), prop, true)\n    };\n    utils$1.forEach(Object.keys({\n        ...config1,\n        ...config2\n    }), function computeConfigValue(prop) {\n        const merge = mergeMap[prop] || mergeDeepProperties;\n        const configValue = merge(config1[prop], config2[prop], prop);\n        utils$1.isUndefined(configValue) && merge !== mergeDirectKeys || (config[prop] = configValue);\n    });\n    return config;\n}\nconst resolveConfig = (config)=>{\n    const newConfig = mergeConfig({}, config);\n    let { data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth } = newConfig;\n    newConfig.headers = headers = AxiosHeaders$1.from(headers);\n    newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url, newConfig.allowAbsoluteUrls), config.params, config.paramsSerializer);\n    // HTTP basic authentication\n    if (auth) {\n        headers.set(\"Authorization\", \"Basic \" + btoa((auth.username || \"\") + \":\" + (auth.password ? unescape(encodeURIComponent(auth.password)) : \"\")));\n    }\n    let contentType;\n    if (utils$1.isFormData(data)) {\n        if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {\n            headers.setContentType(undefined); // Let the browser set it\n        } else if ((contentType = headers.getContentType()) !== false) {\n            // fix semicolon duplication issue for ReactNative FormData implementation\n            const [type, ...tokens] = contentType ? contentType.split(\";\").map((token)=>token.trim()).filter(Boolean) : [];\n            headers.setContentType([\n                type || \"multipart/form-data\",\n                ...tokens\n            ].join(\"; \"));\n        }\n    }\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (platform.hasStandardBrowserEnv) {\n        withXSRFToken && utils$1.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));\n        if (withXSRFToken || withXSRFToken !== false && isURLSameOrigin(newConfig.url)) {\n            // Add xsrf header\n            const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);\n            if (xsrfValue) {\n                headers.set(xsrfHeaderName, xsrfValue);\n            }\n        }\n    }\n    return newConfig;\n};\nconst isXHRAdapterSupported = typeof XMLHttpRequest !== \"undefined\";\nconst xhrAdapter = isXHRAdapterSupported && function(config) {\n    return new Promise(function dispatchXhrRequest(resolve, reject) {\n        const _config = resolveConfig(config);\n        let requestData = _config.data;\n        const requestHeaders = AxiosHeaders$1.from(_config.headers).normalize();\n        let { responseType, onUploadProgress, onDownloadProgress } = _config;\n        let onCanceled;\n        let uploadThrottled, downloadThrottled;\n        let flushUpload, flushDownload;\n        function done() {\n            flushUpload && flushUpload(); // flush events\n            flushDownload && flushDownload(); // flush events\n            _config.cancelToken && _config.cancelToken.unsubscribe(onCanceled);\n            _config.signal && _config.signal.removeEventListener(\"abort\", onCanceled);\n        }\n        let request = new XMLHttpRequest();\n        request.open(_config.method.toUpperCase(), _config.url, true);\n        // Set the request timeout in MS\n        request.timeout = _config.timeout;\n        function onloadend() {\n            if (!request) {\n                return;\n            }\n            // Prepare the response\n            const responseHeaders = AxiosHeaders$1.from(\"getAllResponseHeaders\" in request && request.getAllResponseHeaders());\n            const responseData = !responseType || responseType === \"text\" || responseType === \"json\" ? request.responseText : request.response;\n            const response = {\n                data: responseData,\n                status: request.status,\n                statusText: request.statusText,\n                headers: responseHeaders,\n                config,\n                request\n            };\n            settle(function _resolve(value) {\n                resolve(value);\n                done();\n            }, function _reject(err) {\n                reject(err);\n                done();\n            }, response);\n            // Clean up request\n            request = null;\n        }\n        if (\"onloadend\" in request) {\n            // Use onloadend if available\n            request.onloadend = onloadend;\n        } else {\n            // Listen for ready state to emulate onloadend\n            request.onreadystatechange = function handleLoad() {\n                if (!request || request.readyState !== 4) {\n                    return;\n                }\n                // The request errored out and we didn't get a response, this will be\n                // handled by onerror instead\n                // With one exception: request that using file: protocol, most browsers\n                // will return status as 0 even though it's a successful request\n                if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf(\"file:\") === 0)) {\n                    return;\n                }\n                // readystate handler is calling before onerror or ontimeout handlers,\n                // so we should call onloadend on the next 'tick'\n                setTimeout(onloadend);\n            };\n        }\n        // Handle browser request cancellation (as opposed to a manual cancellation)\n        request.onabort = function handleAbort() {\n            if (!request) {\n                return;\n            }\n            reject(new AxiosError(\"Request aborted\", AxiosError.ECONNABORTED, config, request));\n            // Clean up request\n            request = null;\n        };\n        // Handle low level network errors\n        request.onerror = function handleError() {\n            // Real errors are hidden from us by the browser\n            // onerror should only fire if it's a network error\n            reject(new AxiosError(\"Network Error\", AxiosError.ERR_NETWORK, config, request));\n            // Clean up request\n            request = null;\n        };\n        // Handle timeout\n        request.ontimeout = function handleTimeout() {\n            let timeoutErrorMessage = _config.timeout ? \"timeout of \" + _config.timeout + \"ms exceeded\" : \"timeout exceeded\";\n            const transitional = _config.transitional || transitionalDefaults;\n            if (_config.timeoutErrorMessage) {\n                timeoutErrorMessage = _config.timeoutErrorMessage;\n            }\n            reject(new AxiosError(timeoutErrorMessage, transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED, config, request));\n            // Clean up request\n            request = null;\n        };\n        // Remove Content-Type if data is undefined\n        requestData === undefined && requestHeaders.setContentType(null);\n        // Add headers to the request\n        if (\"setRequestHeader\" in request) {\n            utils$1.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {\n                request.setRequestHeader(key, val);\n            });\n        }\n        // Add withCredentials to request if needed\n        if (!utils$1.isUndefined(_config.withCredentials)) {\n            request.withCredentials = !!_config.withCredentials;\n        }\n        // Add responseType to request if needed\n        if (responseType && responseType !== \"json\") {\n            request.responseType = _config.responseType;\n        }\n        // Handle progress if needed\n        if (onDownloadProgress) {\n            [downloadThrottled, flushDownload] = progressEventReducer(onDownloadProgress, true);\n            request.addEventListener(\"progress\", downloadThrottled);\n        }\n        // Not all browsers support upload events\n        if (onUploadProgress && request.upload) {\n            [uploadThrottled, flushUpload] = progressEventReducer(onUploadProgress);\n            request.upload.addEventListener(\"progress\", uploadThrottled);\n            request.upload.addEventListener(\"loadend\", flushUpload);\n        }\n        if (_config.cancelToken || _config.signal) {\n            // Handle cancellation\n            // eslint-disable-next-line func-names\n            onCanceled = (cancel)=>{\n                if (!request) {\n                    return;\n                }\n                reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);\n                request.abort();\n                request = null;\n            };\n            _config.cancelToken && _config.cancelToken.subscribe(onCanceled);\n            if (_config.signal) {\n                _config.signal.aborted ? onCanceled() : _config.signal.addEventListener(\"abort\", onCanceled);\n            }\n        }\n        const protocol = parseProtocol(_config.url);\n        if (protocol && platform.protocols.indexOf(protocol) === -1) {\n            reject(new AxiosError(\"Unsupported protocol \" + protocol + \":\", AxiosError.ERR_BAD_REQUEST, config));\n            return;\n        }\n        // Send the request\n        request.send(requestData || null);\n    });\n};\nconst composeSignals = (signals, timeout)=>{\n    const { length } = signals = signals ? signals.filter(Boolean) : [];\n    if (timeout || length) {\n        let controller = new AbortController();\n        let aborted;\n        const onabort = function(reason) {\n            if (!aborted) {\n                aborted = true;\n                unsubscribe();\n                const err = reason instanceof Error ? reason : this.reason;\n                controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));\n            }\n        };\n        let timer = timeout && setTimeout(()=>{\n            timer = null;\n            onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT));\n        }, timeout);\n        const unsubscribe = ()=>{\n            if (signals) {\n                timer && clearTimeout(timer);\n                timer = null;\n                signals.forEach((signal)=>{\n                    signal.unsubscribe ? signal.unsubscribe(onabort) : signal.removeEventListener(\"abort\", onabort);\n                });\n                signals = null;\n            }\n        };\n        signals.forEach((signal)=>signal.addEventListener(\"abort\", onabort));\n        const { signal } = controller;\n        signal.unsubscribe = ()=>utils$1.asap(unsubscribe);\n        return signal;\n    }\n};\nconst composeSignals$1 = composeSignals;\nconst streamChunk = function*(chunk, chunkSize) {\n    let len = chunk.byteLength;\n    if (!chunkSize || len < chunkSize) {\n        yield chunk;\n        return;\n    }\n    let pos = 0;\n    let end;\n    while(pos < len){\n        end = pos + chunkSize;\n        yield chunk.slice(pos, end);\n        pos = end;\n    }\n};\nconst readBytes = async function*(iterable, chunkSize) {\n    for await (const chunk of readStream(iterable)){\n        yield* streamChunk(chunk, chunkSize);\n    }\n};\nconst readStream = async function*(stream) {\n    if (stream[Symbol.asyncIterator]) {\n        yield* stream;\n        return;\n    }\n    const reader = stream.getReader();\n    try {\n        for(;;){\n            const { done, value } = await reader.read();\n            if (done) {\n                break;\n            }\n            yield value;\n        }\n    } finally{\n        await reader.cancel();\n    }\n};\nconst trackStream = (stream, chunkSize, onProgress, onFinish)=>{\n    const iterator = readBytes(stream, chunkSize);\n    let bytes = 0;\n    let done;\n    let _onFinish = (e)=>{\n        if (!done) {\n            done = true;\n            onFinish && onFinish(e);\n        }\n    };\n    return new ReadableStream({\n        async pull (controller) {\n            try {\n                const { done, value } = await iterator.next();\n                if (done) {\n                    _onFinish();\n                    controller.close();\n                    return;\n                }\n                let len = value.byteLength;\n                if (onProgress) {\n                    let loadedBytes = bytes += len;\n                    onProgress(loadedBytes);\n                }\n                controller.enqueue(new Uint8Array(value));\n            } catch (err) {\n                _onFinish(err);\n                throw err;\n            }\n        },\n        cancel (reason) {\n            _onFinish(reason);\n            return iterator.return();\n        }\n    }, {\n        highWaterMark: 2\n    });\n};\nconst isFetchSupported = typeof fetch === \"function\" && typeof Request === \"function\" && typeof Response === \"function\";\nconst isReadableStreamSupported = isFetchSupported && typeof ReadableStream === \"function\";\n// used only inside the fetch adapter\nconst encodeText = isFetchSupported && (typeof TextEncoder === \"function\" ? ((encoder)=>(str)=>encoder.encode(str))(new TextEncoder()) : async (str)=>new Uint8Array(await new Response(str).arrayBuffer()));\nconst test = (fn, ...args)=>{\n    try {\n        return !!fn(...args);\n    } catch (e) {\n        return false;\n    }\n};\nconst supportsRequestStream = isReadableStreamSupported && test(()=>{\n    let duplexAccessed = false;\n    const hasContentType = new Request(platform.origin, {\n        body: new ReadableStream(),\n        method: \"POST\",\n        get duplex () {\n            duplexAccessed = true;\n            return \"half\";\n        }\n    }).headers.has(\"Content-Type\");\n    return duplexAccessed && !hasContentType;\n});\nconst DEFAULT_CHUNK_SIZE = 64 * 1024;\nconst supportsResponseStream = isReadableStreamSupported && test(()=>utils$1.isReadableStream(new Response(\"\").body));\nconst resolvers = {\n    stream: supportsResponseStream && ((res)=>res.body)\n};\nisFetchSupported && ((res)=>{\n    [\n        \"text\",\n        \"arrayBuffer\",\n        \"blob\",\n        \"formData\",\n        \"stream\"\n    ].forEach((type)=>{\n        !resolvers[type] && (resolvers[type] = utils$1.isFunction(res[type]) ? (res)=>res[type]() : (_, config)=>{\n            throw new AxiosError(`Response type '${type}' is not supported`, AxiosError.ERR_NOT_SUPPORT, config);\n        });\n    });\n})(new Response);\nconst getBodyLength = async (body)=>{\n    if (body == null) {\n        return 0;\n    }\n    if (utils$1.isBlob(body)) {\n        return body.size;\n    }\n    if (utils$1.isSpecCompliantForm(body)) {\n        const _request = new Request(platform.origin, {\n            method: \"POST\",\n            body\n        });\n        return (await _request.arrayBuffer()).byteLength;\n    }\n    if (utils$1.isArrayBufferView(body) || utils$1.isArrayBuffer(body)) {\n        return body.byteLength;\n    }\n    if (utils$1.isURLSearchParams(body)) {\n        body = body + \"\";\n    }\n    if (utils$1.isString(body)) {\n        return (await encodeText(body)).byteLength;\n    }\n};\nconst resolveBodyLength = async (headers, body)=>{\n    const length = utils$1.toFiniteNumber(headers.getContentLength());\n    return length == null ? getBodyLength(body) : length;\n};\nconst fetchAdapter = isFetchSupported && (async (config)=>{\n    let { url, method, data, signal, cancelToken, timeout, onDownloadProgress, onUploadProgress, responseType, headers, withCredentials = \"same-origin\", fetchOptions } = resolveConfig(config);\n    responseType = responseType ? (responseType + \"\").toLowerCase() : \"text\";\n    let composedSignal = composeSignals$1([\n        signal,\n        cancelToken && cancelToken.toAbortSignal()\n    ], timeout);\n    let request;\n    const unsubscribe = composedSignal && composedSignal.unsubscribe && (()=>{\n        composedSignal.unsubscribe();\n    });\n    let requestContentLength;\n    try {\n        if (onUploadProgress && supportsRequestStream && method !== \"get\" && method !== \"head\" && (requestContentLength = await resolveBodyLength(headers, data)) !== 0) {\n            let _request = new Request(url, {\n                method: \"POST\",\n                body: data,\n                duplex: \"half\"\n            });\n            let contentTypeHeader;\n            if (utils$1.isFormData(data) && (contentTypeHeader = _request.headers.get(\"content-type\"))) {\n                headers.setContentType(contentTypeHeader);\n            }\n            if (_request.body) {\n                const [onProgress, flush] = progressEventDecorator(requestContentLength, progressEventReducer(asyncDecorator(onUploadProgress)));\n                data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);\n            }\n        }\n        if (!utils$1.isString(withCredentials)) {\n            withCredentials = withCredentials ? \"include\" : \"omit\";\n        }\n        // Cloudflare Workers throws when credentials are defined\n        // see https://github.com/cloudflare/workerd/issues/902\n        const isCredentialsSupported = \"credentials\" in Request.prototype;\n        request = new Request(url, {\n            ...fetchOptions,\n            signal: composedSignal,\n            method: method.toUpperCase(),\n            headers: headers.normalize().toJSON(),\n            body: data,\n            duplex: \"half\",\n            credentials: isCredentialsSupported ? withCredentials : undefined\n        });\n        let response = await fetch(request, fetchOptions);\n        const isStreamResponse = supportsResponseStream && (responseType === \"stream\" || responseType === \"response\");\n        if (supportsResponseStream && (onDownloadProgress || isStreamResponse && unsubscribe)) {\n            const options = {};\n            [\n                \"status\",\n                \"statusText\",\n                \"headers\"\n            ].forEach((prop)=>{\n                options[prop] = response[prop];\n            });\n            const responseContentLength = utils$1.toFiniteNumber(response.headers.get(\"content-length\"));\n            const [onProgress, flush] = onDownloadProgress && progressEventDecorator(responseContentLength, progressEventReducer(asyncDecorator(onDownloadProgress), true)) || [];\n            response = new Response(trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, ()=>{\n                flush && flush();\n                unsubscribe && unsubscribe();\n            }), options);\n        }\n        responseType = responseType || \"text\";\n        let responseData = await resolvers[utils$1.findKey(resolvers, responseType) || \"text\"](response, config);\n        !isStreamResponse && unsubscribe && unsubscribe();\n        return await new Promise((resolve, reject)=>{\n            settle(resolve, reject, {\n                data: responseData,\n                headers: AxiosHeaders$1.from(response.headers),\n                status: response.status,\n                statusText: response.statusText,\n                config,\n                request\n            });\n        });\n    } catch (err) {\n        unsubscribe && unsubscribe();\n        if (err && err.name === \"TypeError\" && /Load failed|fetch/i.test(err.message)) {\n            throw Object.assign(new AxiosError(\"Network Error\", AxiosError.ERR_NETWORK, config, request), {\n                cause: err.cause || err\n            });\n        }\n        throw AxiosError.from(err, err && err.code, config, request);\n    }\n});\nconst knownAdapters = {\n    http: httpAdapter,\n    xhr: xhrAdapter,\n    fetch: fetchAdapter\n};\nutils$1.forEach(knownAdapters, (fn, value)=>{\n    if (fn) {\n        try {\n            Object.defineProperty(fn, \"name\", {\n                value\n            });\n        } catch (e) {\n        // eslint-disable-next-line no-empty\n        }\n        Object.defineProperty(fn, \"adapterName\", {\n            value\n        });\n    }\n});\nconst renderReason = (reason)=>`- ${reason}`;\nconst isResolvedHandle = (adapter)=>utils$1.isFunction(adapter) || adapter === null || adapter === false;\nconst adapters = {\n    getAdapter: (adapters)=>{\n        adapters = utils$1.isArray(adapters) ? adapters : [\n            adapters\n        ];\n        const { length } = adapters;\n        let nameOrAdapter;\n        let adapter;\n        const rejectedReasons = {};\n        for(let i = 0; i < length; i++){\n            nameOrAdapter = adapters[i];\n            let id;\n            adapter = nameOrAdapter;\n            if (!isResolvedHandle(nameOrAdapter)) {\n                adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];\n                if (adapter === undefined) {\n                    throw new AxiosError(`Unknown adapter '${id}'`);\n                }\n            }\n            if (adapter) {\n                break;\n            }\n            rejectedReasons[id || \"#\" + i] = adapter;\n        }\n        if (!adapter) {\n            const reasons = Object.entries(rejectedReasons).map(([id, state])=>`adapter ${id} ` + (state === false ? \"is not supported by the environment\" : \"is not available in the build\"));\n            let s = length ? reasons.length > 1 ? \"since :\\n\" + reasons.map(renderReason).join(\"\\n\") : \" \" + renderReason(reasons[0]) : \"as no adapter specified\";\n            throw new AxiosError(`There is no suitable adapter to dispatch the request ` + s, \"ERR_NOT_SUPPORT\");\n        }\n        return adapter;\n    },\n    adapters: knownAdapters\n};\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n *\n * @param {Object} config The config that is to be used for the request\n *\n * @returns {void}\n */ function throwIfCancellationRequested(config) {\n    if (config.cancelToken) {\n        config.cancelToken.throwIfRequested();\n    }\n    if (config.signal && config.signal.aborted) {\n        throw new CanceledError(null, config);\n    }\n}\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n *\n * @returns {Promise} The Promise to be fulfilled\n */ function dispatchRequest(config) {\n    throwIfCancellationRequested(config);\n    config.headers = AxiosHeaders$1.from(config.headers);\n    // Transform request data\n    config.data = transformData.call(config, config.transformRequest);\n    if ([\n        \"post\",\n        \"put\",\n        \"patch\"\n    ].indexOf(config.method) !== -1) {\n        config.headers.setContentType(\"application/x-www-form-urlencoded\", false);\n    }\n    const adapter = adapters.getAdapter(config.adapter || defaults$1.adapter);\n    return adapter(config).then(function onAdapterResolution(response) {\n        throwIfCancellationRequested(config);\n        // Transform response data\n        response.data = transformData.call(config, config.transformResponse, response);\n        response.headers = AxiosHeaders$1.from(response.headers);\n        return response;\n    }, function onAdapterRejection(reason) {\n        if (!isCancel(reason)) {\n            throwIfCancellationRequested(config);\n            // Transform response data\n            if (reason && reason.response) {\n                reason.response.data = transformData.call(config, config.transformResponse, reason.response);\n                reason.response.headers = AxiosHeaders$1.from(reason.response.headers);\n            }\n        }\n        return Promise.reject(reason);\n    });\n}\nconst validators$1 = {};\n// eslint-disable-next-line func-names\n[\n    \"object\",\n    \"boolean\",\n    \"number\",\n    \"function\",\n    \"string\",\n    \"symbol\"\n].forEach((type, i)=>{\n    validators$1[type] = function validator(thing) {\n        return typeof thing === type || \"a\" + (i < 1 ? \"n \" : \" \") + type;\n    };\n});\nconst deprecatedWarnings = {};\n/**\n * Transitional option validator\n *\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n *\n * @returns {function}\n */ validators$1.transitional = function transitional(validator, version, message) {\n    function formatMessage(opt, desc) {\n        return \"[Axios v\" + VERSION + \"] Transitional option '\" + opt + \"'\" + desc + (message ? \". \" + message : \"\");\n    }\n    // eslint-disable-next-line func-names\n    return (value, opt, opts)=>{\n        if (validator === false) {\n            throw new AxiosError(formatMessage(opt, \" has been removed\" + (version ? \" in \" + version : \"\")), AxiosError.ERR_DEPRECATED);\n        }\n        if (version && !deprecatedWarnings[opt]) {\n            deprecatedWarnings[opt] = true;\n            // eslint-disable-next-line no-console\n            console.warn(formatMessage(opt, \" has been deprecated since v\" + version + \" and will be removed in the near future\"));\n        }\n        return validator ? validator(value, opt, opts) : true;\n    };\n};\nvalidators$1.spelling = function spelling(correctSpelling) {\n    return (value, opt)=>{\n        // eslint-disable-next-line no-console\n        console.warn(`${opt} is likely a misspelling of ${correctSpelling}`);\n        return true;\n    };\n};\n/**\n * Assert object's properties type\n *\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n *\n * @returns {object}\n */ function assertOptions(options, schema, allowUnknown) {\n    if (typeof options !== \"object\") {\n        throw new AxiosError(\"options must be an object\", AxiosError.ERR_BAD_OPTION_VALUE);\n    }\n    const keys = Object.keys(options);\n    let i = keys.length;\n    while(i-- > 0){\n        const opt = keys[i];\n        const validator = schema[opt];\n        if (validator) {\n            const value = options[opt];\n            const result = value === undefined || validator(value, opt, options);\n            if (result !== true) {\n                throw new AxiosError(\"option \" + opt + \" must be \" + result, AxiosError.ERR_BAD_OPTION_VALUE);\n            }\n            continue;\n        }\n        if (allowUnknown !== true) {\n            throw new AxiosError(\"Unknown option \" + opt, AxiosError.ERR_BAD_OPTION);\n        }\n    }\n}\nconst validator = {\n    assertOptions,\n    validators: validators$1\n};\nconst validators = validator.validators;\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n *\n * @return {Axios} A new instance of Axios\n */ class Axios {\n    constructor(instanceConfig){\n        this.defaults = instanceConfig || {};\n        this.interceptors = {\n            request: new InterceptorManager$1(),\n            response: new InterceptorManager$1()\n        };\n    }\n    /**\n   * Dispatch a request\n   *\n   * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)\n   * @param {?Object} config\n   *\n   * @returns {Promise} The Promise to be fulfilled\n   */ async request(configOrUrl, config) {\n        try {\n            return await this._request(configOrUrl, config);\n        } catch (err) {\n            if (err instanceof Error) {\n                let dummy = {};\n                Error.captureStackTrace ? Error.captureStackTrace(dummy) : dummy = new Error();\n                // slice off the Error: ... line\n                const stack = dummy.stack ? dummy.stack.replace(/^.+\\n/, \"\") : \"\";\n                try {\n                    if (!err.stack) {\n                        err.stack = stack;\n                    // match without the 2 top stack lines\n                    } else if (stack && !String(err.stack).endsWith(stack.replace(/^.+\\n.+\\n/, \"\"))) {\n                        err.stack += \"\\n\" + stack;\n                    }\n                } catch (e) {\n                // ignore the case where \"stack\" is an un-writable property\n                }\n            }\n            throw err;\n        }\n    }\n    _request(configOrUrl, config) {\n        /*eslint no-param-reassign:0*/ // Allow for axios('example/url'[, config]) a la fetch API\n        if (typeof configOrUrl === \"string\") {\n            config = config || {};\n            config.url = configOrUrl;\n        } else {\n            config = configOrUrl || {};\n        }\n        config = mergeConfig(this.defaults, config);\n        const { transitional, paramsSerializer, headers } = config;\n        if (transitional !== undefined) {\n            validator.assertOptions(transitional, {\n                silentJSONParsing: validators.transitional(validators.boolean),\n                forcedJSONParsing: validators.transitional(validators.boolean),\n                clarifyTimeoutError: validators.transitional(validators.boolean)\n            }, false);\n        }\n        if (paramsSerializer != null) {\n            if (utils$1.isFunction(paramsSerializer)) {\n                config.paramsSerializer = {\n                    serialize: paramsSerializer\n                };\n            } else {\n                validator.assertOptions(paramsSerializer, {\n                    encode: validators.function,\n                    serialize: validators.function\n                }, true);\n            }\n        }\n        // Set config.allowAbsoluteUrls\n        if (config.allowAbsoluteUrls !== undefined) ;\n        else if (this.defaults.allowAbsoluteUrls !== undefined) {\n            config.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls;\n        } else {\n            config.allowAbsoluteUrls = true;\n        }\n        validator.assertOptions(config, {\n            baseUrl: validators.spelling(\"baseURL\"),\n            withXsrfToken: validators.spelling(\"withXSRFToken\")\n        }, true);\n        // Set config.method\n        config.method = (config.method || this.defaults.method || \"get\").toLowerCase();\n        // Flatten headers\n        let contextHeaders = headers && utils$1.merge(headers.common, headers[config.method]);\n        headers && utils$1.forEach([\n            \"delete\",\n            \"get\",\n            \"head\",\n            \"post\",\n            \"put\",\n            \"patch\",\n            \"common\"\n        ], (method)=>{\n            delete headers[method];\n        });\n        config.headers = AxiosHeaders$1.concat(contextHeaders, headers);\n        // filter out skipped interceptors\n        const requestInterceptorChain = [];\n        let synchronousRequestInterceptors = true;\n        this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n            if (typeof interceptor.runWhen === \"function\" && interceptor.runWhen(config) === false) {\n                return;\n            }\n            synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;\n            requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);\n        });\n        const responseInterceptorChain = [];\n        this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n            responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);\n        });\n        let promise;\n        let i = 0;\n        let len;\n        if (!synchronousRequestInterceptors) {\n            const chain = [\n                dispatchRequest.bind(this),\n                undefined\n            ];\n            chain.unshift(...requestInterceptorChain);\n            chain.push(...responseInterceptorChain);\n            len = chain.length;\n            promise = Promise.resolve(config);\n            while(i < len){\n                promise = promise.then(chain[i++], chain[i++]);\n            }\n            return promise;\n        }\n        len = requestInterceptorChain.length;\n        let newConfig = config;\n        i = 0;\n        while(i < len){\n            const onFulfilled = requestInterceptorChain[i++];\n            const onRejected = requestInterceptorChain[i++];\n            try {\n                newConfig = onFulfilled(newConfig);\n            } catch (error) {\n                onRejected.call(this, error);\n                break;\n            }\n        }\n        try {\n            promise = dispatchRequest.call(this, newConfig);\n        } catch (error) {\n            return Promise.reject(error);\n        }\n        i = 0;\n        len = responseInterceptorChain.length;\n        while(i < len){\n            promise = promise.then(responseInterceptorChain[i++], responseInterceptorChain[i++]);\n        }\n        return promise;\n    }\n    getUri(config) {\n        config = mergeConfig(this.defaults, config);\n        const fullPath = buildFullPath(config.baseURL, config.url, config.allowAbsoluteUrls);\n        return buildURL(fullPath, config.params, config.paramsSerializer);\n    }\n}\n// Provide aliases for supported request methods\nutils$1.forEach([\n    \"delete\",\n    \"get\",\n    \"head\",\n    \"options\"\n], function forEachMethodNoData(method) {\n    /*eslint func-names:0*/ Axios.prototype[method] = function(url, config) {\n        return this.request(mergeConfig(config || {}, {\n            method,\n            url,\n            data: (config || {}).data\n        }));\n    };\n});\nutils$1.forEach([\n    \"post\",\n    \"put\",\n    \"patch\"\n], function forEachMethodWithData(method) {\n    /*eslint func-names:0*/ function generateHTTPMethod(isForm) {\n        return function httpMethod(url, data, config) {\n            return this.request(mergeConfig(config || {}, {\n                method,\n                headers: isForm ? {\n                    \"Content-Type\": \"multipart/form-data\"\n                } : {},\n                url,\n                data\n            }));\n        };\n    }\n    Axios.prototype[method] = generateHTTPMethod();\n    Axios.prototype[method + \"Form\"] = generateHTTPMethod(true);\n});\nconst Axios$1 = Axios;\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @param {Function} executor The executor function.\n *\n * @returns {CancelToken}\n */ class CancelToken {\n    constructor(executor){\n        if (typeof executor !== \"function\") {\n            throw new TypeError(\"executor must be a function.\");\n        }\n        let resolvePromise;\n        this.promise = new Promise(function promiseExecutor(resolve) {\n            resolvePromise = resolve;\n        });\n        const token = this;\n        // eslint-disable-next-line func-names\n        this.promise.then((cancel)=>{\n            if (!token._listeners) return;\n            let i = token._listeners.length;\n            while(i-- > 0){\n                token._listeners[i](cancel);\n            }\n            token._listeners = null;\n        });\n        // eslint-disable-next-line func-names\n        this.promise.then = (onfulfilled)=>{\n            let _resolve;\n            // eslint-disable-next-line func-names\n            const promise = new Promise((resolve)=>{\n                token.subscribe(resolve);\n                _resolve = resolve;\n            }).then(onfulfilled);\n            promise.cancel = function reject() {\n                token.unsubscribe(_resolve);\n            };\n            return promise;\n        };\n        executor(function cancel(message, config, request) {\n            if (token.reason) {\n                // Cancellation has already been requested\n                return;\n            }\n            token.reason = new CanceledError(message, config, request);\n            resolvePromise(token.reason);\n        });\n    }\n    /**\n   * Throws a `CanceledError` if cancellation has been requested.\n   */ throwIfRequested() {\n        if (this.reason) {\n            throw this.reason;\n        }\n    }\n    /**\n   * Subscribe to the cancel signal\n   */ subscribe(listener) {\n        if (this.reason) {\n            listener(this.reason);\n            return;\n        }\n        if (this._listeners) {\n            this._listeners.push(listener);\n        } else {\n            this._listeners = [\n                listener\n            ];\n        }\n    }\n    /**\n   * Unsubscribe from the cancel signal\n   */ unsubscribe(listener) {\n        if (!this._listeners) {\n            return;\n        }\n        const index = this._listeners.indexOf(listener);\n        if (index !== -1) {\n            this._listeners.splice(index, 1);\n        }\n    }\n    toAbortSignal() {\n        const controller = new AbortController();\n        const abort = (err)=>{\n            controller.abort(err);\n        };\n        this.subscribe(abort);\n        controller.signal.unsubscribe = ()=>this.unsubscribe(abort);\n        return controller.signal;\n    }\n    /**\n   * Returns an object that contains a new `CancelToken` and a function that, when called,\n   * cancels the `CancelToken`.\n   */ static source() {\n        let cancel;\n        const token = new CancelToken(function executor(c) {\n            cancel = c;\n        });\n        return {\n            token,\n            cancel\n        };\n    }\n}\nconst CancelToken$1 = CancelToken;\n/**\n * Syntactic sugar for invoking a function and expanding an array for arguments.\n *\n * Common use case would be to use `Function.prototype.apply`.\n *\n *  ```js\n *  function f(x, y, z) {}\n *  var args = [1, 2, 3];\n *  f.apply(null, args);\n *  ```\n *\n * With `spread` this example can be re-written.\n *\n *  ```js\n *  spread(function(x, y, z) {})([1, 2, 3]);\n *  ```\n *\n * @param {Function} callback\n *\n * @returns {Function}\n */ function spread(callback) {\n    return function wrap(arr) {\n        return callback.apply(null, arr);\n    };\n}\n/**\n * Determines whether the payload is an error thrown by Axios\n *\n * @param {*} payload The value to test\n *\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */ function isAxiosError(payload) {\n    return utils$1.isObject(payload) && payload.isAxiosError === true;\n}\nconst HttpStatusCode = {\n    Continue: 100,\n    SwitchingProtocols: 101,\n    Processing: 102,\n    EarlyHints: 103,\n    Ok: 200,\n    Created: 201,\n    Accepted: 202,\n    NonAuthoritativeInformation: 203,\n    NoContent: 204,\n    ResetContent: 205,\n    PartialContent: 206,\n    MultiStatus: 207,\n    AlreadyReported: 208,\n    ImUsed: 226,\n    MultipleChoices: 300,\n    MovedPermanently: 301,\n    Found: 302,\n    SeeOther: 303,\n    NotModified: 304,\n    UseProxy: 305,\n    Unused: 306,\n    TemporaryRedirect: 307,\n    PermanentRedirect: 308,\n    BadRequest: 400,\n    Unauthorized: 401,\n    PaymentRequired: 402,\n    Forbidden: 403,\n    NotFound: 404,\n    MethodNotAllowed: 405,\n    NotAcceptable: 406,\n    ProxyAuthenticationRequired: 407,\n    RequestTimeout: 408,\n    Conflict: 409,\n    Gone: 410,\n    LengthRequired: 411,\n    PreconditionFailed: 412,\n    PayloadTooLarge: 413,\n    UriTooLong: 414,\n    UnsupportedMediaType: 415,\n    RangeNotSatisfiable: 416,\n    ExpectationFailed: 417,\n    ImATeapot: 418,\n    MisdirectedRequest: 421,\n    UnprocessableEntity: 422,\n    Locked: 423,\n    FailedDependency: 424,\n    TooEarly: 425,\n    UpgradeRequired: 426,\n    PreconditionRequired: 428,\n    TooManyRequests: 429,\n    RequestHeaderFieldsTooLarge: 431,\n    UnavailableForLegalReasons: 451,\n    InternalServerError: 500,\n    NotImplemented: 501,\n    BadGateway: 502,\n    ServiceUnavailable: 503,\n    GatewayTimeout: 504,\n    HttpVersionNotSupported: 505,\n    VariantAlsoNegotiates: 506,\n    InsufficientStorage: 507,\n    LoopDetected: 508,\n    NotExtended: 510,\n    NetworkAuthenticationRequired: 511\n};\nObject.entries(HttpStatusCode).forEach(([key, value])=>{\n    HttpStatusCode[value] = key;\n});\nconst HttpStatusCode$1 = HttpStatusCode;\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n *\n * @returns {Axios} A new instance of Axios\n */ function createInstance(defaultConfig) {\n    const context = new Axios$1(defaultConfig);\n    const instance = bind(Axios$1.prototype.request, context);\n    // Copy axios.prototype to instance\n    utils$1.extend(instance, Axios$1.prototype, context, {\n        allOwnKeys: true\n    });\n    // Copy context to instance\n    utils$1.extend(instance, context, null, {\n        allOwnKeys: true\n    });\n    // Factory for creating new instances\n    instance.create = function create(instanceConfig) {\n        return createInstance(mergeConfig(defaultConfig, instanceConfig));\n    };\n    return instance;\n}\n// Create the default instance to be exported\nconst axios = createInstance(defaults$1);\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios$1;\n// Expose Cancel & CancelToken\naxios.CanceledError = CanceledError;\naxios.CancelToken = CancelToken$1;\naxios.isCancel = isCancel;\naxios.VERSION = VERSION;\naxios.toFormData = toFormData;\n// Expose AxiosError class\naxios.AxiosError = AxiosError;\n// alias for CanceledError for backward compatibility\naxios.Cancel = axios.CanceledError;\n// Expose all/spread\naxios.all = function all(promises) {\n    return Promise.all(promises);\n};\naxios.spread = spread;\n// Expose isAxiosError\naxios.isAxiosError = isAxiosError;\n// Expose mergeConfig\naxios.mergeConfig = mergeConfig;\naxios.AxiosHeaders = AxiosHeaders$1;\naxios.formToJSON = (thing)=>formDataToJSON(utils$1.isHTMLForm(thing) ? new FormData(thing) : thing);\naxios.getAdapter = adapters.getAdapter;\naxios.HttpStatusCode = HttpStatusCode$1;\naxios.default = axios;\nmodule.exports = axios; //# sourceMappingURL=axios.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/axios/dist/node/axios.cjs\n");

/***/ })

};
;