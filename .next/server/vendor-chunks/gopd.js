"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gopd";
exports.ids = ["vendor-chunks/gopd"];
exports.modules = {

/***/ "(rsc)/./node_modules/gopd/gOPD.js":
/*!***********************************!*\
  !*** ./node_modules/gopd/gOPD.js ***!
  \***********************************/
/***/ ((module) => {

eval("\n/** @type {import('./gOPD')} */ module.exports = Object.getOwnPropertyDescriptor;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ29wZC9nT1BELmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsNkJBQTZCLEdBQzdCQSxPQUFPQyxPQUFPLEdBQUdDLE9BQU9DLHdCQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zY3JhcGVyLy4vbm9kZV9tb2R1bGVzL2dvcGQvZ09QRC5qcz8xMDYxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqIEB0eXBlIHtpbXBvcnQoJy4vZ09QRCcpfSAqL1xubW9kdWxlLmV4cG9ydHMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJPYmplY3QiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gopd/gOPD.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gopd/index.js":
/*!************************************!*\
  !*** ./node_modules/gopd/index.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/** @type {import('.')} */ var $gOPD = __webpack_require__(/*! ./gOPD */ \"(rsc)/./node_modules/gopd/gOPD.js\");\nif ($gOPD) {\n    try {\n        $gOPD([], \"length\");\n    } catch (e) {\n        // IE 8 has a broken gOPD\n        $gOPD = null;\n    }\n}\nmodule.exports = $gOPD;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ29wZC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLHdCQUF3QixHQUN4QixJQUFJQSxRQUFRQyxtQkFBT0EsQ0FBQztBQUVwQixJQUFJRCxPQUFPO0lBQ1YsSUFBSTtRQUNIQSxNQUFNLEVBQUUsRUFBRTtJQUNYLEVBQUUsT0FBT0UsR0FBRztRQUNYLHlCQUF5QjtRQUN6QkYsUUFBUTtJQUNUO0FBQ0Q7QUFFQUcsT0FBT0MsT0FBTyxHQUFHSiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zY3JhcGVyLy4vbm9kZV9tb2R1bGVzL2dvcGQvaW5kZXguanM/ODk1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbi8qKiBAdHlwZSB7aW1wb3J0KCcuJyl9ICovXG52YXIgJGdPUEQgPSByZXF1aXJlKCcuL2dPUEQnKTtcblxuaWYgKCRnT1BEKSB7XG5cdHRyeSB7XG5cdFx0JGdPUEQoW10sICdsZW5ndGgnKTtcblx0fSBjYXRjaCAoZSkge1xuXHRcdC8vIElFIDggaGFzIGEgYnJva2VuIGdPUERcblx0XHQkZ09QRCA9IG51bGw7XG5cdH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSAkZ09QRDtcbiJdLCJuYW1lcyI6WyIkZ09QRCIsInJlcXVpcmUiLCJlIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gopd/index.js\n");

/***/ })

};
;