/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lodash.isequal";
exports.ids = ["vendor-chunks/lodash.isequal"];
exports.modules = {

/***/ "(rsc)/./node_modules/lodash.isequal/index.js":
/*!**********************************************!*\
  !*** ./node_modules/lodash.isequal/index.js ***!
  \**********************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\n/**\n * Lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright JS Foundation and other contributors <https://js.foundation/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors\n */ /** Used as the size to enable large array optimizations. */ var LARGE_ARRAY_SIZE = 200;\n/** Used to stand-in for `undefined` hash values. */ var HASH_UNDEFINED = \"__lodash_hash_undefined__\";\n/** Used to compose bitmasks for value comparisons. */ var COMPARE_PARTIAL_FLAG = 1, COMPARE_UNORDERED_FLAG = 2;\n/** Used as references for various `Number` constants. */ var MAX_SAFE_INTEGER = 9007199254740991;\n/** `Object#toString` result references. */ var argsTag = \"[object Arguments]\", arrayTag = \"[object Array]\", asyncTag = \"[object AsyncFunction]\", boolTag = \"[object Boolean]\", dateTag = \"[object Date]\", errorTag = \"[object Error]\", funcTag = \"[object Function]\", genTag = \"[object GeneratorFunction]\", mapTag = \"[object Map]\", numberTag = \"[object Number]\", nullTag = \"[object Null]\", objectTag = \"[object Object]\", promiseTag = \"[object Promise]\", proxyTag = \"[object Proxy]\", regexpTag = \"[object RegExp]\", setTag = \"[object Set]\", stringTag = \"[object String]\", symbolTag = \"[object Symbol]\", undefinedTag = \"[object Undefined]\", weakMapTag = \"[object WeakMap]\";\nvar arrayBufferTag = \"[object ArrayBuffer]\", dataViewTag = \"[object DataView]\", float32Tag = \"[object Float32Array]\", float64Tag = \"[object Float64Array]\", int8Tag = \"[object Int8Array]\", int16Tag = \"[object Int16Array]\", int32Tag = \"[object Int32Array]\", uint8Tag = \"[object Uint8Array]\", uint8ClampedTag = \"[object Uint8ClampedArray]\", uint16Tag = \"[object Uint16Array]\", uint32Tag = \"[object Uint32Array]\";\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */ var reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n/** Used to detect host constructors (Safari). */ var reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n/** Used to detect unsigned integer values. */ var reIsUint = /^(?:0|[1-9]\\d*)$/;\n/** Used to identify `toStringTag` values of typed arrays. */ var typedArrayTags = {};\ntypedArrayTags[float32Tag] = typedArrayTags[float64Tag] = typedArrayTags[int8Tag] = typedArrayTags[int16Tag] = typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] = typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] = typedArrayTags[uint32Tag] = true;\ntypedArrayTags[argsTag] = typedArrayTags[arrayTag] = typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] = typedArrayTags[dataViewTag] = typedArrayTags[dateTag] = typedArrayTags[errorTag] = typedArrayTags[funcTag] = typedArrayTags[mapTag] = typedArrayTags[numberTag] = typedArrayTags[objectTag] = typedArrayTags[regexpTag] = typedArrayTags[setTag] = typedArrayTags[stringTag] = typedArrayTags[weakMapTag] = false;\n/** Detect free variable `global` from Node.js. */ var freeGlobal = typeof global == \"object\" && global && global.Object === Object && global;\n/** Detect free variable `self`. */ var freeSelf = typeof self == \"object\" && self && self.Object === Object && self;\n/** Used as a reference to the global object. */ var root = freeGlobal || freeSelf || Function(\"return this\")();\n/** Detect free variable `exports`. */ var freeExports =  true && exports && !exports.nodeType && exports;\n/** Detect free variable `module`. */ var freeModule = freeExports && \"object\" == \"object\" && module && !module.nodeType && module;\n/** Detect the popular CommonJS extension `module.exports`. */ var moduleExports = freeModule && freeModule.exports === freeExports;\n/** Detect free variable `process` from Node.js. */ var freeProcess = moduleExports && freeGlobal.process;\n/** Used to access faster Node.js helpers. */ var nodeUtil = function() {\n    try {\n        return freeProcess && freeProcess.binding && freeProcess.binding(\"util\");\n    } catch (e) {}\n}();\n/* Node.js helper references. */ var nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n/**\n * A specialized version of `_.filter` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */ function arrayFilter(array, predicate) {\n    var index = -1, length = array == null ? 0 : array.length, resIndex = 0, result = [];\n    while(++index < length){\n        var value = array[index];\n        if (predicate(value, index, array)) {\n            result[resIndex++] = value;\n        }\n    }\n    return result;\n}\n/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */ function arrayPush(array, values) {\n    var index = -1, length = values.length, offset = array.length;\n    while(++index < length){\n        array[offset + index] = values[index];\n    }\n    return array;\n}\n/**\n * A specialized version of `_.some` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */ function arraySome(array, predicate) {\n    var index = -1, length = array == null ? 0 : array.length;\n    while(++index < length){\n        if (predicate(array[index], index, array)) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */ function baseTimes(n, iteratee) {\n    var index = -1, result = Array(n);\n    while(++index < n){\n        result[index] = iteratee(index);\n    }\n    return result;\n}\n/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */ function baseUnary(func) {\n    return function(value) {\n        return func(value);\n    };\n}\n/**\n * Checks if a `cache` value for `key` exists.\n *\n * @private\n * @param {Object} cache The cache to query.\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function cacheHas(cache, key) {\n    return cache.has(key);\n}\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */ function getValue(object, key) {\n    return object == null ? undefined : object[key];\n}\n/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */ function mapToArray(map) {\n    var index = -1, result = Array(map.size);\n    map.forEach(function(value, key) {\n        result[++index] = [\n            key,\n            value\n        ];\n    });\n    return result;\n}\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */ function overArg(func, transform) {\n    return function(arg) {\n        return func(transform(arg));\n    };\n}\n/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */ function setToArray(set) {\n    var index = -1, result = Array(set.size);\n    set.forEach(function(value) {\n        result[++index] = value;\n    });\n    return result;\n}\n/** Used for built-in method references. */ var arrayProto = Array.prototype, funcProto = Function.prototype, objectProto = Object.prototype;\n/** Used to detect overreaching core-js shims. */ var coreJsData = root[\"__core-js_shared__\"];\n/** Used to resolve the decompiled source of functions. */ var funcToString = funcProto.toString;\n/** Used to check objects for own properties. */ var hasOwnProperty = objectProto.hasOwnProperty;\n/** Used to detect methods masquerading as native. */ var maskSrcKey = function() {\n    var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || \"\");\n    return uid ? \"Symbol(src)_1.\" + uid : \"\";\n}();\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */ var nativeObjectToString = objectProto.toString;\n/** Used to detect if a method is native. */ var reIsNative = RegExp(\"^\" + funcToString.call(hasOwnProperty).replace(reRegExpChar, \"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, \"$1.*?\") + \"$\");\n/** Built-in value references. */ var Buffer = moduleExports ? root.Buffer : undefined, Symbol = root.Symbol, Uint8Array = root.Uint8Array, propertyIsEnumerable = objectProto.propertyIsEnumerable, splice = arrayProto.splice, symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n/* Built-in method references for those with the same name as other `lodash` methods. */ var nativeGetSymbols = Object.getOwnPropertySymbols, nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined, nativeKeys = overArg(Object.keys, Object);\n/* Built-in method references that are verified to be native. */ var DataView = getNative(root, \"DataView\"), Map = getNative(root, \"Map\"), Promise = getNative(root, \"Promise\"), Set = getNative(root, \"Set\"), WeakMap = getNative(root, \"WeakMap\"), nativeCreate = getNative(Object, \"create\");\n/** Used to detect maps, sets, and weakmaps. */ var dataViewCtorString = toSource(DataView), mapCtorString = toSource(Map), promiseCtorString = toSource(Promise), setCtorString = toSource(Set), weakMapCtorString = toSource(WeakMap);\n/** Used to convert symbols to primitives and strings. */ var symbolProto = Symbol ? Symbol.prototype : undefined, symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function Hash(entries) {\n    var index = -1, length = entries == null ? 0 : entries.length;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */ function hashClear() {\n    this.__data__ = nativeCreate ? nativeCreate(null) : {};\n    this.size = 0;\n}\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function hashDelete(key) {\n    var result = this.has(key) && delete this.__data__[key];\n    this.size -= result ? 1 : 0;\n    return result;\n}\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function hashGet(key) {\n    var data = this.__data__;\n    if (nativeCreate) {\n        var result = data[key];\n        return result === HASH_UNDEFINED ? undefined : result;\n    }\n    return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function hashHas(key) {\n    var data = this.__data__;\n    return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */ function hashSet(key, value) {\n    var data = this.__data__;\n    this.size += this.has(key) ? 0 : 1;\n    data[key] = nativeCreate && value === undefined ? HASH_UNDEFINED : value;\n    return this;\n}\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype[\"delete\"] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function ListCache(entries) {\n    var index = -1, length = entries == null ? 0 : entries.length;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */ function listCacheClear() {\n    this.__data__ = [];\n    this.size = 0;\n}\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function listCacheDelete(key) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    if (index < 0) {\n        return false;\n    }\n    var lastIndex = data.length - 1;\n    if (index == lastIndex) {\n        data.pop();\n    } else {\n        splice.call(data, index, 1);\n    }\n    --this.size;\n    return true;\n}\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function listCacheGet(key) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    return index < 0 ? undefined : data[index][1];\n}\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function listCacheHas(key) {\n    return assocIndexOf(this.__data__, key) > -1;\n}\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */ function listCacheSet(key, value) {\n    var data = this.__data__, index = assocIndexOf(data, key);\n    if (index < 0) {\n        ++this.size;\n        data.push([\n            key,\n            value\n        ]);\n    } else {\n        data[index][1] = value;\n    }\n    return this;\n}\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype[\"delete\"] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function MapCache(entries) {\n    var index = -1, length = entries == null ? 0 : entries.length;\n    this.clear();\n    while(++index < length){\n        var entry = entries[index];\n        this.set(entry[0], entry[1]);\n    }\n}\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */ function mapCacheClear() {\n    this.size = 0;\n    this.__data__ = {\n        \"hash\": new Hash,\n        \"map\": new (Map || ListCache),\n        \"string\": new Hash\n    };\n}\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function mapCacheDelete(key) {\n    var result = getMapData(this, key)[\"delete\"](key);\n    this.size -= result ? 1 : 0;\n    return result;\n}\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function mapCacheGet(key) {\n    return getMapData(this, key).get(key);\n}\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function mapCacheHas(key) {\n    return getMapData(this, key).has(key);\n}\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */ function mapCacheSet(key, value) {\n    var data = getMapData(this, key), size = data.size;\n    data.set(key, value);\n    this.size += data.size == size ? 0 : 1;\n    return this;\n}\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype[\"delete\"] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */ function SetCache(values) {\n    var index = -1, length = values == null ? 0 : values.length;\n    this.__data__ = new MapCache;\n    while(++index < length){\n        this.add(values[index]);\n    }\n}\n/**\n * Adds `value` to the array cache.\n *\n * @private\n * @name add\n * @memberOf SetCache\n * @alias push\n * @param {*} value The value to cache.\n * @returns {Object} Returns the cache instance.\n */ function setCacheAdd(value) {\n    this.__data__.set(value, HASH_UNDEFINED);\n    return this;\n}\n/**\n * Checks if `value` is in the array cache.\n *\n * @private\n * @name has\n * @memberOf SetCache\n * @param {*} value The value to search for.\n * @returns {number} Returns `true` if `value` is found, else `false`.\n */ function setCacheHas(value) {\n    return this.__data__.has(value);\n}\n// Add methods to `SetCache`.\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */ function Stack(entries) {\n    var data = this.__data__ = new ListCache(entries);\n    this.size = data.size;\n}\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */ function stackClear() {\n    this.__data__ = new ListCache;\n    this.size = 0;\n}\n/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */ function stackDelete(key) {\n    var data = this.__data__, result = data[\"delete\"](key);\n    this.size = data.size;\n    return result;\n}\n/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */ function stackGet(key) {\n    return this.__data__.get(key);\n}\n/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */ function stackHas(key) {\n    return this.__data__.has(key);\n}\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */ function stackSet(key, value) {\n    var data = this.__data__;\n    if (data instanceof ListCache) {\n        var pairs = data.__data__;\n        if (!Map || pairs.length < LARGE_ARRAY_SIZE - 1) {\n            pairs.push([\n                key,\n                value\n            ]);\n            this.size = ++data.size;\n            return this;\n        }\n        data = this.__data__ = new MapCache(pairs);\n    }\n    data.set(key, value);\n    this.size = data.size;\n    return this;\n}\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype[\"delete\"] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */ function arrayLikeKeys(value, inherited) {\n    var isArr = isArray(value), isArg = !isArr && isArguments(value), isBuff = !isArr && !isArg && isBuffer(value), isType = !isArr && !isArg && !isBuff && isTypedArray(value), skipIndexes = isArr || isArg || isBuff || isType, result = skipIndexes ? baseTimes(value.length, String) : [], length = result.length;\n    for(var key in value){\n        if ((inherited || hasOwnProperty.call(value, key)) && !(skipIndexes && // Safari 9 has enumerable `arguments.length` in strict mode.\n        (key == \"length\" || // Node.js 0.10 has enumerable non-index properties on buffers.\n        isBuff && (key == \"offset\" || key == \"parent\") || // PhantomJS 2 has enumerable non-index properties on typed arrays.\n        isType && (key == \"buffer\" || key == \"byteLength\" || key == \"byteOffset\") || // Skip index properties.\n        isIndex(key, length)))) {\n            result.push(key);\n        }\n    }\n    return result;\n}\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */ function assocIndexOf(array, key) {\n    var length = array.length;\n    while(length--){\n        if (eq(array[length][0], key)) {\n            return length;\n        }\n    }\n    return -1;\n}\n/**\n * The base implementation of `getAllKeys` and `getAllKeysIn` which uses\n * `keysFunc` and `symbolsFunc` to get the enumerable property names and\n * symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @param {Function} symbolsFunc The function to get the symbols of `object`.\n * @returns {Array} Returns the array of property names and symbols.\n */ function baseGetAllKeys(object, keysFunc, symbolsFunc) {\n    var result = keysFunc(object);\n    return isArray(object) ? result : arrayPush(result, symbolsFunc(object));\n}\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */ function baseGetTag(value) {\n    if (value == null) {\n        return value === undefined ? undefinedTag : nullTag;\n    }\n    return symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);\n}\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */ function baseIsArguments(value) {\n    return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */ function baseIsEqual(value, other, bitmask, customizer, stack) {\n    if (value === other) {\n        return true;\n    }\n    if (value == null || other == null || !isObjectLike(value) && !isObjectLike(other)) {\n        return value !== value && other !== other;\n    }\n    return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\n/**\n * A specialized version of `baseIsEqual` for arrays and objects which performs\n * deep comparisons and tracks traversed objects enabling objects with circular\n * references to be compared.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} [stack] Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */ function baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\n    var objIsArr = isArray(object), othIsArr = isArray(other), objTag = objIsArr ? arrayTag : getTag(object), othTag = othIsArr ? arrayTag : getTag(other);\n    objTag = objTag == argsTag ? objectTag : objTag;\n    othTag = othTag == argsTag ? objectTag : othTag;\n    var objIsObj = objTag == objectTag, othIsObj = othTag == objectTag, isSameTag = objTag == othTag;\n    if (isSameTag && isBuffer(object)) {\n        if (!isBuffer(other)) {\n            return false;\n        }\n        objIsArr = true;\n        objIsObj = false;\n    }\n    if (isSameTag && !objIsObj) {\n        stack || (stack = new Stack);\n        return objIsArr || isTypedArray(object) ? equalArrays(object, other, bitmask, customizer, equalFunc, stack) : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);\n    }\n    if (!(bitmask & COMPARE_PARTIAL_FLAG)) {\n        var objIsWrapped = objIsObj && hasOwnProperty.call(object, \"__wrapped__\"), othIsWrapped = othIsObj && hasOwnProperty.call(other, \"__wrapped__\");\n        if (objIsWrapped || othIsWrapped) {\n            var objUnwrapped = objIsWrapped ? object.value() : object, othUnwrapped = othIsWrapped ? other.value() : other;\n            stack || (stack = new Stack);\n            return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\n        }\n    }\n    if (!isSameTag) {\n        return false;\n    }\n    stack || (stack = new Stack);\n    return equalObjects(object, other, bitmask, customizer, equalFunc, stack);\n}\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */ function baseIsNative(value) {\n    if (!isObject(value) || isMasked(value)) {\n        return false;\n    }\n    var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n    return pattern.test(toSource(value));\n}\n/**\n * The base implementation of `_.isTypedArray` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n */ function baseIsTypedArray(value) {\n    return isObjectLike(value) && isLength(value.length) && !!typedArrayTags[baseGetTag(value)];\n}\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */ function baseKeys(object) {\n    if (!isPrototype(object)) {\n        return nativeKeys(object);\n    }\n    var result = [];\n    for(var key in Object(object)){\n        if (hasOwnProperty.call(object, key) && key != \"constructor\") {\n            result.push(key);\n        }\n    }\n    return result;\n}\n/**\n * A specialized version of `baseIsEqualDeep` for arrays with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Array} array The array to compare.\n * @param {Array} other The other array to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `array` and `other` objects.\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\n */ function equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n    var isPartial = bitmask & COMPARE_PARTIAL_FLAG, arrLength = array.length, othLength = other.length;\n    if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n        return false;\n    }\n    // Assume cyclic values are equal.\n    var stacked = stack.get(array);\n    if (stacked && stack.get(other)) {\n        return stacked == other;\n    }\n    var index = -1, result = true, seen = bitmask & COMPARE_UNORDERED_FLAG ? new SetCache : undefined;\n    stack.set(array, other);\n    stack.set(other, array);\n    // Ignore non-index properties.\n    while(++index < arrLength){\n        var arrValue = array[index], othValue = other[index];\n        if (customizer) {\n            var compared = isPartial ? customizer(othValue, arrValue, index, other, array, stack) : customizer(arrValue, othValue, index, array, other, stack);\n        }\n        if (compared !== undefined) {\n            if (compared) {\n                continue;\n            }\n            result = false;\n            break;\n        }\n        // Recursively compare arrays (susceptible to call stack limits).\n        if (seen) {\n            if (!arraySome(other, function(othValue, othIndex) {\n                if (!cacheHas(seen, othIndex) && (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n                    return seen.push(othIndex);\n                }\n            })) {\n                result = false;\n                break;\n            }\n        } else if (!(arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n            result = false;\n            break;\n        }\n    }\n    stack[\"delete\"](array);\n    stack[\"delete\"](other);\n    return result;\n}\n/**\n * A specialized version of `baseIsEqualDeep` for comparing objects of\n * the same `toStringTag`.\n *\n * **Note:** This function only supports comparing values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {string} tag The `toStringTag` of the objects to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */ function equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\n    switch(tag){\n        case dataViewTag:\n            if (object.byteLength != other.byteLength || object.byteOffset != other.byteOffset) {\n                return false;\n            }\n            object = object.buffer;\n            other = other.buffer;\n        case arrayBufferTag:\n            if (object.byteLength != other.byteLength || !equalFunc(new Uint8Array(object), new Uint8Array(other))) {\n                return false;\n            }\n            return true;\n        case boolTag:\n        case dateTag:\n        case numberTag:\n            // Coerce booleans to `1` or `0` and dates to milliseconds.\n            // Invalid dates are coerced to `NaN`.\n            return eq(+object, +other);\n        case errorTag:\n            return object.name == other.name && object.message == other.message;\n        case regexpTag:\n        case stringTag:\n            // Coerce regexes to strings and treat strings, primitives and objects,\n            // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring\n            // for more details.\n            return object == other + \"\";\n        case mapTag:\n            var convert = mapToArray;\n        case setTag:\n            var isPartial = bitmask & COMPARE_PARTIAL_FLAG;\n            convert || (convert = setToArray);\n            if (object.size != other.size && !isPartial) {\n                return false;\n            }\n            // Assume cyclic values are equal.\n            var stacked = stack.get(object);\n            if (stacked) {\n                return stacked == other;\n            }\n            bitmask |= COMPARE_UNORDERED_FLAG;\n            // Recursively compare objects (susceptible to call stack limits).\n            stack.set(object, other);\n            var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\n            stack[\"delete\"](object);\n            return result;\n        case symbolTag:\n            if (symbolValueOf) {\n                return symbolValueOf.call(object) == symbolValueOf.call(other);\n            }\n    }\n    return false;\n}\n/**\n * A specialized version of `baseIsEqualDeep` for objects with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */ function equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n    var isPartial = bitmask & COMPARE_PARTIAL_FLAG, objProps = getAllKeys(object), objLength = objProps.length, othProps = getAllKeys(other), othLength = othProps.length;\n    if (objLength != othLength && !isPartial) {\n        return false;\n    }\n    var index = objLength;\n    while(index--){\n        var key = objProps[index];\n        if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\n            return false;\n        }\n    }\n    // Assume cyclic values are equal.\n    var stacked = stack.get(object);\n    if (stacked && stack.get(other)) {\n        return stacked == other;\n    }\n    var result = true;\n    stack.set(object, other);\n    stack.set(other, object);\n    var skipCtor = isPartial;\n    while(++index < objLength){\n        key = objProps[index];\n        var objValue = object[key], othValue = other[key];\n        if (customizer) {\n            var compared = isPartial ? customizer(othValue, objValue, key, other, object, stack) : customizer(objValue, othValue, key, object, other, stack);\n        }\n        // Recursively compare objects (susceptible to call stack limits).\n        if (!(compared === undefined ? objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack) : compared)) {\n            result = false;\n            break;\n        }\n        skipCtor || (skipCtor = key == \"constructor\");\n    }\n    if (result && !skipCtor) {\n        var objCtor = object.constructor, othCtor = other.constructor;\n        // Non `Object` object instances with different constructors are not equal.\n        if (objCtor != othCtor && \"constructor\" in object && \"constructor\" in other && !(typeof objCtor == \"function\" && objCtor instanceof objCtor && typeof othCtor == \"function\" && othCtor instanceof othCtor)) {\n            result = false;\n        }\n    }\n    stack[\"delete\"](object);\n    stack[\"delete\"](other);\n    return result;\n}\n/**\n * Creates an array of own enumerable property names and symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names and symbols.\n */ function getAllKeys(object) {\n    return baseGetAllKeys(object, keys, getSymbols);\n}\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */ function getMapData(map, key) {\n    var data = map.__data__;\n    return isKeyable(key) ? data[typeof key == \"string\" ? \"string\" : \"hash\"] : data.map;\n}\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */ function getNative(object, key) {\n    var value = getValue(object, key);\n    return baseIsNative(value) ? value : undefined;\n}\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */ function getRawTag(value) {\n    var isOwn = hasOwnProperty.call(value, symToStringTag), tag = value[symToStringTag];\n    try {\n        value[symToStringTag] = undefined;\n        var unmasked = true;\n    } catch (e) {}\n    var result = nativeObjectToString.call(value);\n    if (unmasked) {\n        if (isOwn) {\n            value[symToStringTag] = tag;\n        } else {\n            delete value[symToStringTag];\n        }\n    }\n    return result;\n}\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */ var getSymbols = !nativeGetSymbols ? stubArray : function(object) {\n    if (object == null) {\n        return [];\n    }\n    object = Object(object);\n    return arrayFilter(nativeGetSymbols(object), function(symbol) {\n        return propertyIsEnumerable.call(object, symbol);\n    });\n};\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */ var getTag = baseGetTag;\n// Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.\nif (DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag || Map && getTag(new Map) != mapTag || Promise && getTag(Promise.resolve()) != promiseTag || Set && getTag(new Set) != setTag || WeakMap && getTag(new WeakMap) != weakMapTag) {\n    getTag = function(value) {\n        var result = baseGetTag(value), Ctor = result == objectTag ? value.constructor : undefined, ctorString = Ctor ? toSource(Ctor) : \"\";\n        if (ctorString) {\n            switch(ctorString){\n                case dataViewCtorString:\n                    return dataViewTag;\n                case mapCtorString:\n                    return mapTag;\n                case promiseCtorString:\n                    return promiseTag;\n                case setCtorString:\n                    return setTag;\n                case weakMapCtorString:\n                    return weakMapTag;\n            }\n        }\n        return result;\n    };\n}\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */ function isIndex(value, length) {\n    length = length == null ? MAX_SAFE_INTEGER : length;\n    return !!length && (typeof value == \"number\" || reIsUint.test(value)) && value > -1 && value % 1 == 0 && value < length;\n}\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */ function isKeyable(value) {\n    var type = typeof value;\n    return type == \"string\" || type == \"number\" || type == \"symbol\" || type == \"boolean\" ? value !== \"__proto__\" : value === null;\n}\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */ function isMasked(func) {\n    return !!maskSrcKey && maskSrcKey in func;\n}\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */ function isPrototype(value) {\n    var Ctor = value && value.constructor, proto = typeof Ctor == \"function\" && Ctor.prototype || objectProto;\n    return value === proto;\n}\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */ function objectToString(value) {\n    return nativeObjectToString.call(value);\n}\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */ function toSource(func) {\n    if (func != null) {\n        try {\n            return funcToString.call(func);\n        } catch (e) {}\n        try {\n            return func + \"\";\n        } catch (e) {}\n    }\n    return \"\";\n}\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */ function eq(value, other) {\n    return value === other || value !== value && other !== other;\n}\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */ var isArguments = baseIsArguments(function() {\n    return arguments;\n}()) ? baseIsArguments : function(value) {\n    return isObjectLike(value) && hasOwnProperty.call(value, \"callee\") && !propertyIsEnumerable.call(value, \"callee\");\n};\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */ var isArray = Array.isArray;\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */ function isArrayLike(value) {\n    return value != null && isLength(value.length) && !isFunction(value);\n}\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */ var isBuffer = nativeIsBuffer || stubFalse;\n/**\n * Performs a deep comparison between two values to determine if they are\n * equivalent.\n *\n * **Note:** This method supports comparing arrays, array buffers, booleans,\n * date objects, error objects, maps, numbers, `Object` objects, regexes,\n * sets, strings, symbols, and typed arrays. `Object` objects are compared\n * by their own, not inherited, enumerable properties. Functions and DOM\n * nodes are compared by strict equality, i.e. `===`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.isEqual(object, other);\n * // => true\n *\n * object === other;\n * // => false\n */ function isEqual(value, other) {\n    return baseIsEqual(value, other);\n}\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */ function isFunction(value) {\n    if (!isObject(value)) {\n        return false;\n    }\n    // The use of `Object#toString` avoids issues with the `typeof` operator\n    // in Safari 9 which returns 'object' for typed arrays and other constructors.\n    var tag = baseGetTag(value);\n    return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */ function isLength(value) {\n    return typeof value == \"number\" && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */ function isObject(value) {\n    var type = typeof value;\n    return value != null && (type == \"object\" || type == \"function\");\n}\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */ function isObjectLike(value) {\n    return value != null && typeof value == \"object\";\n}\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */ var isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */ function keys(object) {\n    return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n/**\n * This method returns a new empty array.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {Array} Returns the new empty array.\n * @example\n *\n * var arrays = _.times(2, _.stubArray);\n *\n * console.log(arrays);\n * // => [[], []]\n *\n * console.log(arrays[0] === arrays[1]);\n * // => false\n */ function stubArray() {\n    return [];\n}\n/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */ function stubFalse() {\n    return false;\n}\nmodule.exports = isEqual;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lodash.isequal/index.js\n");

/***/ })

};
;