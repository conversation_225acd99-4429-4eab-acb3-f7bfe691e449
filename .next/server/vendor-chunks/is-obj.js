"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-obj";
exports.ids = ["vendor-chunks/is-obj"];
exports.modules = {

/***/ "(rsc)/./node_modules/is-obj/index.js":
/*!**************************************!*\
  !*** ./node_modules/is-obj/index.js ***!
  \**************************************/
/***/ ((module) => {

eval("\nmodule.exports = (value)=>{\n    const type = typeof value;\n    return value !== null && (type === \"object\" || type === \"function\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaXMtb2JqL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUFBLE9BQU9DLE9BQU8sR0FBR0MsQ0FBQUE7SUFDaEIsTUFBTUMsT0FBTyxPQUFPRDtJQUNwQixPQUFPQSxVQUFVLFFBQVNDLENBQUFBLFNBQVMsWUFBWUEsU0FBUyxVQUFTO0FBQ2xFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNjcmFwZXIvLi9ub2RlX21vZHVsZXMvaXMtb2JqL2luZGV4LmpzPzRiNWYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHZhbHVlID0+IHtcblx0Y29uc3QgdHlwZSA9IHR5cGVvZiB2YWx1ZTtcblx0cmV0dXJuIHZhbHVlICE9PSBudWxsICYmICh0eXBlID09PSAnb2JqZWN0JyB8fCB0eXBlID09PSAnZnVuY3Rpb24nKTtcbn07XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInZhbHVlIiwidHlwZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/is-obj/index.js\n");

/***/ })

};
;