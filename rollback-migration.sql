-- Rollback Script: Multi-Source to Single-Source
-- Use this script if you need to rollback the migration
-- WARNING: This will delete all multi-source data and revert to the original schema

-- ============================================================================
-- STEP 1: DROP NEW TABLES AND VIEWS
-- ============================================================================

-- Drop views first
DROP VIEW IF EXISTS public.businesses_with_sources;
DROP VIEW IF EXISTS public.businesses_with_review_stats;

-- Drop functions
DROP FUNCTION IF EXISTS find_potential_duplicates(TEXT, TEXT, DECIMAL, DECIMAL, DECIMAL);

-- Drop new tables (in reverse dependency order)
DROP TABLE IF EXISTS public.user_businesses;
DROP TABLE IF EXISTS public.business_scraper_runs;
DROP TABLE IF EXISTS public.scraper_runs;
DROP TABLE IF EXISTS public.reviews_new;
DROP TABLE IF EXISTS public.business_sources;
DROP TABLE IF EXISTS public.businesses_new;
DROP TABLE IF EXISTS public.sources;

-- ============================================================================
-- STEP 2: VERIFY ORIGINAL TABLES STILL EXIST
-- ============================================================================

DO $$
DECLARE
    business_table_exists BOOLEAN;
    review_table_exists BOOLEAN;
    session_table_exists BOOLEAN;
BEGIN
    -- Check if original tables exist
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'businesses'
    ) INTO business_table_exists;
    
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'business_reviews'
    ) INTO review_table_exists;
    
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'scrape_sessions'
    ) INTO session_table_exists;
    
    IF business_table_exists AND review_table_exists AND session_table_exists THEN
        RAISE NOTICE 'SUCCESS: Original tables are intact. Rollback complete.';
    ELSE
        RAISE WARNING 'WARNING: Some original tables are missing. Manual recovery may be needed.';
        RAISE NOTICE 'businesses table exists: %', business_table_exists;
        RAISE NOTICE 'business_reviews table exists: %', review_table_exists;
        RAISE NOTICE 'scrape_sessions table exists: %', session_table_exists;
    END IF;
END $$;

-- ============================================================================
-- ROLLBACK COMPLETE
-- ============================================================================

RAISE NOTICE '=================================================================';
RAISE NOTICE 'ROLLBACK COMPLETE!';
RAISE NOTICE 'Your database has been reverted to the original single-source schema.';
RAISE NOTICE 'Make sure to revert your application code changes as well.';
RAISE NOTICE '=================================================================';
