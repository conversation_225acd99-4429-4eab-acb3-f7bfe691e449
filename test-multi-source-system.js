/**
 * Test Script for Multi-Source Business & Review Tracking System
 * 
 * This script tests the new multi-source database service and API endpoints
 * Run with: node test-multi-source-system.js
 */

const { MultiSourceDatabaseService } = require('./src/services/database/multi-source-database-service')

async function testDatabaseService() {
  console.log('🧪 Testing Multi-Source Database Service...\n')
  
  try {
    const dbService = new MultiSourceDatabaseService()
    
    // Test 1: Check sources
    console.log('1️⃣ Testing source retrieval...')
    const googleSource = await dbService.getSourceByName('google')
    console.log('✅ Google source:', googleSource ? 'Found' : 'Not found')
    
    if (!googleSource) {
      console.log('❌ Google source not found. Please run the schema setup first.')
      return
    }
    
    // Test 2: Test business deduplication
    console.log('\n2️⃣ Testing business deduplication...')
    const duplicates = await dbService.findPotentialDuplicates(
      'Test Restaurant',
      '123 Main Street, New York, NY',
      40.7128,
      -74.0060
    )
    console.log('✅ Deduplication function works. Found', duplicates.length, 'potential matches')
    
    // Test 3: Test scraper run creation
    console.log('\n3️⃣ Testing scraper run creation...')
    const scraperRunId = await dbService.createScraperRun({
      sourceId: googleSource.id,
      actorName: 'test-actor',
      parameters: { test: true },
      metadata: { testRun: true }
    })
    console.log('✅ Scraper run created:', scraperRunId)
    
    // Test 4: Test scraper run update
    console.log('\n4️⃣ Testing scraper run update...')
    await dbService.updateScraperRun(scraperRunId, {
      status: 'completed',
      completedAt: new Date().toISOString(),
      durationSeconds: 30
    })
    console.log('✅ Scraper run updated successfully')
    
    // Test 5: Test business creation
    console.log('\n5️⃣ Testing business creation...')
    const businessResult = await dbService.upsertBusiness({
      name: 'Test Restaurant',
      address: '123 Main Street, New York, NY',
      phone: '******-0123',
      website: 'https://testrestaurant.com',
      category: 'Restaurant',
      rating: 4.5,
      totalReviews: 100,
      latitude: 40.7128,
      longitude: -74.0060,
      metadata: { test: true }
    }, scraperRunId)
    console.log('✅ Business created:', businessResult.business.id)
    
    // Test 6: Test business source mapping
    console.log('\n6️⃣ Testing business source mapping...')
    const businessSource = await dbService.upsertBusinessSource(
      businessResult.business.id,
      googleSource.id,
      'test-google-place-id-123',
      'https://maps.google.com/test',
      { verified: true, plusCode: 'TEST123' }
    )
    console.log('✅ Business source mapping created:', businessSource.id)
    
    // Test 7: Test review saving
    console.log('\n7️⃣ Testing review saving...')
    const reviewsSaved = await dbService.saveReviews(
      businessResult.business.id,
      googleSource.id,
      [
        {
          sourceReviewId: 'test-review-1',
          authorName: 'John Doe',
          authorId: 'john-doe-123',
          rating: 5,
          reviewText: 'Great restaurant!',
          language: 'en',
          likes: 10,
          publishedAt: new Date().toISOString(),
          metadata: { test: true }
        },
        {
          sourceReviewId: 'test-review-2',
          authorName: 'Jane Smith',
          authorId: 'jane-smith-456',
          rating: 4,
          reviewText: 'Good food, nice atmosphere.',
          language: 'en',
          likes: 5,
          publishedAt: new Date().toISOString(),
          metadata: { test: true }
        }
      ]
    )
    console.log('✅ Reviews saved:', reviewsSaved)
    
    // Test 8: Test data retrieval
    console.log('\n8️⃣ Testing data retrieval...')
    const businesses = await dbService.getBusinessesWithSourcesAndReviews(5)
    console.log('✅ Retrieved businesses with sources and reviews:', businesses.length)
    
    // Test 9: Test statistics
    console.log('\n9️⃣ Testing statistics...')
    const stats = await dbService.getStats()
    console.log('✅ Database statistics:')
    console.log('   - Total sources:', stats.totalSources)
    console.log('   - Total businesses:', stats.totalBusinesses)
    console.log('   - Total reviews:', stats.totalReviews)
    console.log('   - Total scraper runs:', stats.totalScraperRuns)
    
    console.log('\n🎉 All database tests passed!')
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message)
    console.error('Stack trace:', error.stack)
  }
}

async function testAPIEndpoints() {
  console.log('\n🌐 Testing API Endpoints...\n')
  
  try {
    const baseUrl = 'http://localhost:3000'
    
    // Test 1: GET endpoint (recent businesses)
    console.log('1️⃣ Testing GET /api/fetch-business...')
    const getResponse = await fetch(`${baseUrl}/api/fetch-business?limit=5`)
    const getData = await getResponse.json()
    
    if (getData.success) {
      console.log('✅ GET endpoint works. Retrieved', getData.count, 'businesses')
    } else {
      console.log('❌ GET endpoint failed:', getData.error)
    }
    
    // Test 2: POST endpoint (scraping) - commented out to avoid actual scraping
    console.log('\n2️⃣ POST endpoint test skipped (would trigger actual scraping)')
    console.log('   To test manually, send POST to /api/fetch-business with:')
    console.log('   {')
    console.log('     "urls": ["https://www.google.com/maps/place/..."],')
    console.log('     "maxReviews": 5')
    console.log('   }')
    
    console.log('\n🎉 API tests completed!')
    
  } catch (error) {
    console.error('❌ API test failed:', error.message)
    console.log('💡 Make sure the development server is running (npm run dev)')
  }
}

async function runAllTests() {
  console.log('🚀 Starting Multi-Source System Tests\n')
  console.log('=' .repeat(50))
  
  await testDatabaseService()
  
  console.log('\n' + '=' .repeat(50))
  
  await testAPIEndpoints()
  
  console.log('\n' + '=' .repeat(50))
  console.log('🏁 Test suite completed!')
  console.log('\nNext steps:')
  console.log('1. If all tests passed, the system is ready to use')
  console.log('2. Try scraping a real Google Maps URL through the UI')
  console.log('3. Check the database for proper data storage')
  console.log('4. Monitor for any deduplication behavior')
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = {
  testDatabaseService,
  testAPIEndpoints,
  runAllTests
}
