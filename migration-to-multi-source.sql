-- Migration Script: Single-Source to Multi-Source Business & Review Tracking
-- This script migrates the existing Google Maps-only schema to support multiple sources
-- Run this script in your Supabase SQL Editor

-- Enable UUID extension (if not already enabled)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- STEP 1: CREATE NEW TABLES FOR MULTI-SOURCE SUPPORT
-- ============================================================================

-- 1. Sources table - defines available data sources
CREATE TABLE public.sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    name TEXT UNIQUE NOT NULL, -- 'google', 'yelp', 'tripadvisor', etc.
    display_name TEXT NOT NULL, -- 'Google Maps', 'Yelp', 'TripAdvisor', etc.
    base_url TEXT, -- Base URL for the source
    metadata JSONB DEFAULT '{}'
);

-- 2. New businesses table (replaces old businesses table)
CREATE TABLE public.businesses_new (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    phone TEXT,
    website TEXT,
    category TEXT,
    rating DECIMAL(3,2) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
    total_reviews INTEGER DEFAULT 0,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    version INTEGER DEFAULT 1, -- Version counter for business data
    metadata JSONB DEFAULT '{}'
);

-- 3. Business sources mapping table
CREATE TABLE public.business_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    business_id UUID NOT NULL REFERENCES public.businesses_new(id) ON DELETE CASCADE,
    source_id UUID NOT NULL REFERENCES public.sources(id) ON DELETE CASCADE,
    source_business_id TEXT NOT NULL, -- Source-specific business ID (e.g., Google Place ID)
    source_url TEXT, -- Source-specific URL
    source_metadata JSONB DEFAULT '{}', -- Source-specific metadata
    last_scraped_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(source_id, source_business_id) -- Prevent duplicate source entries
);

-- 4. New reviews table (replaces old business_reviews table)
CREATE TABLE public.reviews_new (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    business_id UUID NOT NULL REFERENCES public.businesses_new(id) ON DELETE CASCADE,
    source_id UUID NOT NULL REFERENCES public.sources(id) ON DELETE CASCADE,
    source_review_id TEXT NOT NULL, -- Source-specific review ID
    author_name TEXT NOT NULL,
    author_id TEXT,
    author_avatar TEXT,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    language TEXT,
    likes INTEGER DEFAULT 0,
    published_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_review_at TIMESTAMP WITH TIME ZONE,
    reply_text TEXT,
    reply_published_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    UNIQUE(source_id, source_review_id, business_id) -- Prevent duplicate reviews
);

-- 5. Scraper runs table (replaces scrape_sessions)
CREATE TABLE public.scraper_runs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source_id UUID NOT NULL REFERENCES public.sources(id) ON DELETE CASCADE,
    actor_name TEXT NOT NULL, -- Apify actor name
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    apify_run_id TEXT,
    apify_dataset_id TEXT,
    storage_path TEXT, -- Path to stored data
    duration_seconds INTEGER,
    cost DECIMAL(10,4), -- Cost in USD
    parameters JSONB DEFAULT '{}', -- Scraper parameters
    metadata JSONB DEFAULT '{}'
);

-- 6. Business scraper runs junction table (tracks which runs updated which businesses)
CREATE TABLE public.business_scraper_runs (
    business_id UUID NOT NULL REFERENCES public.businesses_new(id) ON DELETE CASCADE,
    scraper_run_id UUID NOT NULL REFERENCES public.scraper_runs(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    version_before INTEGER NOT NULL,
    version_after INTEGER NOT NULL,
    changes_made JSONB DEFAULT '{}', -- What changed in this update
    PRIMARY KEY (business_id, scraper_run_id)
);

-- 7. User businesses table (for future user linking)
CREATE TABLE public.user_businesses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID NOT NULL, -- Will reference users table when created
    business_id UUID NOT NULL REFERENCES public.businesses_new(id) ON DELETE CASCADE,
    relationship_type TEXT DEFAULT 'owner' CHECK (relationship_type IN ('owner', 'manager', 'employee', 'follower')),
    metadata JSONB DEFAULT '{}'
);

-- ============================================================================
-- STEP 2: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Sources indexes
CREATE INDEX idx_sources_name ON public.sources(name);

-- Businesses indexes
CREATE INDEX idx_businesses_new_name ON public.businesses_new(name);
CREATE INDEX idx_businesses_new_category ON public.businesses_new(category);
CREATE INDEX idx_businesses_new_rating ON public.businesses_new(rating DESC);
CREATE INDEX idx_businesses_new_location ON public.businesses_new(latitude, longitude);
CREATE INDEX idx_businesses_new_version ON public.businesses_new(version);
CREATE INDEX idx_businesses_new_created_at ON public.businesses_new(created_at DESC);

-- Business sources indexes
CREATE INDEX idx_business_sources_business_id ON public.business_sources(business_id);
CREATE INDEX idx_business_sources_source_id ON public.business_sources(source_id);
CREATE INDEX idx_business_sources_source_business_id ON public.business_sources(source_business_id);
CREATE INDEX idx_business_sources_last_scraped ON public.business_sources(last_scraped_at DESC);

-- Reviews indexes
CREATE INDEX idx_reviews_new_business_id ON public.reviews_new(business_id);
CREATE INDEX idx_reviews_new_source_id ON public.reviews_new(source_id);
CREATE INDEX idx_reviews_new_rating ON public.reviews_new(rating);
CREATE INDEX idx_reviews_new_published_at ON public.reviews_new(published_at DESC);
CREATE INDEX idx_reviews_new_author_id ON public.reviews_new(author_id);

-- Scraper runs indexes
CREATE INDEX idx_scraper_runs_source_id ON public.scraper_runs(source_id);
CREATE INDEX idx_scraper_runs_status ON public.scraper_runs(status);
CREATE INDEX idx_scraper_runs_started_at ON public.scraper_runs(started_at DESC);
CREATE INDEX idx_scraper_runs_actor_name ON public.scraper_runs(actor_name);

-- Business scraper runs indexes
CREATE INDEX idx_business_scraper_runs_business_id ON public.business_scraper_runs(business_id);
CREATE INDEX idx_business_scraper_runs_scraper_run_id ON public.business_scraper_runs(scraper_run_id);
CREATE INDEX idx_business_scraper_runs_created_at ON public.business_scraper_runs(created_at DESC);

-- User businesses indexes
CREATE INDEX idx_user_businesses_user_id ON public.user_businesses(user_id);
CREATE INDEX idx_user_businesses_business_id ON public.user_businesses(business_id);
CREATE INDEX idx_user_businesses_relationship_type ON public.user_businesses(relationship_type);

-- ============================================================================
-- STEP 3: CREATE TRIGGERS FOR AUTO-UPDATING TIMESTAMPS
-- ============================================================================

-- Create or replace the trigger function (reuse existing one)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers to new tables
CREATE TRIGGER update_sources_updated_at 
    BEFORE UPDATE ON public.sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_businesses_new_updated_at 
    BEFORE UPDATE ON public.businesses_new 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_sources_updated_at 
    BEFORE UPDATE ON public.business_sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_new_updated_at 
    BEFORE UPDATE ON public.reviews_new 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scraper_runs_updated_at 
    BEFORE UPDATE ON public.scraper_runs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_businesses_updated_at
    BEFORE UPDATE ON public.user_businesses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- STEP 4: INSERT DEFAULT SOURCES
-- ============================================================================

-- Insert Google Maps as the first source
INSERT INTO public.sources (name, display_name, base_url, metadata) VALUES
('google', 'Google Maps', 'https://maps.google.com', '{"actor": "compass/crawler-google-places", "free_tier": true}'),
('yelp', 'Yelp', 'https://www.yelp.com', '{"actor": "TBD", "free_tier": false}'),
('tripadvisor', 'TripAdvisor', 'https://www.tripadvisor.com', '{"actor": "TBD", "free_tier": false}'),
('foursquare', 'Foursquare', 'https://foursquare.com', '{"actor": "TBD", "free_tier": false}');

-- ============================================================================
-- STEP 5: MIGRATE EXISTING DATA
-- ============================================================================

-- Get Google source ID for migration
DO $$
DECLARE
    google_source_id UUID;
BEGIN
    SELECT id INTO google_source_id FROM public.sources WHERE name = 'google';

    -- Migrate existing businesses from old table to new table
    INSERT INTO public.businesses_new (
        id, created_at, updated_at, name, address, phone, website, category,
        rating, total_reviews, latitude, longitude, version, metadata
    )
    SELECT
        id, created_at, updated_at, name, address, phone, website, category,
        rating, total_reviews, latitude, longitude,
        1 as version, -- Start with version 1
        COALESCE(metadata, '{}')
    FROM public.businesses;

    -- Create business_sources entries for migrated businesses
    INSERT INTO public.business_sources (
        business_id, source_id, source_business_id, source_url,
        source_metadata, last_scraped_at
    )
    SELECT
        b.id as business_id,
        google_source_id as source_id,
        b.google_place_id as source_business_id,
        NULL as source_url, -- We don't have the original URL
        jsonb_build_object(
            'verified', COALESCE(b.verified, false),
            'plus_code', b.plus_code,
            'opening_hours', COALESCE(b.opening_hours, '{}'),
            'photos', COALESCE(b.photos, '[]'),
            'price_level', b.price_level
        ) as source_metadata,
        b.extracted_at as last_scraped_at
    FROM public.businesses b;

    -- Migrate existing reviews from old table to new table
    INSERT INTO public.reviews_new (
        id, created_at, updated_at, business_id, source_id, source_review_id,
        author_name, author_id, author_avatar, rating, review_text, language,
        likes, published_at, updated_review_at, reply_text, reply_published_at,
        metadata
    )
    SELECT
        br.id, br.created_at, br.updated_at, br.business_id, google_source_id,
        br.google_review_id as source_review_id,
        br.author_name, br.author_id, br.author_avatar, br.rating, br.review_text, br.language,
        br.likes, br.published_at, br.updated_review_at, br.reply_text, br.reply_published_at,
        COALESCE(br.metadata, '{}')
    FROM public.business_reviews br;

    -- Migrate scrape sessions to scraper runs
    INSERT INTO public.scraper_runs (
        id, created_at, updated_at, source_id, actor_name, status,
        started_at, completed_at, apify_run_id, apify_dataset_id,
        parameters, metadata
    )
    SELECT
        ss.id, ss.created_at, ss.updated_at, google_source_id,
        'compass/crawler-google-places' as actor_name,
        ss.status, ss.started_at, ss.completed_at, ss.apify_run_id, ss.apify_dataset_id,
        COALESCE(ss.parameters, '{}'),
        jsonb_build_object(
            'session_type', ss.session_type,
            'urls', ss.urls,
            'results_count', ss.results_count,
            'error_message', ss.error_message,
            'original_metadata', COALESCE(ss.metadata, '{}')
        )
    FROM public.scrape_sessions ss;

END $$;

-- ============================================================================
-- STEP 6: ENABLE ROW LEVEL SECURITY ON NEW TABLES
-- ============================================================================

-- Enable RLS
ALTER TABLE public.sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.businesses_new ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews_new ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scraper_runs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_scraper_runs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_businesses ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies for service role (full access)
CREATE POLICY "Allow service role all operations on sources"
ON public.sources FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on businesses_new"
ON public.businesses_new FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on business_sources"
ON public.business_sources FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on reviews_new"
ON public.reviews_new FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on scraper_runs"
ON public.scraper_runs FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on business_scraper_runs"
ON public.business_scraper_runs FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on user_businesses"
ON public.user_businesses FOR ALL TO service_role USING (true) WITH CHECK (true);

-- Create RLS Policies for anon role (read access)
CREATE POLICY "Allow anon read access to sources"
ON public.sources FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to businesses_new"
ON public.businesses_new FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to business_sources"
ON public.business_sources FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to reviews_new"
ON public.reviews_new FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to scraper_runs"
ON public.scraper_runs FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to business_scraper_runs"
ON public.business_scraper_runs FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to user_businesses"
ON public.user_businesses FOR SELECT TO anon USING (true);

-- ============================================================================
-- STEP 7: CREATE HELPFUL VIEWS FOR COMMON QUERIES
-- ============================================================================

-- View to get businesses with all their source information
CREATE VIEW public.businesses_with_sources AS
SELECT
    b.*,
    json_agg(
        json_build_object(
            'source_name', s.name,
            'source_display_name', s.display_name,
            'source_business_id', bs.source_business_id,
            'source_url', bs.source_url,
            'last_scraped_at', bs.last_scraped_at,
            'source_metadata', bs.source_metadata
        )
    ) as sources
FROM public.businesses_new b
LEFT JOIN public.business_sources bs ON b.id = bs.business_id
LEFT JOIN public.sources s ON bs.source_id = s.id
GROUP BY b.id, b.created_at, b.updated_at, b.name, b.address, b.phone,
         b.website, b.category, b.rating, b.total_reviews, b.latitude,
         b.longitude, b.version, b.metadata;

-- View to get businesses with review counts by source
CREATE VIEW public.businesses_with_review_stats AS
SELECT
    b.*,
    COALESCE(review_stats.total_reviews_scraped, 0) as total_reviews_scraped,
    COALESCE(review_stats.avg_rating_scraped, 0) as avg_rating_scraped,
    review_stats.reviews_by_source
FROM public.businesses_new b
LEFT JOIN (
    SELECT
        r.business_id,
        COUNT(*) as total_reviews_scraped,
        AVG(r.rating::decimal) as avg_rating_scraped,
        json_agg(
            json_build_object(
                'source_name', s.name,
                'review_count', source_counts.review_count,
                'avg_rating', source_counts.avg_rating
            )
        ) as reviews_by_source
    FROM public.reviews_new r
    JOIN public.sources s ON r.source_id = s.id
    JOIN (
        SELECT
            business_id,
            source_id,
            COUNT(*) as review_count,
            AVG(rating::decimal) as avg_rating
        FROM public.reviews_new
        GROUP BY business_id, source_id
    ) source_counts ON r.business_id = source_counts.business_id AND r.source_id = source_counts.source_id
    GROUP BY r.business_id
) review_stats ON b.id = review_stats.business_id;

-- ============================================================================
-- STEP 8: CREATE FUNCTIONS FOR BUSINESS DEDUPLICATION
-- ============================================================================

-- Function to find potential duplicate businesses based on name and location
CREATE OR REPLACE FUNCTION find_potential_duplicates(
    business_name TEXT,
    business_address TEXT,
    latitude_param DECIMAL DEFAULT NULL,
    longitude_param DECIMAL DEFAULT NULL,
    distance_threshold_km DECIMAL DEFAULT 0.1
)
RETURNS TABLE(
    business_id UUID,
    name TEXT,
    address TEXT,
    similarity_score DECIMAL,
    distance_km DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        b.id as business_id,
        b.name,
        b.address,
        -- Simple similarity score based on name and address similarity
        (
            CASE
                WHEN LOWER(b.name) = LOWER(business_name) THEN 1.0
                WHEN LOWER(b.name) LIKE '%' || LOWER(business_name) || '%' OR LOWER(business_name) LIKE '%' || LOWER(b.name) || '%' THEN 0.8
                ELSE 0.0
            END +
            CASE
                WHEN LOWER(b.address) = LOWER(business_address) THEN 1.0
                WHEN LOWER(b.address) LIKE '%' || LOWER(business_address) || '%' OR LOWER(business_address) LIKE '%' || LOWER(b.address) || '%' THEN 0.6
                ELSE 0.0
            END
        ) / 2.0 as similarity_score,
        -- Calculate distance if coordinates are provided
        CASE
            WHEN latitude_param IS NOT NULL AND longitude_param IS NOT NULL AND b.latitude IS NOT NULL AND b.longitude IS NOT NULL THEN
                -- Haversine formula approximation for small distances
                6371 * acos(
                    cos(radians(latitude_param)) * cos(radians(b.latitude)) *
                    cos(radians(b.longitude) - radians(longitude_param)) +
                    sin(radians(latitude_param)) * sin(radians(b.latitude))
                )
            ELSE NULL
        END as distance_km
    FROM public.businesses_new b
    WHERE
        -- Name similarity filter
        (LOWER(b.name) LIKE '%' || LOWER(business_name) || '%' OR LOWER(business_name) LIKE '%' || LOWER(b.name) || '%')
        AND
        -- Distance filter (if coordinates provided)
        (
            latitude_param IS NULL OR longitude_param IS NULL OR b.latitude IS NULL OR b.longitude IS NULL
            OR
            6371 * acos(
                cos(radians(latitude_param)) * cos(radians(b.latitude)) *
                cos(radians(b.longitude) - radians(longitude_param)) +
                sin(radians(latitude_param)) * sin(radians(b.latitude))
            ) <= distance_threshold_km
        )
    ORDER BY similarity_score DESC, distance_km ASC NULLS LAST;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- STEP 9: VERIFICATION QUERIES
-- ============================================================================

-- Verify migration results
DO $$
DECLARE
    old_business_count INTEGER;
    new_business_count INTEGER;
    old_review_count INTEGER;
    new_review_count INTEGER;
    old_session_count INTEGER;
    new_run_count INTEGER;
BEGIN
    -- Count records in old vs new tables
    SELECT COUNT(*) INTO old_business_count FROM public.businesses;
    SELECT COUNT(*) INTO new_business_count FROM public.businesses_new;
    SELECT COUNT(*) INTO old_review_count FROM public.business_reviews;
    SELECT COUNT(*) INTO new_review_count FROM public.reviews_new;
    SELECT COUNT(*) INTO old_session_count FROM public.scrape_sessions;
    SELECT COUNT(*) INTO new_run_count FROM public.scraper_runs;

    -- Output verification results
    RAISE NOTICE 'MIGRATION VERIFICATION:';
    RAISE NOTICE 'Businesses: % old -> % new', old_business_count, new_business_count;
    RAISE NOTICE 'Reviews: % old -> % new', old_review_count, new_review_count;
    RAISE NOTICE 'Sessions/Runs: % old -> % new', old_session_count, new_run_count;

    -- Check if counts match
    IF old_business_count = new_business_count AND old_review_count = new_review_count AND old_session_count = new_run_count THEN
        RAISE NOTICE 'SUCCESS: All data migrated correctly!';
    ELSE
        RAISE WARNING 'WARNING: Data counts do not match. Please review migration.';
    END IF;
END $$;

-- ============================================================================
-- STEP 10: OPTIONAL - RENAME TABLES (UNCOMMENT WHEN READY)
-- ============================================================================

-- IMPORTANT: Only run these commands after verifying the migration is successful
-- and updating your application code to use the new table names

/*
-- Rename old tables to backup
ALTER TABLE public.businesses RENAME TO businesses_old_backup;
ALTER TABLE public.business_reviews RENAME TO business_reviews_old_backup;
ALTER TABLE public.scrape_sessions RENAME TO scrape_sessions_old_backup;

-- Rename new tables to final names
ALTER TABLE public.businesses_new RENAME TO businesses;
ALTER TABLE public.reviews_new RENAME TO reviews;

-- Update view definitions to use new table names
DROP VIEW IF EXISTS public.businesses_with_sources;
DROP VIEW IF EXISTS public.businesses_with_review_stats;

CREATE VIEW public.businesses_with_sources AS
SELECT
    b.*,
    json_agg(
        json_build_object(
            'source_name', s.name,
            'source_display_name', s.display_name,
            'source_business_id', bs.source_business_id,
            'source_url', bs.source_url,
            'last_scraped_at', bs.last_scraped_at,
            'source_metadata', bs.source_metadata
        )
    ) as sources
FROM public.businesses b
LEFT JOIN public.business_sources bs ON b.id = bs.business_id
LEFT JOIN public.sources s ON bs.source_id = s.id
GROUP BY b.id, b.created_at, b.updated_at, b.name, b.address, b.phone,
         b.website, b.category, b.rating, b.total_reviews, b.latitude,
         b.longitude, b.version, b.metadata;

CREATE VIEW public.businesses_with_review_stats AS
SELECT
    b.*,
    COALESCE(review_stats.total_reviews_scraped, 0) as total_reviews_scraped,
    COALESCE(review_stats.avg_rating_scraped, 0) as avg_rating_scraped,
    review_stats.reviews_by_source
FROM public.businesses b
LEFT JOIN (
    SELECT
        r.business_id,
        COUNT(*) as total_reviews_scraped,
        AVG(r.rating::decimal) as avg_rating_scraped,
        json_agg(
            json_build_object(
                'source_name', s.name,
                'review_count', source_counts.review_count,
                'avg_rating', source_counts.avg_rating
            )
        ) as reviews_by_source
    FROM public.reviews r
    JOIN public.sources s ON r.source_id = s.id
    JOIN (
        SELECT
            business_id,
            source_id,
            COUNT(*) as review_count,
            AVG(rating::decimal) as avg_rating
        FROM public.reviews
        GROUP BY business_id, source_id
    ) source_counts ON r.business_id = source_counts.business_id AND r.source_id = source_counts.source_id
    GROUP BY r.business_id
) review_stats ON b.id = review_stats.business_id;
*/

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

RAISE NOTICE '=================================================================';
RAISE NOTICE 'MIGRATION TO MULTI-SOURCE SCHEMA COMPLETE!';
RAISE NOTICE '=================================================================';
RAISE NOTICE 'Next steps:';
RAISE NOTICE '1. Update your TypeScript types to match new schema';
RAISE NOTICE '2. Update DatabaseService to use new tables';
RAISE NOTICE '3. Test the application thoroughly';
RAISE NOTICE '4. When ready, uncomment Step 10 to finalize table names';
RAISE NOTICE '=================================================================';
