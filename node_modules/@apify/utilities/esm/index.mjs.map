{"version": 3, "sources": ["../../src/utilities.ts", "../../src/utilities.client.ts", "../../src/exponential_backoff.ts", "../../src/health_checker.ts", "../../src/parse_jsonl_stream.ts", "../../src/streams_utilities.ts", "../../src/webhook_payload_template.ts", "../../src/crypto.ts", "../../src/url_params_utils.ts", "../../src/code_hash_manager.ts", "../../src/hmac.ts", "../../src/storages.ts"], "sourcesContent": ["/*!\n * This module contains various server utility and helper functions.\n * Note that it automatically exports functions from utilities.client.js\n *\n * Author: <PERSON> (<EMAIL>)\n * Copyright(c) 2015 Apify. All rights reserved.\n *\n */\n\nimport crypto from 'node:crypto';\n\nimport { ANONYMOUS_USERNAME, APIFY_ID_REGEX } from '@apify/consts';\nimport type { Log } from '@apify/log';\nimport log, { LoggerJson, LogLevel } from '@apify/log';\n\n/**\n * Generates a random cryptographically strong string consisting of 17 alphanumeric characters.\n * This string is similar to MongoDB ObjectIds generated by Meteor.\n */\nexport function cryptoRandomObjectId(length = 17): string {\n    const chars = 'abcdefghijklmnopqrstuvwxyzABCEDFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    const bytes = crypto.randomBytes(length);\n    let str = '';\n    // eslint-disable-next-line\n    for (let i = bytes.length - 1; i >= 0; i--) { str += chars[(bytes[i] | 0) % chars.length]; }\n    return str;\n}\n\n/**\n * Generates unique, deterministic record ID from the provided key with given length (defaults to 17).\n */\nexport function deterministicUniqueId(key: string, length = 17): string {\n    return crypto\n        .createHash('sha256')\n        .update(key)\n        .digest('base64')\n        .replace(/(\\+|\\/|=)/g, 'x')\n        .substr(0, length);\n}\n\n/**\n * Returns a random integer between 0 and max (excluded, unless it is also 0).\n * @param {number} maxExcluded\n * @returns {number}\n */\nexport function getRandomInt(maxExcluded: number) {\n    maxExcluded = Math.floor(maxExcluded);\n    return Math.floor(Math.random() * maxExcluded);\n}\n\n/**\n * If 'date' is a String, this function converts and returns it as a Date object.\n * Otherwise, the function returns the original 'date' argument.\n * This function is useful to convert dates transfered via JSON which doesn't natively support dates.\n */\nexport function parseDateFromJson(date: string | Date) {\n    if (typeof date === 'string') { return new Date(Date.parse(date)); }\n    return date;\n}\n\n/**\n * Returns a Promise object that will wait a specific number of milliseconds.\n * @param {number} millis Time to wait. If the value is not larger than zero, the promise resolves immediately.\n */\nexport async function delayPromise(millis: number): Promise<void> {\n    return new Promise(((resolve) => {\n        if (millis > 0) {\n            setTimeout(() => resolve(), millis);\n        } else {\n            resolve();\n        }\n    }));\n}\n\n/**\n * Removes an element from an array.\n */\nexport function removeFromArray<T>(array: T[], element: T) {\n    const index = array.indexOf(element);\n    if (index >= 0) {\n        array.splice(index, 1);\n        return true;\n    }\n    return false;\n}\n\ninterface RequestLike {\n    url: string;\n}\n\ninterface ResponseLike {\n    status: (code: number) => void;\n    send: (payload: string) => void;\n    headersSent?: boolean;\n}\n\n/**\n * A default route for HTTP 404 error page for API endpoints.\n */\nexport function http404Route(req: RequestLike, res: ResponseLike) {\n    res.status(404);\n    res.send('Page not found');\n}\n\n/**\n * Default error handler of Express API endpoints.\n */\nexport function expressErrorHandler(err: Error, req: RequestLike, res: ResponseLike, next: (...a: unknown[]) => unknown) {\n    log.warning('Client HTTP request failed', { url: req.url, errMsg: err.message });\n    if (res.headersSent) {\n        next(err);\n        return;\n    }\n    res.status(505);\n    res.send('Internal server error');\n}\n\nexport type BetterIntervalID = { _betterClearInterval: () => void };\n\n/**\n * Similar to setInterval() but with two important differences:\n * First, it assumes the function is asynchronous and only schedules its next invocation AFTER the asynchronous function finished.\n * Second, it invokes the function immediately.\n * @param func Function to be periodically executed.\n * For backwards compatibility reasons, it is passed a callback as its first argument during invocation, however that callback has no effect.\n * @param delay The number of milliseconds to wait to next invocation of the function after the current invocation finishes.\n * @returns Object that can be passed to betterClearInterval()\n */\nexport function betterSetInterval(func: ((a: (...args: unknown[]) => unknown) => void) | ((...args: unknown[]) => unknown), delay: number): BetterIntervalID {\n    let scheduleNextRun: () => void;\n    let timeoutId: NodeJS.Timeout;\n    let isRunning = true;\n\n    const funcWrapper = function () {\n        // Historically, the function was passed a callback that it needed to call to signal it was done.\n        // We keep passing this callback for backwards compatibility, but it has no effect anymore.\n        void new Promise((resolve) => {\n            resolve(func(() => undefined));\n        }).finally(scheduleNextRun);\n    };\n    scheduleNextRun = function () {\n        if (isRunning) timeoutId = setTimeout(funcWrapper, delay);\n    };\n    funcWrapper();\n\n    return {\n        _betterClearInterval() {\n            isRunning = false;\n            clearTimeout(timeoutId);\n        },\n    };\n}\n\nexport function betterClearInterval(intervalID: BetterIntervalID) {\n    // eslint-disable-next-line no-underscore-dangle\n    if (intervalID && intervalID._betterClearInterval) {\n        try {\n            // eslint-disable-next-line no-underscore-dangle\n            intervalID._betterClearInterval();\n        } catch (e) {\n            log.exception(e as Error, '_betterClearInterval() threw an exception!?');\n        }\n    }\n}\n\n/**\n * Escapes a string so that it can be used in regular expression (e.g. converts \"myfile.*\" to \"myfile\\\\.\\\\*\").\n */\nexport function escapeRegExp(str: string): string {\n    // code taken from https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions\n    return String(str).replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\n/**\n * String left pad\n */\nexport function leftpad(str: string, len: number, ch: string | number = ' ') {\n    // code inspired by https://www.theregister.co.uk/2016/03/23/npm_left_pad_chaos/\n    str = String(str);\n    let i = -1;\n\n    if (!ch && ch !== 0) ch = ' ';\n\n    len -= str.length;\n\n    while (++i < len) {\n        str = ch + str;\n    }\n\n    return str;\n}\n\n/**\n * Computes weighted average of 2 values.\n */\nexport function weightedAverage(val1: number, weight1: number, val2: number, weight2: number) {\n    return (val1 * weight1 + val2 * weight2) / (weight1 + weight2);\n}\n\n/**\n * List of forbidden usernames. Note that usernames can be used as apify.com/username,\n * so we need to prohibit any username that might be part of our website or confusing in anyway.\n */\nconst FORBIDDEN_USERNAMES_REGEXPS = [\n    // App routes\n    'page-not-found', 'docs', 'terms-of-use', 'about', 'pricing', 'privacy-policy', 'customers',\n    'request-form', 'request-solution', 'release-notes', 'jobs', 'api-reference', 'video-tutorials',\n    'acts', 'key-value-stores', 'schedules', 'account', 'sign-up', 'sign-in-discourse', 'admin',\n    'documentation', 'change-password', 'enroll-account', 'forgot-password', 'reset-password',\n    'sign-in', 'verify-email', 'live-status', 'browser-info', 'webhooks', 'health-check', 'api',\n    'change-log', 'dashboard', 'community', 'crawlers', 'ext',\n\n    // Various strings\n    'admin', 'administration', 'crawler', 'act', 'library', 'lib', 'apifier', 'team',\n    'contact', 'doc', 'documentation', 'for-business', 'for-developers', 'developers', 'business',\n    'integrations', 'job', 'setting', 'settings', 'privacy', 'policy', 'assets', 'help',\n    'config', 'configuration', 'terms', 'hiring', 'hire', 'status', 'status-page', 'solutions',\n    'support', 'market', 'marketplace', 'download', 'downloads', 'username', 'users', 'user',\n    'login', 'logout', 'signin', 'sign', 'signup', 'sign-out', 'signout', 'plugins', 'plug-ins',\n    'reset', 'password', 'passwords', 'square', 'profile-photos', 'profiles', 'true', 'false',\n    'js', 'css', 'img', 'images', 'image', 'partials', 'fonts', 'font', 'dynamic_templates',\n    'app', 'schedules', 'community', 'storage', 'storages', 'account', 'node_modules', 'bower_components',\n    'video', 'knowledgebase', 'forum', 'customers', 'blog', 'health-check', 'health', 'anim',\n    'forum_topics.json', 'forum_categories.json', 'me', 'you', 'him', 'she', 'it', 'external',\n    'actor', 'crawler', 'scheduler', 'api', 'sdk', 'puppeteer', 'webdriver',\n    'selenium', '(selenium.*webdriver)', 'undefined', 'page-analyzer', 'wp-login.php',\n    'welcome.action', 'echo', 'proxy', 'super-proxy', 'gdpr', 'case-studies', 'use-cases', 'how-to',\n    'kb', 'cookies', 'cookie-policy', 'cookies-policy', 'powered-by', 'run', 'runs', 'actor', 'actors',\n    'act', 'acts', 'success-stories', 'roadmap', 'join-marketplace', 'presskit', 'press-kit', 'covid-19',\n    'covid', 'covid19', 'matfyz', 'ideas', 'public-actors', 'resources', 'partners', 'affiliate',\n    'industries', 'web-scraping', 'custom-solutions', 'solution-provider', 'alternatives', 'platform',\n    'freelancers', 'freelancer', 'partner', 'preview', 'templates', 'data-for-generative-ai',\n    'discord', 'praguecrawl', 'prague-crawl', 'bob', 'ai-agents', 'reel', 'video-reel',\n    'mcp', 'model-context-protocol', 'modelcontextprotocol', 'apify.com', 'design-kit', 'press-kit',\n    'scrapers', 'professional-services',\n\n    // Special files\n    'index', 'index\\\\.html', '(favicon\\\\.[a-z]+)', 'BingSiteAuth.xml', '(google.+\\\\.html)', 'robots\\\\.txt',\n    '(sitemap\\\\.[a-z]+)', '(apple-touch-icon.*)', 'security-whitepaper\\\\.pdf', 'security\\\\.txt',\n\n    // All hidden files\n    '(\\\\..*)',\n\n    // File starting with xxx-\n    '(xxx-.*)',\n\n    // Strings not starting with letter or number\n    '([^0-9a-z].*)',\n\n    // Strings not ending with letter or number\n    '(.*[^0-9a-z])',\n\n    // Strings where there's more than one underscore, comma or dash in row\n    '(.*[_.\\\\-]{2}.*)',\n\n    // Reserved usernames from https://github.com/shouldbee/reserved-usernames/blob/master/reserved-usernames.json\n    '0', 'about', 'access', 'account', 'accounts', 'activate', 'activities', 'activity', 'ad', 'add',\n    'address', 'adm', 'admin', 'administration', 'administrator', 'ads', 'adult', 'advertising',\n    'affiliate', 'affiliates', 'ajax', 'all', 'alpha', 'analysis', 'analytics', 'android', 'anon',\n    'anonymous', 'api', 'app', 'apps', 'archive', 'archives', 'article', 'asct', 'asset', 'atom',\n    'auth', 'authentication', 'avatar', 'backup', 'balancer-manager', 'banner', 'banners', 'beta',\n    'billing', 'bin', 'blog', 'blogs', 'board', 'book', 'bookmark', 'bot', 'bots', 'bug', 'business',\n    'cache', 'cadastro', 'calendar', 'call', 'campaign', 'cancel', 'captcha', 'career', 'careers',\n    'cart', 'categories', 'category', 'cgi', 'cgi-bin', 'changelog', 'chat', 'check', 'checking',\n    'checkout', 'client', 'cliente', 'clients', 'code', 'codereview', 'comercial', 'comment',\n    'comments', 'communities', 'community', 'company', 'compare', 'compras', 'config', 'configuration',\n    'connect', 'contact', 'contact-us', 'contact_us', 'contactus', 'contest', 'contribute', 'corp',\n    'create', 'css', 'dashboard', 'data', 'db', 'default', 'delete', 'demo', 'design', 'designer',\n    'destroy', 'dev', 'devel', 'developer', 'developers', 'diagram', 'diary', 'dict', 'dictionary',\n    'die', 'dir', 'direct_messages', 'directory', 'dist', 'doc', 'docs', 'documentation', 'domain',\n    'download', 'downloads', 'ecommerce', 'edit', 'editor', 'edu', 'education', 'email', 'employment',\n    'empty', 'end', 'enterprise', 'entries', 'entry', 'error', 'errors', 'eval', 'event', 'exit',\n    'explore', 'facebook', 'faq', 'favorite', 'favorites', 'feature', 'features', 'feed', 'feedback',\n    'feeds', 'file', 'files', 'first', 'flash', 'fleet', 'fleets', 'flog', 'follow', 'followers',\n    'following', 'forgot', 'form', 'forum', 'forums', 'founder', 'free', 'friend', 'friends', 'ftp',\n    'gadget', 'gadgets', 'game', 'games', 'get', 'gift', 'gifts', 'gist', 'github', 'graph', 'group',\n    'groups', 'guest', 'guests', 'help', 'home', 'homepage', 'host', 'hosting', 'hostmaster',\n    'hostname', 'howto', 'hpg', 'html', 'http', 'httpd', 'https', 'i', 'iamges', 'icon', 'icons',\n    'id', 'idea', 'ideas', 'image', 'images', 'imap', 'img', 'index', 'indice', 'info', 'information',\n    'inquiry', 'instagram', 'intranet', 'invitations', 'invite', 'ipad', 'iphone', 'irc', 'is',\n    'issue', 'issues', 'it', 'item', 'items', 'java', 'javascript', 'job', 'jobs', 'join', 'js',\n    'json', 'jump', 'knowledgebase', 'language', 'languages', 'last', 'ldap-status', 'legal', 'license',\n    'link', 'links', 'linux', 'list', 'lists', 'log', 'log-in', 'log-out', 'log_in', 'log_out',\n    'login', 'logout', 'logs', 'm', 'mac', 'mail', 'mail1', 'mail2', 'mail3', 'mail4', 'mail5',\n    'mailer', 'mailing', 'maintenance', 'manager', 'manual', 'map', 'maps', 'marketing', 'master',\n    'me', 'media', 'member', 'members', 'message', 'messages', 'messenger', 'microblog', 'microblogs',\n    'mine', 'mis', 'mob', 'mobile', 'movie', 'movies', 'mp3', 'msg', 'msn', 'music', 'musicas', 'mx',\n    'my', 'mysql', 'name', 'named', 'nan', 'navi', 'navigation', 'net', 'network', 'new', 'news',\n    'newsletter', 'nick', 'nickname', 'notes', 'noticias', 'notification', 'notifications', 'notify',\n    'ns', 'ns1', 'ns10', 'ns2', 'ns3', 'ns4', 'ns5', 'ns6', 'ns7', 'ns8', 'ns9', 'null', 'oauth',\n    'oauth_clients', 'offer', 'offers', 'official', 'old', 'online', 'openid', 'operator', 'order',\n    'orders', 'organization', 'organizations', 'overview', 'owner', 'owners', 'page', 'pager',\n    'pages', 'panel', 'password', 'payment', 'perl', 'phone', 'photo', 'photoalbum', 'photos', 'php',\n    'phpmyadmin', 'phppgadmin', 'phpredisadmin', 'pic', 'pics', 'ping', 'plan', 'plans', 'plugin',\n    'plugins', 'policy', 'pop', 'pop3', 'popular', 'portal', 'post', 'postfix', 'postmaster', 'posts',\n    'pr', 'premium', 'press', 'price', 'pricing', 'privacy', 'privacy-policy', 'privacy_policy',\n    'privacypolicy', 'private', 'product', 'products', 'profile', 'project', 'projects', 'promo',\n    'pub', 'public', 'purpose', 'put', 'python', 'query', 'random', 'ranking', 'read', 'readme',\n    'recent', 'recruit', 'recruitment', 'register', 'registration', 'release', 'remove', 'replies',\n    'report', 'reports', 'repositories', 'repository', 'req', 'request', 'requests', 'reset', 'roc',\n    'root', 'rss', 'ruby', 'rule', 'sag', 'sale', 'sales', 'sample', 'samples', 'save', 'school',\n    'script', 'scripts', 'search', 'secure', 'security', 'self', 'send', 'server', 'server-info',\n    'server-status', 'service', 'services', 'session', 'sessions', 'setting', 'settings', 'setup',\n    'share', 'shop', 'show', 'sign-in', 'sign-up', 'sign_in', 'sign_up', 'signin', 'signout', 'signup',\n    'site', 'sitemap', 'sites', 'smartphone', 'smtp', 'soporte', 'source', 'spec', 'special', 'sql',\n    'src', 'ssh', 'ssl', 'ssladmin', 'ssladministrator', 'sslwebmaster', 'staff', 'stage', 'staging',\n    'start', 'stat', 'state', 'static', 'stats', 'status', 'store', 'stores', 'stories', 'style',\n    'styleguide', 'stylesheet', 'stylesheets', 'subdomain', 'subscribe', 'subscription', 'subscriptions', 'suporte',\n    'support', 'svn', 'swf', 'sys', 'sysadmin', 'sysadministrator', 'system', 'tablet', 'tablets',\n    'tag', 'talk', 'task', 'tasks', 'team', 'teams', 'tech', 'telnet', 'term', 'terms',\n    'terms-of-service', 'terms_of_service', 'termsofservice', 'test', 'test1', 'test2', 'test3',\n    'teste', 'testing', 'tests', 'theme', 'themes', 'thread', 'threads', 'tmp', 'todo', 'tool',\n    'tools', 'top', 'topic', 'topics', 'tos', 'tour', 'translations', 'trends', 'tutorial', 'tux',\n    'tv', 'twitter', 'undef', 'unfollow', 'unsubscribe', 'update', 'upload', 'uploads', 'url',\n    'usage', 'user', 'username', 'users', 'usuario', 'vendas', 'ver', 'version', 'video', 'videos',\n    'visitor', 'watch', 'weather', 'web', 'webhook', 'webhooks', 'webmail', 'webmaster', 'website',\n    'websites', 'welcome', 'widget', 'widgets', 'wiki', 'win', 'windows', 'word', 'work', 'works',\n    'workshop', 'ww', 'wws', 'www', 'www1', 'www2', 'www3', 'www4', 'www5', 'www6', 'www7', 'wwws',\n    'wwww', 'xfn', 'xml', 'xmpp', 'xpg', 'xxx', 'yaml', 'year', 'yml', 'you', 'yourdomain', 'yourname',\n    'yoursite', 'yourusername',\n];\n\n// Regex matching forbidden usernames.\nconst FORBIDDEN_REGEXP = new RegExp(`^(${ANONYMOUS_USERNAME}|${FORBIDDEN_USERNAMES_REGEXPS.join('|')})$`, 'i');\n\n/**\n * Checks whether username is listed in FORBIDDEN_USERNAMES\n * or matches any root route path.\n */\nexport function isForbiddenUsername(username: string): boolean {\n    return !!username.match(APIFY_ID_REGEX) || !!username.match(FORBIDDEN_REGEXP);\n}\n\n/**\n * Executes array of promises in sequence and then returns array where Nth item is result of Nth promise.\n */\nexport async function sequentializePromises<T>(promises: (Promise<T> | (() => Promise<T>))[]) {\n    if (!promises.length) return [];\n    const results: T[] = [];\n\n    for (const promiseOrFunc of promises) {\n        const promise = promiseOrFunc instanceof Function ? promiseOrFunc() : promiseOrFunc;\n        results.push(await promise);\n    }\n\n    return results;\n}\n\n/**\n * Helper function for validation if parameter is an instance of given prototype or multiple prototypes.\n */\nexport function checkParamPrototypeOrThrow(paramVal: any, paramName: string, prototypes: any, prototypeName: string, isOptional = false) {\n    if (isOptional && (paramVal === undefined || paramVal === null)) return;\n\n    const hasCorrectPrototype = prototypes instanceof Array\n        ? prototypes.some((prototype) => paramVal instanceof prototype)\n        : paramVal instanceof prototypes;\n\n    if (!hasCorrectPrototype) throw new Error(`Parameter \"${paramName}\" must be an instance of ${prototypeName}`);\n}\n\ninterface Server {\n    removeListener(event: string, cb: (...params: any[]) => any): unknown;\n    on(event: string, cb: (...params: any[]) => any): unknown;\n    listen(port: number): unknown;\n}\n\n/**\n * Starts listening at a port specified in the constructor.\n * Unfortunately server.listen() is not a normal function that fails on error, so we need this trickery.\n * Returns a function that calls `server.listen(port)` and resolves once server starts listening.\n *\n * Usage: `promisifyServerListen(server)(1234)`;\n */\nexport function promisifyServerListen<T extends Server>(server: T) {\n    return async (port: number) => {\n        return new Promise<void>((resolve, reject) => {\n            const onError = (err: Error) => {\n                removeListeners();\n                reject(err);\n            };\n            const onListening = () => {\n                removeListeners();\n                resolve();\n            };\n            const removeListeners = () => {\n                server.removeListener('error', onError);\n                server.removeListener('listening', onListening);\n            };\n\n            server.on('error', onError);\n            server.on('listening', onListening);\n            server.listen(port);\n        });\n    };\n}\n\nexport function configureLogger(givenLog: Log, isProduction?: boolean) {\n    if (isProduction) {\n        givenLog.setOptions({\n            level: LogLevel.INFO,\n            logger: new LoggerJson(),\n        });\n    } else {\n        givenLog.setOptions({ level: LogLevel.DEBUG });\n    }\n}\n\n/**\n * Wraps given promise with timeout.\n */\nexport async function timeoutPromise<T>(promise: Promise<T>, timeoutMillis: number, errorMessage = 'Promise has timed-out') {\n    return new Promise((resolve, reject) => {\n        let timeout: number;\n        let hasFulfilled = false;\n\n        const callback = (err: Error | null, result?: T) => {\n            if (hasFulfilled) return;\n            clearTimeout(timeout);\n            hasFulfilled = true;\n            if (err) {\n                reject(err);\n                return;\n            }\n            resolve(result);\n        };\n\n        promise.then<void, void>((result: T) => callback(null, result), callback);\n        timeout = setTimeout(() => callback(new Error(errorMessage)), timeoutMillis) as unknown as number;\n    });\n}\n", "/*!\n * This module contains various client-side utility and helper functions.\n *\n * Author: <PERSON> (<EMAIL>)\n * Copyright(c) 2016 Apify. All rights reserved.\n *\n */\n\nimport { RELATIVE_URL_REGEX, VERSION_INT_MAJOR_BASE, VERSION_INT_MINOR_BASE } from '@apify/consts';\n\n/**\n * Returns true if object equals null or undefined, otherwise returns false.\n */\nexport function isNullOrUndefined(obj: unknown): boolean {\n    return obj == null;\n}\n\nexport function isBuffer(obj: any): boolean {\n    return obj != null && obj.constructor != null && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj);\n}\n\n/**\n * Converts Date object to ISO string.\n */\nexport function dateToString(date: Date, middleT: boolean): string {\n    if (!(date instanceof Date)) { return ''; }\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1; // January is 0, February is 1, and so on.\n    const day = date.getDate();\n    const hours = date.getHours();\n    const minutes = date.getMinutes();\n    const seconds = date.getSeconds();\n    const millis = date.getMilliseconds();\n\n    const pad = (num: number) => (num < 10 ? `0${num}` : num);\n    const datePart = `${year}-${pad(month)}-${pad(day)}`;\n    // eslint-disable-next-line no-nested-ternary\n    const millisPart = millis < 10 ? `00${millis}` : (millis < 100 ? `0${millis}` : millis);\n    const timePart = `${pad(hours)}:${pad(minutes)}:${pad(seconds)}.${millisPart}`;\n\n    return `${datePart}${middleT ? 'T' : ' '}${timePart}`;\n}\n\n/**\n * Ensures a string is shorter than a specified number of character, and truncates it if not,\n * appending a specific suffix to it.\n * @param str\n * @param maxLength\n * @param [suffix] Suffix to be appended to truncated string. Defaults to \"...[truncated]\".\n */\nexport function truncate(str: string, maxLength: number, suffix = '...[truncated]'): string {\n    maxLength = Math.floor(maxLength);\n\n    // TODO: we should just ignore rest of the suffix...\n    if (suffix.length > maxLength) {\n        throw new Error('suffix string cannot be longer than maxLength');\n    }\n\n    if (typeof str === 'string' && str.length > maxLength) {\n        str = str.substr(0, maxLength - suffix.length) + suffix;\n    }\n\n    return str;\n}\n\n/**\n * Gets ordinal suffix for a number (e.g. \"nd\" for 2).\n */\nexport function getOrdinalSuffix(num: number) {\n    // code from https://ecommerce.shopify.com/c/ecommerce-design/t/ordinal-number-in-javascript-1st-2nd-3rd-4th-29259\n    const s = ['th', 'st', 'nd', 'rd'];\n    const v = num % 100;\n    return s[(v - 20) % 10] || s[v] || s[0];\n}\n\ninterface Uri {\n    protocol?: string;\n    host?: string;\n    path?: string;\n    query?: string;\n    fragment?: string;\n    fragmentKey?: Record<string, unknown>;\n}\n\n/**\n * @deprecated use `new URL()` instead\n */\nexport function parseUrl(str: string): Uri {\n    if (typeof str !== 'string') return {};\n    const o = {\n        strictMode: false,\n        key: ['source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port',\n            'relative', 'path', 'directory', 'file', 'query', 'fragment'],\n        q: {\n            name: 'queryKey',\n            parser: /(?:^|&)([^&=]*)=?([^&]*)/g,\n        },\n        parser: {\n            strict: /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?))?((((?:[^?#\\/]*\\/)*)([^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/, // eslint-disable-line max-len,no-useless-escape\n            loose: /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/, // eslint-disable-line max-len,no-useless-escape\n        },\n    };\n\n    const m = o.parser[o.strictMode ? 'strict' : 'loose'].exec(str);\n    const uri: Record<string, any> = {};\n    let i = o.key.length;\n\n    while (i--) uri[o.key[i]] = m![i] || '';\n\n    uri[o.q.name] = {};\n    uri[o.key[12]].replace(o.q.parser, ($0: any, $1: any, $2: any) => {\n        if ($1) uri[o.q.name][$1] = $2;\n    });\n\n    // our extension - parse fragment using a query string format (i.e. \"#key1=val1&key2=val2\")\n    // this format is used by many websites\n    uri.fragmentKey = {};\n    if (uri.fragment) {\n        // casting as any, as the usage seems invalid, replacer should always return something (but keeping as is to mitigate unwanted BCs)\n        uri.fragment.replace(o.q.parser, (($0: any, $1: any, $2: any) => {\n            if ($1) uri.fragmentKey![$1] = $2;\n        }) as any);\n    }\n\n    return uri;\n}\n\nexport function normalizeUrl(url: string, keepFragment?: boolean) {\n    if (typeof url !== 'string' || !url.length) {\n        return null;\n    }\n\n    let urlObj;\n\n    try {\n        urlObj = new URL(url.trim());\n    } catch {\n        return null;\n    }\n\n    const { searchParams } = urlObj;\n\n    for (const key of [...searchParams.keys()]) {\n        if (key.startsWith('utm_')) {\n            searchParams.delete(key);\n        }\n    }\n\n    searchParams.sort();\n\n    const protocol = urlObj.protocol.toLowerCase();\n    const host = urlObj.host.toLowerCase();\n    const path = urlObj.pathname.replace(/\\/$/, '');\n    const search = searchParams.toString() ? `?${searchParams}` : '';\n    const hash = keepFragment ? urlObj.hash : '';\n\n    return `${protocol}//${host}${path}${search}${hash}`;\n}\n\n// Helper function for markdown rendered marked\n// If passed referrerHostname, it renders links outside that hostname in readme with rel=\"noopener noreferrer\" and target=\"_blank\" attributes\n// And links outside apify.com in readme with rel=\"noopener noreferrer nofollow\" and target=\"_blank\" attributes\nexport function markedSetNofollowLinks(href: string, title: string, text: string, referrerHostname?: string) {\n    let urlParsed: URL;\n    try {\n        urlParsed = new URL(href);\n    } catch {\n        // Probably invalid url, go on\n    }\n    const isApifyLink = (urlParsed! && /(\\.|^)apify\\.com$/i.test(urlParsed.hostname));\n    const isSameHostname = !referrerHostname || (urlParsed! && urlParsed.hostname === referrerHostname);\n\n    if (isApifyLink && isSameHostname) {\n        return `<a href=\"${href}\">${title || text}</a>`;\n    } if (isApifyLink) {\n        return `<a rel=\"noopener noreferrer\" target=\"_blank\" href=\"${href}\">${title || text}</a>`;\n    }\n\n    return `<a rel=\"noopener noreferrer nofollow\" target=\"_blank\" href=\"${href}\">${title || text}</a>`;\n}\n\n// Helper function for markdown rendered marked\n// Decreases level of all headings by one, h1 -> h2\nexport function markedDecreaseHeadsLevel(text: string, level: number) {\n    level += 1;\n    return `<h${level}>${text}</h${level}>`;\n}\n\n/**\n * Converts integer version number previously generated by buildNumberToInt() or versionNumberToInt()\n * to string in a form 'MAJOR.MINOR' or 'MAJOR.MINOR.BUILD' in case build number is non-zero.\n */\nexport function buildOrVersionNumberIntToStr(int: number): string | null {\n    if (typeof int !== 'number' || !(int >= 0)) return null;\n\n    const major = Math.floor(int / VERSION_INT_MAJOR_BASE);\n    const remainder = int % VERSION_INT_MAJOR_BASE;\n    const minor = Math.floor(remainder / VERSION_INT_MINOR_BASE);\n    const build = remainder % VERSION_INT_MINOR_BASE;\n\n    let str = `${major}.${minor}`;\n    if (build > 0) str += `.${build}`;\n\n    return str;\n}\n\n// escaped variants for various strings\nconst ESCAPE_DOT = '\\uFF0E'; // \".\"\nconst ESCAPE_DOLLAR = '\\uFF04'; // \"$\"\nconst ESCAPE_TO_BSON = '\\uFF54\\uFF4F\\uFF22\\uFF33\\uFF2F\\uFF2E'; // \"toBSON\"\nconst ESCAPE_TO_STRING = '\\uFF54\\uFF4F\\uFF33\\uFF54\\uFF52\\uFF49\\uFF4E\\uFF47'; // \"toString\"\nconst ESCAPE_BSON_TYPE = '\\uFF3F\\uFF42\\uFF53\\uFF4F\\uFF4E\\uFF54\\uFF59\\uFF50\\uFF45'; // \"_bsontype\"\nconst ESCAPE_NULL = ''; // \"\\0\" (null chars are removed completely, they won't be recovered)\n\nconst REGEXP_IS_ESCAPED = new RegExp(`(${ESCAPE_DOT}|^${ESCAPE_DOLLAR}|^${ESCAPE_TO_BSON}$|^${ESCAPE_BSON_TYPE}|^${ESCAPE_TO_STRING}$)`);\n\nconst REGEXP_DOT = new RegExp(ESCAPE_DOT, 'g');\nconst REGEXP_DOLLAR = new RegExp(`^${ESCAPE_DOLLAR}`);\nconst REGEXP_TO_BSON = new RegExp(`^${ESCAPE_TO_BSON}$`);\nconst REGEXP_TO_STRING = new RegExp(`^${ESCAPE_TO_STRING}$`);\nconst REGEXP_BSON_TYPE = new RegExp(`^${ESCAPE_BSON_TYPE}$`);\n\n/**\n * If a property name is invalid for MongoDB or BSON, the function transforms\n * it to a valid form, which can be (most of the time) reversed back using unescapePropertyName().\n * For a detailed list of transformations, see escapeForBson().\n * @private\n */\nexport function escapePropertyName(name: string) {\n    // From MongoDB docs:\n    // \"Field names cannot contain dots (.) or null (\"\\0\") characters, and they must not start with\n    // a dollar sign (i.e. $). See faq-dollar-sign-escaping for an alternate approach.\"\n    // Moreover, the name cannot be \"toBSON\" and \"_bsontype\" because they have a special meaning in BSON serialization.\n    // Other special BSON properties like $id and $db are covered thanks to $ escape.\n    // 2021-06-25: The `toString` string was added as a property to escape because\n    // it generates issues due to a bug in mongo bson-ext package https://jira.mongodb.org/browse/NODE-3375.\n\n    // pre-test to improve performance\n    if (/(\\.|^\\$|^toBSON$|^_bsontype$|^toString$|\\0)/.test(name)) {\n        name = name.replace(/\\./g, ESCAPE_DOT);\n        name = name.replace(/^\\$/, ESCAPE_DOLLAR);\n        name = name.replace(/^toBSON$/, ESCAPE_TO_BSON);\n        name = name.replace(/^toString$/, ESCAPE_TO_STRING);\n        name = name.replace(/^_bsontype$/, ESCAPE_BSON_TYPE);\n        name = name.replace(/\\0/g, ESCAPE_NULL);\n    }\n\n    return name;\n}\n\n/**\n * Reverses a string transformed using escapePropertyName() back to its original form.\n * Note that the reverse transformation might not be 100% correct for certain unlikely-to-occur strings\n * (e.g. string contain null chars).\n * @private\n */\nexport function unescapePropertyName(name: string) {\n    // pre-test to improve performance\n    if (REGEXP_IS_ESCAPED.test(name)) {\n        name = name.replace(REGEXP_DOT, '.');\n        name = name.replace(REGEXP_DOLLAR, '$');\n        name = name.replace(REGEXP_TO_BSON, 'toBSON');\n        name = name.replace(REGEXP_TO_STRING, 'toString');\n        name = name.replace(REGEXP_BSON_TYPE, '_bsontype');\n    }\n\n    return name;\n}\n\n/**\n * Traverses an object, creates a deep clone if requested and transforms object keys and values using a provided function.\n * The `traverseObject` is recursive, hence if the input object has circular references, the function will run into\n * and infinite recursion and crash the Node.js process.\n * @param obj Object to traverse, it must not contain circular references!\n * @param clone If true, object is not modified but cloned.\n * @param transformFunc Function used to transform the property names na value.\n *  It has the following signature: `(key, value) => [key, value]`.\n *  Beware that the transformed value is only set if it !== old value.\n * @returns {*}\n * @private\n */\nexport function traverseObject(obj: Record<string, any>, clone: boolean, transformFunc: (key: string, value: unknown) => [string, unknown]) {\n    // Primitive types don't need to be cloned or further traversed.\n    // Buffer needs to be skipped otherwise this will iterate over the whole buffer which kills the event loop.\n    if (\n        obj === null\n        || typeof obj !== 'object'\n        || Object.prototype.toString.call(obj) === '[object Date]'\n        || isBuffer(obj)\n    ) return obj;\n\n    let result;\n\n    if (Array.isArray(obj)) {\n        // obj is an array, keys are numbers and never need to be escaped\n        result = clone ? new Array(obj.length) : obj;\n        for (let i = 0; i < obj.length; i++) {\n            const val = traverseObject(obj[i], clone, transformFunc);\n            if (clone) result[i] = val;\n        }\n\n        return result;\n    }\n\n    // obj is an object, all keys need to be checked\n    result = clone ? {} : obj;\n    for (const key in obj) { // eslint-disable-line no-restricted-syntax, guard-for-in\n        const val = traverseObject(obj[key], clone, transformFunc);\n        const [transformedKey, transformedVal] = transformFunc(key, val);\n        if (key === transformedKey) {\n            // For better efficiency, skip setting the key-value if not cloning and nothing changed\n            if (clone || val !== transformedVal) result[key] = transformedVal;\n        } else {\n            // Key has been renamed\n            result[transformedKey] = transformedVal;\n            if (!clone) delete obj[key];\n        }\n    }\n\n    return result;\n}\n\n/**\n * Transforms an object so that it can be stored to MongoDB or serialized to BSON.\n * It does so by transforming prohibited property names (e.g. names starting with \"$\",\n * containing \".\" or null char, equal to \"toBSON\" or \"_bsontype\") to equivalent full-width Unicode chars\n * which are normally allowed. To revert this transformation, use unescapeFromBson().\n * @param obj Object to be transformed. It must not contain circular references or any complex types (e.g. Maps, Promises etc.)!\n * @param clone If true, the function transforms a deep clone of the object rather than the original object.\n * @returns {*} Transformed object\n */\nexport function escapeForBson(obj: Record<string, any>, clone = false) {\n    return traverseObject(obj, clone, (key, value) => [escapePropertyName(key), value]);\n}\n\n/**\n * Reverts a transformation of object property names performed by escapeForBson().\n * Note that the reverse transformation might not be 100% equal to the original object\n * for certain unlikely-to-occur property name (e.g. one contain null chars or full-width Unicode chars).\n * @param obj Object to be transformed. It must not contain circular references or any complex types (e.g. Maps, Promises etc.)!\n * @param clone If true, the function transforms a deep clone of the object rather than the original object.\n * @returns {*} Transformed object.\n */\nexport function unescapeFromBson(obj: Record<string, any>, clone = false): Record<string, any> {\n    return traverseObject(obj, clone, (key, value) => [unescapePropertyName(key), value]);\n}\n\n/**\n * Determines whether an object contains property names that cannot be stored to MongoDB.\n * See escapeForBson() for more details.\n * Note that this function only works with objects that are serializable to JSON!\n * @param obj Object to be checked. It must not contain circular references or any complex types (e.g. Maps, Promises etc.)!\n * @returns {boolean} Returns true if object is invalid, otherwise it returns false.\n */\nexport function isBadForMongo(obj: Record<string, any>): boolean {\n    let isBad = false;\n    try {\n        traverseObject(obj, false, (key, value) => {\n            const escapedKey = escapePropertyName(key);\n            if (key !== escapedKey) {\n                isBad = true;\n                throw new Error();\n            }\n            return [key, value];\n        });\n    } catch (e) {\n        if (!isBad) throw e;\n    }\n    return isBad;\n}\n\nexport class JsonVariable {\n    constructor(readonly name: string) { }\n\n    getToken() {\n        return `{{${this.name}}}`;\n    }\n}\n\n/**\n * Stringifies provided value to JSON with a difference that supports functions that\n * are stringified using .toString() method.\n *\n * In addition to that supports instances of JsonVariable('my.token') that are replaced\n * with a {{my.token}}.\n */\nexport function jsonStringifyExtended(value: Record<string, any>, replacer?: ((k: string, val: unknown) => unknown) | null, space = 0): string {\n    if (replacer && !(replacer instanceof Function)) throw new Error('Parameter \"replacer\" of jsonStringifyExtended() must be a function!');\n\n    const replacements: Record<string, string> = {};\n\n    const extendedReplacer = (key: string, val: unknown) => {\n        val = replacer ? replacer(key, val) : val;\n\n        if (val instanceof Function) return val.toString();\n        if (val instanceof JsonVariable) {\n            const randomToken = `<<<REPLACEMENT_TOKEN::${Math.random()}>>>`;\n            replacements[randomToken] = val.getToken();\n            return randomToken;\n        }\n\n        return val;\n    };\n\n    let stringifiedValue = JSON.stringify(value, extendedReplacer, space);\n    Object.entries(replacements).forEach(([replacementToken, replacementValue]) => {\n        stringifiedValue = stringifiedValue.replace(`\"${replacementToken}\"`, replacementValue);\n    });\n\n    return stringifiedValue;\n}\n\n/**\n * Splits a full name into the first name and last name, trimming all internal and external spaces.\n * Returns an array with two elements or null if splitting is not possible.\n */\nexport function splitFullName(fullName: string) {\n    if (typeof fullName !== 'string') return [null, null];\n\n    const names = (fullName || '').trim().split(' ');\n    const nonEmptyNames = names.filter((val) => val);\n\n    if (nonEmptyNames.length === 0) {\n        return [null, null];\n    }\n\n    if (nonEmptyNames.length === 1) {\n        return [null, nonEmptyNames[0]];\n    }\n\n    return [names[0], nonEmptyNames.slice(1).join(' ')];\n}\n\n/**\n * Perform a Regex test on a given URL to see if it is relative.\n */\nexport function isUrlRelative(url: string): boolean {\n    return RELATIVE_URL_REGEX.test(url);\n}\n", "import log from '@apify/log';\n\nimport { delayPromise } from './utilities';\n\nexport class RetryableError extends Error {\n    readonly error: Error;\n\n    constructor(error: Error, ...args: unknown[]) {\n        super(...args as [string]);\n        this.error = error;\n    }\n}\n\n// extend the error with added properties\nexport interface RetryableError extends Error {}\n\nexport async function retryWithExpBackoff<T>(\n    params: { func?: (...args: unknown[]) => T | Promise<T>, expBackoffMillis?: number, expBackoffMaxRepeats?: number } = {},\n): Promise<T> {\n    const { func, expBackoffMillis, expBackoffMaxRepeats } = params;\n\n    if (typeof func !== 'function') {\n        throw new Error('Parameter \"func\" should be a function.');\n    }\n\n    if (typeof expBackoffMillis !== 'number') {\n        throw new Error('Parameter \"expBackoffMillis\" should be a number.');\n    }\n\n    if (typeof expBackoffMaxRepeats !== 'number') {\n        throw new Error('Parameter \"expBackoffMaxRepeats\" should be a number.');\n    }\n\n    for (let i = 0; ; i++) {\n        let error;\n\n        try {\n            return await func();\n        } catch (e) {\n            error = e;\n        }\n\n        if (!(error instanceof RetryableError)) {\n            throw error;\n        }\n\n        if (i >= expBackoffMaxRepeats - 1) {\n            throw error.error;\n        }\n\n        const waitMillis = expBackoffMillis * (2 ** i);\n        const rand = (from: number, to: number) => from + Math.floor(Math.random() * (to - from + 1));\n        const randomizedWaitMillis = rand(waitMillis, waitMillis * 2);\n\n        if (i === Math.round(expBackoffMaxRepeats / 2)) {\n            log.warning(`Retry failed ${i} times and will be repeated in ${randomizedWaitMillis}ms`, {\n                originalError: error.error.message,\n                errorDetails: Reflect.get(error.error, 'details'),\n            });\n        }\n\n        await delayPromise(randomizedWaitMillis);\n    }\n}\n", "import { cryptoRandomObjectId, timeoutPromise } from './utilities';\n\nexport enum CHECK_TYPES {\n    MONGODB_PING = 'MONGODB_PING',\n    MONGODB_READ = 'MONGODB_READ',\n    MONGODB_WRITE = 'MONGODB_WRITE',\n    REDIS = 'REDIS', // Old alias for 'REDIS_WRITE', deprecated\n    REDIS_PING = 'REDIS_PING',\n    REDIS_WRITE = 'REDIS_WRITE',\n}\n\ntype CheckType<T extends Record<string, any> = Record<string, any>> = { client: T; type: CHECK_TYPES };\n\ninterface HealthCheckerOptions {\n    checks: CheckType[];\n    redisPrefix?: string;\n    redisTtlSecs?: number;\n    checkTimeoutMillis?: number;\n    mongoDbWriteTestCollection?: string;\n    mongoDbWriteTestRemoveOlderThanSecs?: number;\n}\n\n/**\n * Provides health-checking functionality to ensure that connection to Redis and MongoDB is working.\n *\n * Example use:\n *\n * ```javascript\n * const redis = new Redis();\n * const mongo = await MongoClient.connect('mongodb://127.0.0.1:3001/my-db');\n *\n * const checks = [{\n *     client: redis,\n *     type: HealthChecker.CHECK_TYPES.REDIS,\n * }, {\n *     client: mongo.db('my-db'),\n *     type: HealthChecker.CHECK_TYPES.MONGODB_READ,\n * }];\n *\n * const checker = new HealthChecker({ checks });\n * setInterval(() => checker.ensureIsHealthy().then(() => console.log('ok'), err => console.log(err)), 5000);\n * ```\n */\nexport class HealthChecker {\n    static readonly CHECK_TYPES = CHECK_TYPES;\n\n    checks: CheckType[];\n\n    redisPrefix: string;\n\n    redisTtlSecs: number;\n\n    checkTimeoutMillis: number;\n\n    mongoDbWriteTestCollection: string;\n\n    mongoDbWriteTestRemoveOlderThanSecs: number;\n\n    constructor(private readonly options: HealthCheckerOptions) {\n        const {\n            checks,\n            redisPrefix = 'health-check',\n            redisTtlSecs = 15,\n            checkTimeoutMillis = 15000,\n            mongoDbWriteTestCollection = 'healthCheckPlayground',\n            mongoDbWriteTestRemoveOlderThanSecs = 15,\n        } = options;\n\n        if (!Array.isArray(checks)) throw new Error('Parameter \"check\" must be an array');\n        checks.map((check) => this._validateCheck(check));\n\n        this.checks = checks;\n        this.redisPrefix = redisPrefix;\n        this.redisTtlSecs = redisTtlSecs;\n        this.checkTimeoutMillis = checkTimeoutMillis;\n        this.mongoDbWriteTestCollection = mongoDbWriteTestCollection;\n        this.mongoDbWriteTestRemoveOlderThanSecs = mongoDbWriteTestRemoveOlderThanSecs;\n    }\n\n    async ensureIsHealthy() {\n        for (const check of this.checks) {\n            try {\n                const checkPromise = this._performCheck(check);\n                await timeoutPromise(checkPromise, this.checkTimeoutMillis, 'Check has timed-out');\n            } catch (_err) {\n                const err = _err as Error;\n                throw new Error(`Health check test \"${check.type}\" failed with an error: ${err.message}\"`);\n            }\n        }\n    }\n\n    _validateCheck(check: CheckType): void {\n        if (!(check.type in CHECK_TYPES)) throw new Error(`Check type \"${check.type}\" is invalid`);\n        if (typeof check.client !== 'object') throw new Error(`Check client must be an object got \"${typeof check.client}\" instead`);\n    }\n\n    async _performCheck(check: CheckType): Promise<void> {\n        switch (check.type) {\n            case CHECK_TYPES.MONGODB_PING:\n                return this._testMongoDbPing(check);\n            case CHECK_TYPES.MONGODB_READ:\n                return this._testMongoDbRead(check);\n            case CHECK_TYPES.MONGODB_WRITE:\n                return this._testMongoDbWrite(check);\n            case CHECK_TYPES.REDIS_PING:\n                return this._testRedisPing(check);\n            case CHECK_TYPES.REDIS:\n            case CHECK_TYPES.REDIS_WRITE:\n                return this._testRedisWrite(check);\n            default:\n                throw new Error('Unknown check type');\n        }\n    }\n\n    async _testMongoDbPing({ client }: CheckType) {\n        const response = await client.command({ ping: 1 });\n        if (response.ok !== 1) throw new Error(`Got ${response.ok} instead of 1!`);\n    }\n\n    async _testMongoDbRead({ client }: CheckType) {\n        const response = await client.listCollections().toArray();\n        if (!Array.isArray(response)) throw new Error(`Got ${typeof response} instead of an array!`);\n    }\n\n    async _testMongoDbWrite({ client }: CheckType) {\n        const id = cryptoRandomObjectId();\n        const collection = client.collection(this.mongoDbWriteTestCollection);\n\n        // Remove old test items.\n        await collection.deleteMany({\n            createdAt: {\n                $lt: new Date(Date.now() - this.mongoDbWriteTestRemoveOlderThanSecs * 1000),\n            },\n        });\n\n        // Insert and read some item.\n        await collection.insertOne({\n            _id: id,\n            createdAt: new Date(),\n        });\n        const retrieved = await collection.findOne({ _id: id });\n        if (!retrieved) throw new Error(`Item with ID \"${id}\" not found!`);\n    }\n\n    async _testRedisPing({ client }: CheckType) {\n        const response = await client.ping();\n        if (response !== 'PONG') throw new Error(`Got \"${response}\" instead of \"PONG\"!`);\n    }\n\n    async _testRedisWrite({ client }: CheckType) {\n        const key = `${this.redisPrefix}:${cryptoRandomObjectId()}`;\n        const expected = 'OK';\n\n        // Set some value in Redis and try to read it.\n        await client.set(key, expected, 'EX', this.redisTtlSecs);\n        const given = await client.get(key);\n        if (given !== expected) throw new Error(`Returned value \"${given}\" is not equal to \"${expected}\"!`);\n    }\n}\n", "import type { TransformCallback } from 'node:stream';\nimport { Transform } from 'node:stream';\n\n// TODO: Fix the issue with the separate 'data' and 'object' event - see below.\n// For example, we could just have 'data' and it would just pass the object.\n// For that, you can use the 'objectMode' param\n\n/**\n * A transforming stream which accepts string/Buffer data with JSON Lines objects on input\n * and emits 'object' event for every parsed JavaScript objects.\n * The stream passes through the original data.\n * Each JSON object is expected to be on a separate line, some lines might be empty or contain whitespace.\n * After each JSON object there needs to be '\\n' or end of stream.\n * This stream is especially useful for processing stream from Docker engine, such as:\n *\n * <pre>\n *  {\"status\":\"Preparing\",\"progressDetail\":{},\"id\":\"e0380bb6c0bb\"}\n *  {\"status\":\"Preparing\",\"progressDetail\":{},\"id\":\"9f8566ee5135\"}\n *  {\"errorDetail\":{\"message\":\"no basic auth credentials\"},\"error\":\"no basic auth credentials\"}\n * </pre>\n *\n * **WARNING**: You still need to consume the `data` event from the transformed stream,\n * otherwise the internal buffers will get full and the stream might be corked.\n */\nexport class ParseJsonlStream extends Transform {\n    private pendingChunk: string | null = null;\n\n    parseLineAndEmitObject(line: string): void {\n        line = line.trim();\n\n        if (!line) {\n            return;\n        }\n\n        try {\n            const obj = JSON.parse(line);\n            this.emit('object', obj);\n        } catch (e) {\n            throw new Error(`Cannot parse JSON stream data ('${String(line)}'): ${String(e)}`);\n        }\n    }\n\n    _transform(chunk: any, encoding: BufferEncoding, callback: TransformCallback): void {\n        let allData;\n        if (this.pendingChunk) {\n            allData = this.pendingChunk + chunk;\n            this.pendingChunk = null;\n        } else {\n            allData = chunk;\n        }\n\n        const lines = allData.toString().split('\\n');\n\n        // One line can span multiple chunks, so if the new chunk doesn't end with '\\n',\n        // store the last part and later concat it with the new chunk\n        if (lines[lines.length - 1] !== '') {\n            this.pendingChunk = lines.pop();\n        }\n\n        try {\n            for (let i = 0; i < lines.length; i++) {\n                this.parseLineAndEmitObject(lines[i]);\n            }\n        } catch (err) {\n            callback(err as Error, null);\n            return;\n        }\n\n        callback(null, chunk);\n    }\n\n    // This function is called right after stream.end() is called by the writer.\n    // It just tries to process the pending chunk and returns an error if that fails.\n    _flush(callback: TransformCallback): void {\n        if (this.pendingChunk) {\n            try {\n                this.parseLineAndEmitObject(this.pendingChunk);\n                this.pendingChunk = null;\n            } catch (err) {\n                callback(err as Error, null);\n                return;\n            }\n        }\n        callback();\n    }\n}\n", "import type { PassThrough, Readable } from 'node:stream';\n\n/**\n * Concat data from stream to Buffer\n */\nexport async function concatStreamToBuffer(stream: Readable | PassThrough): Promise<Buffer> {\n    return new Promise((resolve, reject) => {\n        const chunks: Uint8Array[] = [];\n        stream\n            .on('data', (chunk) => {\n                chunks.push(chunk);\n            })\n            .on('error', (e) => reject(e))\n            .on('end', () => {\n                const buffer = Buffer.concat(chunks);\n                return resolve(buffer);\n            });\n    });\n}\n\n/**\n * Flushes the provided stream into a Buffer and transforms\n * it to a String using the provided encoding or utf-8 as default.\n */\nexport async function readStreamToString(stream: Readable | PassThrough, encoding?: BufferEncoding): Promise<string> {\n    const buffer = await concatStreamToBuffer(stream);\n    return buffer.toString(encoding);\n}\n", "/* eslint-disable max-classes-per-file */\n\nimport { jsonStringifyExtended, JsonVariable } from './utilities.client';\n\nclass WebhookPayloadTemplateError extends Error {\n    constructor(message?: string) {\n        super(message);\n        this.name = this.constructor.name;\n        if (typeof Error.captureStackTrace === 'function') {\n            Error.captureStackTrace(this, this.constructor);\n        }\n    }\n}\n\nexport class InvalidJsonError extends WebhookPayloadTemplateError {\n    constructor(originalError: Error) {\n        super(originalError.message);\n    }\n}\nexport class InvalidVariableError extends Error {\n    constructor(variable?: string) {\n        super(`Invalid payload template variable: ${variable}`);\n    }\n}\n\ninterface ParsePosition {\n    isInsideString: boolean;\n    openBraceIndex: number;\n    closeBraceIndex: number;\n}\n\n/**\n * WebhookPayloadTemplate enables creation and parsing of webhook payload template strings.\n * Template strings are JSON that may include template variables enclosed in double\n * curly brackets: `{{variable}}`. When the template is parsed, variables are replaced\n * with values from a provided context.\n *\n * This is useful to create dynamic webhook payloads where a template is saved on webhook\n * creation and then variables are dynamically added on webhook dispatch.\n *\n * **Example:**\n * ```js\n * const payloadTemplate = `{\n *    \"id\": \"some-id\",\n *    \"createdAt\": \"2019-05-08T15:22:21.095Z\",\n *    \"dataToSend\": {{data}}\n * }`\n *\n * const data = {\n *     status: 200,\n *     body: 'hello world'\n * }\n *\n * const payloadObject = WebhookPayloadTemplate.parse(payloadTemplate, null, { data })\n * ```\n *\n * **Produces:**\n * ```js\n * {\n *     id: \"some-id\",\n *    createdAt: '2019-05-08T15:22:21.095Z',\n *    dataToSend: {\n *        status: 200,\n *        body: 'hello world'\n *    }\n * }\n * ```\n * @hideconstructor\n */\nexport class WebhookPayloadTemplate {\n    private payload: string;\n\n    readonly replacedVariables: { variableName: string, replacement: string }[] = [];\n\n    constructor(private readonly template: string,\n                private readonly allowedVariables: Set<string> | null = null,\n                private readonly context: Record<string, any> = {}) {\n        this.payload = template;\n    }\n\n    /**\n     * Parse existing webhook payload template string into an object, replacing\n     * template variables using the provided context.\n     *\n     * Parse also validates the template structure, so it can be used\n     * to check validity of the template JSON and usage of allowedVariables.\n     */\n    static parse(\n        payloadTemplate: string,\n        allowedVariables: Set<string> | null = null,\n        context: Record<string, any> = {},\n        options: { interpolateStrings?: boolean } = {},\n    ): Record<string, any> {\n        const type = typeof payloadTemplate;\n        if (type !== 'string') throw new Error(`Cannot parse a ${type} payload template.`);\n        const template = new WebhookPayloadTemplate(payloadTemplate, allowedVariables, context);\n        const data = template._parse(); // eslint-disable-line no-underscore-dangle\n        if (options.interpolateStrings) {\n            return template._interpolate(data); // eslint-disable-line no-underscore-dangle\n        }\n        return data;\n    }\n\n    /**\n     * Stringify an object into a webhook payload template.\n     * Values created using `getTemplateVariable('foo.bar')`\n     * will be stringified to `{{foo.bar}}` template variable.\n     */\n    static stringify(objectTemplate: Record<string, any>, replacer?: ((_: any) => string) | null, indent = 2): string {\n        const type = typeof objectTemplate;\n        if (!objectTemplate || type !== 'object') throw new Error(`Cannot stringify a ${type} payload template.`);\n        return jsonStringifyExtended(objectTemplate, replacer, indent);\n    }\n\n    /**\n     * Produces an instance of a template variable that can be used\n     * in objects and will be stringified into `{{variableName}}` syntax.\n     *\n     * **Example:**\n     * ```js\n     * const resourceVariable = WebhookPayloadTemplate.getVariable('resource');\n     * const objectTemplate = {\n     *     foo: 'foo',\n     *     bar: ['bar'],\n     *     res: resourceVariable,\n     * }\n     *\n     * const payloadTemplate = WebhookPayloadTemplate.stringify(objectTemplate);\n     * ```\n     *\n     * **Produces:**\n     * ```json\n     * {\n     *     \"foo\": \"foo\",\n     *     \"bar\": [\"bar\"],\n     *     \"res\": {{resource}},\n     * }\n     * ```\n     */\n    static getVariable(variableName: string): JsonVariable {\n        return new JsonVariable(variableName);\n    }\n\n    private _parse() {\n        let currentIndex = 0;\n\n        while (true) {\n            try {\n                return JSON.parse(this.payload);\n            } catch (err) {\n                const position = this._findPositionOfNextVariable(currentIndex);\n                // When we catch an error from JSON.parse, but there's no remaining variable, we must have an invalid JSON.\n                if (!position) {\n                    throw new InvalidJsonError(err as Error);\n                }\n                if (!position.isInsideString) {\n                    this._replaceVariable(position);\n                }\n                currentIndex = position.openBraceIndex + 1;\n            }\n        }\n    }\n\n    private _interpolate(value: any): any {\n        if (typeof value === 'string') {\n            return this._interpolateString(value);\n        }\n        // Array needs to go before object!\n        if (Array.isArray(value)) {\n            return this._interpolateArray(value);\n        }\n        if (typeof value === 'object' && value !== null) {\n            return this._interpolateObject(value);\n        }\n        // We can't interpolate anything else\n        return value;\n    }\n\n    private _interpolateString(value: string): string {\n        // If the string matches exactly, we return the variable value including the type\n        if (value.match(/^\\{\\{([a-zA-Z0-9.]+)\\}\\}$/)) {\n            // This just strips the {{ and }}\n            const variableName = value.substring(2, value.length - 2);\n            this._validateVariableName(variableName);\n            return this._getVariableValue(variableName);\n        }\n        // If it's just a part of substring, we replace the respective variables with their string variants\n        return value.replace(/\\{\\{([a-zA-Z0-9.]+)\\}\\}/g, (match, variableName) => {\n            this._validateVariableName(variableName);\n            const variableValue = this._getVariableValue(variableName);\n            return `${variableValue}`;\n        });\n    }\n\n    private _interpolateObject(value: Record<string, any>): Record<string, any> {\n        const result = {} as Record<string, any>;\n        Object.entries(value).forEach(([key, v]) => {\n            result[key] = this._interpolate(v);\n        });\n        return result;\n    }\n\n    private _interpolateArray(value: any[]): any[] {\n        return value.map(this._interpolate.bind(this));\n    }\n\n    private _findPositionOfNextVariable(startIndex = 0): ParsePosition | null {\n        const openBraceIndex = this.payload.indexOf('{{', startIndex);\n        const closeBraceIndex = this.payload.indexOf('}}', openBraceIndex) + 1;\n        const someVariableMaybeExists = (openBraceIndex > -1) && (closeBraceIndex > -1);\n        if (!someVariableMaybeExists) return null;\n        const isInsideString = this._isVariableInsideString(openBraceIndex);\n        return { isInsideString, openBraceIndex, closeBraceIndex };\n    }\n\n    private _isVariableInsideString(openBraceIndex: number): boolean {\n        const unescapedQuoteCount = this._countUnescapedDoubleQuotesUpToIndex(openBraceIndex);\n        return unescapedQuoteCount % 2 === 1;\n    }\n\n    private _countUnescapedDoubleQuotesUpToIndex(index: number): number {\n        const payloadSection = this.payload.substring(0, index);\n        let unescapedQuoteCount = 0;\n        for (let i = 0; i < payloadSection.length; i++) {\n            const char = payloadSection[i];\n            const prevChar = payloadSection[i - 1];\n            if (char === '\"' && prevChar !== '\\\\') {\n                unescapedQuoteCount++;\n            }\n        }\n        return unescapedQuoteCount;\n    }\n\n    private _replaceVariable({ openBraceIndex, closeBraceIndex }: ParsePosition): void {\n        const variableName = this.payload.substring(openBraceIndex + 2, closeBraceIndex - 1);\n        this._validateVariableName(variableName);\n        const replacement = this._getVariableReplacement(variableName)!;\n        this.replacedVariables.push({ variableName, replacement });\n        this.payload = this.payload.substring(0, openBraceIndex) + replacement + this.payload.substring(closeBraceIndex + 1);\n    }\n\n    private _validateVariableName(variableName: string): void {\n        if (this.allowedVariables === null) return;\n        const [variable] = variableName.split('.');\n\n        // Properties of the variable are not validated on purpose\n        // as they will later be set to null if not found.\n        // This serves to enable dynamic variable structures.\n        const isVariableValid = this.allowedVariables.has(variable);\n\n        if (!isVariableValid) throw new InvalidVariableError(variableName);\n    }\n\n    private _getVariableValue(variableName: string): any {\n        const [variable, ...properties] = variableName.split('.');\n        const context = this.context[variable];\n        const value = properties.reduce((ctx, prop) => {\n            if (!ctx || typeof ctx !== 'object') return null;\n            return ctx[prop];\n        }, context);\n        return value;\n    }\n\n    private _getVariableReplacement(variableName: string): string | null {\n        const value = this._getVariableValue(variableName);\n        return value ? JSON.stringify(value) : null;\n    }\n}\n", "import type { KeyObject } from 'node:crypto';\nimport crypto from 'node:crypto';\n\nimport { cryptoRandomObjectId } from './utilities';\n\nconst ENCRYPTION_ALGORITHM = 'aes-256-gcm';\nconst ENCRYPTION_KEY_LENGTH = 32;\nconst ENCRYPTION_IV_LENGTH = 16;\nconst ENCRYPTION_AUTH_TAG_LENGTH = 16;\n\ntype DecryptOptions = {\n    privateKey: KeyObject;\n    encryptedPassword: string;\n    encryptedValue: string;\n}\n\ntype EncryptOptions = {\n    publicKey: KeyObject;\n    value: string;\n}\n\n/**\n * It encrypts the given value using AES cipher and the password for encryption using the public key.\n * NOTE: The encryption password is a string of encryption key and initial vector used for cipher.\n * It returns the encrypted password and encrypted value in BASE64 format.\n *\n * @param publicKey {KeyObject} Public key used for encryption\n * @param value {string} Value to be encrypted\n * @returns {Object<encryptedPassword, encryptedValue>}\n */\nexport function publicEncrypt({ publicKey, value }: EncryptOptions) {\n    const key = cryptoRandomObjectId(ENCRYPTION_KEY_LENGTH);\n    const initVector = cryptoRandomObjectId(ENCRYPTION_IV_LENGTH);\n    const cipher = crypto.createCipheriv(ENCRYPTION_ALGORITHM, key, initVector);\n\n    const bufferFromValue = Buffer.from(value, 'utf-8');\n    const bufferFromKey = Buffer.from(key, 'utf-8');\n    const bufferFromInitVector = Buffer.from(initVector, 'utf-8');\n    const passwordBuffer = Buffer.concat([bufferFromKey, bufferFromInitVector]);\n\n    // NOTE: Auth Tag is appended to the end of the encrypted data, it has length of 16 bytes and ensures integrity of the data.\n    const encryptedValue = Buffer.concat([cipher.update(bufferFromValue), cipher.final(), cipher.getAuthTag()]);\n    const encryptedPassword = crypto.publicEncrypt(publicKey, passwordBuffer);\n\n    return {\n        encryptedPassword: encryptedPassword.toString('base64'),\n        encryptedValue: encryptedValue.toString('base64'),\n    };\n}\n\n/**\n * It decrypts encrypted password using private key\n * and uses the password(consists of encrypted key and initial vector)\n * to decrypt the encrypted value.\n *\n * @param privateKey {KeyObject} Private key used for decryption\n * @param encryptedPassword {string} Password in Base64 encrypted using private key\n * @param encryptedValue {string} Content in Base64 encrypted using AES cipher\n * @returns {string}\n */\nexport function privateDecrypt({\n    privateKey,\n    encryptedPassword,\n    encryptedValue,\n}: DecryptOptions): string {\n    const encryptedValueBuffer = Buffer.from(encryptedValue, 'base64');\n    const encryptedPasswordBuffer = Buffer.from(encryptedPassword, 'base64');\n\n    const passwordBuffer = crypto.privateDecrypt(privateKey, encryptedPasswordBuffer);\n    if (passwordBuffer.length !== ENCRYPTION_KEY_LENGTH + ENCRYPTION_IV_LENGTH) {\n        throw new Error('privateDecrypt: Decryption failed, invalid password length!');\n    }\n\n    // Slice Auth tag from the final value cipher\n    const authTagBuffer = encryptedValueBuffer.slice(encryptedValueBuffer.length - ENCRYPTION_AUTH_TAG_LENGTH);\n    const encryptedDataBuffer = encryptedValueBuffer.slice(0, encryptedValueBuffer.length - ENCRYPTION_AUTH_TAG_LENGTH);\n\n    const encryptionKeyBuffer = passwordBuffer.slice(0, ENCRYPTION_KEY_LENGTH);\n    const initVectorBuffer = passwordBuffer.slice(ENCRYPTION_KEY_LENGTH);\n    const decipher = crypto.createDecipheriv(ENCRYPTION_ALGORITHM, encryptionKeyBuffer, initVectorBuffer);\n    decipher.setAuthTag(authTagBuffer);\n\n    return Buffer.concat([decipher.update(encryptedDataBuffer), decipher.final()]).toString('utf-8');\n}\n", "/**\n * Extract import statements from the code.\n */\nexport function separateImports(code: string): { code: string; imports: string } {\n    const lines = code.split('\\n');\n    return {\n        code: lines.filter((line) => !line.trim().startsWith('import')).join('\\n'),\n        imports: lines.filter((line) => line.trim().startsWith('import')).join('\\n'),\n    };\n}\n", "import { createHmac, timingSafeEqual } from 'node:crypto';\n\nexport enum CodeHashMetaKey {\n    VERSION = 'v',\n    USER = 'u',\n}\n\n/**\n * Allows hashing of an Actor input together with some metadata into a shareable link for the \"Run on Apify\" button.\n * Uses a common secret for checking the signatures.\n *\n * The hash consists of 3 parts separated by a dot, as in `ABC.DEF.GHI`, each being a base64url encoded string:\n *  - `meta` object with the `version` and `user` properties.\n *  - `data` data object (the one that gets encoded)\n *  - `signature` used for verification of the URL hash, computed from the `meta` and `data` objects\n */\nexport class CodeHashManager {\n    static readonly SECTION_SEPARATOR = '.';\n    static readonly VERSION = 1;\n\n    constructor(private readonly secret: string) {}\n\n    /**\n     * Encodes object (e.g. input for actor) to a string hash and uses the `secret` to sign the hash.\n     */\n    encode<T extends object>(data: T, userId: string) {\n        const meta = {\n            [CodeHashMetaKey.USER]: userId,\n            [CodeHashMetaKey.VERSION]: CodeHashManager.VERSION,\n        };\n        const metaBase64 = this.toBase64(JSON.stringify(meta));\n        const inputBase64 = this.toBase64(JSON.stringify(data));\n        const dataToSign = [metaBase64, inputBase64].join(CodeHashManager.SECTION_SEPARATOR);\n        const signature = this.generateSignature(dataToSign);\n        const signatureBase64 = this.toBase64(signature);\n        const parts = [metaBase64, inputBase64, signatureBase64];\n\n        return parts.join(CodeHashManager.SECTION_SEPARATOR);\n    }\n\n    decode(urlHash: string) {\n        const parts = urlHash.split(CodeHashManager.SECTION_SEPARATOR);\n        const dataToSign = parts.slice(0, 2).join(CodeHashManager.SECTION_SEPARATOR);\n        const meta = JSON.parse(this.fromBase64(parts[0]).toString());\n        const data = JSON.parse(this.fromBase64(parts[1]).toString());\n        const signature = this.fromBase64(parts[2]);\n        const expectedSignature = this.generateSignature(dataToSign);\n        const isSignatureValid = timingSafeEqual(signature, expectedSignature);\n\n        return {\n            data,\n            meta: {\n                userId: meta[CodeHashMetaKey.USER],\n                version: meta[CodeHashMetaKey.VERSION],\n                isSignatureValid,\n            },\n        };\n    }\n\n    private toBase64(data: string | Buffer) {\n        return Buffer.from(data as Buffer).toString('base64url');\n    }\n\n    private fromBase64(encoded: string) {\n        return Buffer.from(encoded, 'base64url');\n    }\n\n    private generateSignature(data: string) {\n        return createHmac('sha256', this.secret).update(data).digest();\n    }\n}\n", "import crypto from 'node:crypto';\n\nconst CHARSET = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');\n\n/**\n * Encodes BigInt to base62.\n */\nfunction encodeBase62(num: bigint) {\n    if (num === 0n) {\n        return CHARSET[0];\n    }\n\n    let res = '';\n    while (num > 0n) {\n        res = CHARSET[Number(num % 62n)] + res;\n        num /= 62n;\n    }\n    return res;\n}\n\n/**\n * Generates an HMAC signature and encodes it using Base62.\n * Base62 encoding reduces the signature length.\n *\n * @param secretKey {string} Secret key used for signing signatures\n * @param message {string} Message to be signed\n * @returns string\n */\nexport function createHmacSignature(secretKey: string, message: string): string {\n    const signature = crypto.createHmac('sha256', secretKey)\n        .update(message)\n        .digest('hex')\n        .substring(0, 30);\n\n    return encodeBase62(BigInt(`0x${signature}`));\n}\n", "import { createHmacSignature } from './hmac';\n\n/**\n * Creates a secure signature for a resource like a dataset or key-value store.\n * This signature is used to generate a signed URL for authenticated access, which can be expiring or permanent.\n * The signature is created using HMAC with the provided secret key and includes the resource ID, expiration time, and version.\n *\n * Note: expirationMillis is optional. If not provided, the signature will not expire.\n */\nexport function createStorageContentSignature({\n    resourceId,\n    urlSigningSecretKey,\n    expiresInMillis,\n    version = 0,\n}: {\n    resourceId: string;\n    urlSigningSecretKey: string;\n    expiresInMillis?: number;\n    version?: number;\n}) {\n    const expiresAt = expiresInMillis ? new Date().getTime() + expiresInMillis : 0;\n    const hmac = createHmacSignature(urlSigningSecretKey, `${version}.${expiresAt}.${resourceId}`);\n    return Buffer.from(`${version}.${expiresAt}.${hmac}`).toString('base64url');\n}\n"], "mappings": ";;;;;;AASA,OAAO,YAAY;AAEnB,SAAS,oBAAoB,sBAAsB;AAEnD,OAAO,OAAO,YAAY,gBAAgB;AAMnC,SAAS,qBAAqB,SAAS,IAAY;AACtD,QAAM,QAAQ;AACd,QAAM,QAAQ,OAAO,YAAY,MAAM;AACvC,MAAI,MAAM;AAEV,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAAE,WAAO,OAAO,MAAM,CAAC,IAAI,KAAK,MAAM,MAAM;AAAA,EAAG;AAC3F,SAAO;AACX;AAPgB;AAYT,SAAS,sBAAsB,KAAa,SAAS,IAAY;AACpE,SAAO,OACF,WAAW,QAAQ,EACnB,OAAO,GAAG,EACV,OAAO,QAAQ,EACf,QAAQ,cAAc,GAAG,EACzB,OAAO,GAAG,MAAM;AACzB;AAPgB;AAcT,SAAS,aAAa,aAAqB;AAC9C,gBAAc,KAAK,MAAM,WAAW;AACpC,SAAO,KAAK,MAAM,KAAK,OAAO,IAAI,WAAW;AACjD;AAHgB;AAUT,SAAS,kBAAkB,MAAqB;AACnD,MAAI,OAAO,SAAS,UAAU;AAAE,WAAO,IAAI,KAAK,KAAK,MAAM,IAAI,CAAC;AAAA,EAAG;AACnE,SAAO;AACX;AAHgB;AAShB,eAAsB,aAAa,QAA+B;AAC9D,SAAO,IAAI,QAAS,CAAC,YAAY;AAC7B,QAAI,SAAS,GAAG;AACZ,iBAAW,MAAM,QAAQ,GAAG,MAAM;AAAA,IACtC,OAAO;AACH,cAAQ;AAAA,IACZ;AAAA,EACJ,CAAE;AACN;AARsB;AAaf,SAAS,gBAAmB,OAAY,SAAY;AACvD,QAAM,QAAQ,MAAM,QAAQ,OAAO;AACnC,MAAI,SAAS,GAAG;AACZ,UAAM,OAAO,OAAO,CAAC;AACrB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAPgB;AAsBT,SAAS,aAAa,KAAkB,KAAmB;AAC9D,MAAI,OAAO,GAAG;AACd,MAAI,KAAK,gBAAgB;AAC7B;AAHgB;AAQT,SAAS,oBAAoB,KAAY,KAAkB,KAAmB,MAAoC;AACrH,MAAI,QAAQ,8BAA8B,EAAE,KAAK,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC;AAC/E,MAAI,IAAI,aAAa;AACjB,SAAK,GAAG;AACR;AAAA,EACJ;AACA,MAAI,OAAO,GAAG;AACd,MAAI,KAAK,uBAAuB;AACpC;AARgB;AAqBT,SAAS,kBAAkB,MAA0F,OAAiC;AACzJ,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY;AAEhB,QAAM,cAAc,kCAAY;AAG5B,SAAK,IAAI,QAAQ,CAAC,YAAY;AAC1B,cAAQ,KAAK,MAAM,MAAS,CAAC;AAAA,IACjC,CAAC,EAAE,QAAQ,eAAe;AAAA,EAC9B,GANoB;AAOpB,oBAAkB,kCAAY;AAC1B,QAAI,UAAW,aAAY,WAAW,aAAa,KAAK;AAAA,EAC5D,GAFkB;AAGlB,cAAY;AAEZ,SAAO;AAAA,IACH,uBAAuB;AACnB,kBAAY;AACZ,mBAAa,SAAS;AAAA,IAC1B;AAAA,EACJ;AACJ;AAvBgB;AAyBT,SAAS,oBAAoB,YAA8B;AAE9D,MAAI,cAAc,WAAW,sBAAsB;AAC/C,QAAI;AAEA,iBAAW,qBAAqB;AAAA,IACpC,SAAS,GAAG;AACR,UAAI,UAAU,GAAY,6CAA6C;AAAA,IAC3E;AAAA,EACJ;AACJ;AAVgB;AAeT,SAAS,aAAa,KAAqB;AAE9C,SAAO,OAAO,GAAG,EAAE,QAAQ,uBAAuB,MAAM;AAC5D;AAHgB;AAQT,SAAS,QAAQ,KAAa,KAAa,KAAsB,KAAK;AAEzE,QAAM,OAAO,GAAG;AAChB,MAAI,IAAI;AAER,MAAI,CAAC,MAAM,OAAO,EAAG,MAAK;AAE1B,SAAO,IAAI;AAEX,SAAO,EAAE,IAAI,KAAK;AACd,UAAM,KAAK;AAAA,EACf;AAEA,SAAO;AACX;AAdgB;AAmBT,SAAS,gBAAgB,MAAc,SAAiB,MAAc,SAAiB;AAC1F,UAAQ,OAAO,UAAU,OAAO,YAAY,UAAU;AAC1D;AAFgB;AAQhB,IAAM,8BAA8B;AAAA;AAAA,EAEhC;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAW;AAAA,EAAkB;AAAA,EAChF;AAAA,EAAgB;AAAA,EAAoB;AAAA,EAAiB;AAAA,EAAQ;AAAA,EAAiB;AAAA,EAC9E;AAAA,EAAQ;AAAA,EAAoB;AAAA,EAAa;AAAA,EAAW;AAAA,EAAW;AAAA,EAAqB;AAAA,EACpF;AAAA,EAAiB;AAAA,EAAmB;AAAA,EAAkB;AAAA,EAAmB;AAAA,EACzE;AAAA,EAAW;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAgB;AAAA,EACtF;AAAA,EAAc;AAAA,EAAa;AAAA,EAAa;AAAA,EAAY;AAAA;AAAA,EAGpD;AAAA,EAAS;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAO;AAAA,EAAW;AAAA,EAAO;AAAA,EAAW;AAAA,EAC1E;AAAA,EAAW;AAAA,EAAO;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAkB;AAAA,EAAc;AAAA,EACnF;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAC7E;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAe;AAAA,EAC/E;AAAA,EAAW;AAAA,EAAU;AAAA,EAAe;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAAS;AAAA,EAClF;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAW;AAAA,EACjF;AAAA,EAAS;AAAA,EAAY;AAAA,EAAa;AAAA,EAAU;AAAA,EAAkB;AAAA,EAAY;AAAA,EAAQ;AAAA,EAClF;AAAA,EAAM;AAAA,EAAO;AAAA,EAAO;AAAA,EAAU;AAAA,EAAS;AAAA,EAAY;AAAA,EAAS;AAAA,EAAQ;AAAA,EACpE;AAAA,EAAO;AAAA,EAAa;AAAA,EAAa;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAgB;AAAA,EACnF;AAAA,EAAS;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAU;AAAA,EAClF;AAAA,EAAqB;AAAA,EAAyB;AAAA,EAAM;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAM;AAAA,EAC/E;AAAA,EAAS;AAAA,EAAW;AAAA,EAAa;AAAA,EAAO;AAAA,EAAO;AAAA,EAAa;AAAA,EAC5D;AAAA,EAAY;AAAA,EAAyB;AAAA,EAAa;AAAA,EAAiB;AAAA,EACnE;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAa;AAAA,EACvF;AAAA,EAAM;AAAA,EAAW;AAAA,EAAiB;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAC1F;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAmB;AAAA,EAAW;AAAA,EAAoB;AAAA,EAAY;AAAA,EAAa;AAAA,EAC1F;AAAA,EAAS;AAAA,EAAW;AAAA,EAAU;AAAA,EAAS;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAY;AAAA,EACjF;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAoB;AAAA,EAAqB;AAAA,EAAgB;AAAA,EACvF;AAAA,EAAe;AAAA,EAAc;AAAA,EAAW;AAAA,EAAW;AAAA,EAAa;AAAA,EAChE;AAAA,EAAW;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAa;AAAA,EAAQ;AAAA,EACtE;AAAA,EAAO;AAAA,EAA0B;AAAA,EAAwB;AAAA,EAAa;AAAA,EAAc;AAAA,EACpF;AAAA,EAAY;AAAA;AAAA,EAGZ;AAAA,EAAS;AAAA,EAAgB;AAAA,EAAsB;AAAA,EAAoB;AAAA,EAAqB;AAAA,EACxF;AAAA,EAAsB;AAAA,EAAwB;AAAA,EAA6B;AAAA;AAAA,EAG3E;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA;AAAA,EAAK;AAAA,EAAS;AAAA,EAAU;AAAA,EAAW;AAAA,EAAY;AAAA,EAAY;AAAA,EAAc;AAAA,EAAY;AAAA,EAAM;AAAA,EAC3F;AAAA,EAAW;AAAA,EAAO;AAAA,EAAS;AAAA,EAAkB;AAAA,EAAiB;AAAA,EAAO;AAAA,EAAS;AAAA,EAC9E;AAAA,EAAa;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAS;AAAA,EAAY;AAAA,EAAa;AAAA,EAAW;AAAA,EACvF;AAAA,EAAa;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAS;AAAA,EACtF;AAAA,EAAQ;AAAA,EAAkB;AAAA,EAAU;AAAA,EAAU;AAAA,EAAoB;AAAA,EAAU;AAAA,EAAW;AAAA,EACvF;AAAA,EAAW;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EACtF;AAAA,EAAS;AAAA,EAAY;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAU;AAAA,EAAW;AAAA,EAAU;AAAA,EACpF;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAY;AAAA,EAAO;AAAA,EAAW;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAS;AAAA,EAClF;AAAA,EAAY;AAAA,EAAU;AAAA,EAAW;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAa;AAAA,EAC/E;AAAA,EAAY;AAAA,EAAe;AAAA,EAAa;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAU;AAAA,EACnF;AAAA,EAAW;AAAA,EAAW;AAAA,EAAc;AAAA,EAAc;AAAA,EAAa;AAAA,EAAW;AAAA,EAAc;AAAA,EACxF;AAAA,EAAU;AAAA,EAAO;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAW;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EACnF;AAAA,EAAW;AAAA,EAAO;AAAA,EAAS;AAAA,EAAa;AAAA,EAAc;AAAA,EAAW;AAAA,EAAS;AAAA,EAAQ;AAAA,EAClF;AAAA,EAAO;AAAA,EAAO;AAAA,EAAmB;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAiB;AAAA,EACtF;AAAA,EAAY;AAAA,EAAa;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EAAa;AAAA,EAAS;AAAA,EACrF;AAAA,EAAS;AAAA,EAAO;AAAA,EAAc;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EACtF;AAAA,EAAW;AAAA,EAAY;AAAA,EAAO;AAAA,EAAY;AAAA,EAAa;AAAA,EAAW;AAAA,EAAY;AAAA,EAAQ;AAAA,EACtF;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EACjF;AAAA,EAAa;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAW;AAAA,EAC1F;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EACzF;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAW;AAAA,EAC5E;AAAA,EAAY;AAAA,EAAS;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAK;AAAA,EAAU;AAAA,EAAQ;AAAA,EACrF;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EACpF;AAAA,EAAW;AAAA,EAAa;AAAA,EAAY;AAAA,EAAe;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EACtF;AAAA,EAAS;AAAA,EAAU;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACvF;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAS;AAAA,EAC1F;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAU;AAAA,EAAW;AAAA,EAAU;AAAA,EACjF;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAK;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EACnF;AAAA,EAAU;AAAA,EAAW;AAAA,EAAe;AAAA,EAAW;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAa;AAAA,EACrF;AAAA,EAAM;AAAA,EAAS;AAAA,EAAU;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAa;AAAA,EAAa;AAAA,EACrF;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAS;AAAA,EAAW;AAAA,EAC5F;AAAA,EAAM;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAO;AAAA,EAAW;AAAA,EAAO;AAAA,EACtF;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAS;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAiB;AAAA,EACxF;AAAA,EAAM;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EACrF;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAO;AAAA,EAAU;AAAA,EAAU;AAAA,EAAY;AAAA,EACvF;AAAA,EAAU;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAClF;AAAA,EAAS;AAAA,EAAS;AAAA,EAAY;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAc;AAAA,EAAU;AAAA,EAC3F;AAAA,EAAc;AAAA,EAAc;AAAA,EAAiB;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EACrF;AAAA,EAAW;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAc;AAAA,EAC1F;AAAA,EAAM;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAW;AAAA,EAAW;AAAA,EAAkB;AAAA,EAC3E;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EACrF;AAAA,EAAO;AAAA,EAAU;AAAA,EAAW;AAAA,EAAO;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EACnF;AAAA,EAAU;AAAA,EAAW;AAAA,EAAe;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAU;AAAA,EACrF;AAAA,EAAU;AAAA,EAAW;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAO;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EAC1F;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EACpF;AAAA,EAAU;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAU;AAAA,EAC/E;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAY;AAAA,EACtF;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAU;AAAA,EAAW;AAAA,EAC1F;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAS;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAW;AAAA,EAC1F;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAY;AAAA,EAAoB;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAS;AAAA,EACvF;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAW;AAAA,EACrF;AAAA,EAAc;AAAA,EAAc;AAAA,EAAe;AAAA,EAAa;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAiB;AAAA,EACtG;AAAA,EAAW;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAY;AAAA,EAAoB;AAAA,EAAU;AAAA,EAAU;AAAA,EACpF;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAQ;AAAA,EAC3E;AAAA,EAAoB;AAAA,EAAoB;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EACpF;AAAA,EAAS;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAAO;AAAA,EAAQ;AAAA,EACpF;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAU;AAAA,EAAY;AAAA,EACxF;AAAA,EAAM;AAAA,EAAW;AAAA,EAAS;AAAA,EAAY;AAAA,EAAe;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EACpF;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAS;AAAA,EAAW;AAAA,EAAU;AAAA,EAAO;AAAA,EAAW;AAAA,EAAS;AAAA,EACtF;AAAA,EAAW;AAAA,EAAS;AAAA,EAAW;AAAA,EAAO;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAa;AAAA,EACrF;AAAA,EAAY;AAAA,EAAW;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACtF;AAAA,EAAY;AAAA,EAAM;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACxF;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAc;AAAA,EACxF;AAAA,EAAY;AAChB;AAGA,IAAM,mBAAmB,IAAI,OAAO,KAAK,kBAAkB,IAAI,4BAA4B,KAAK,GAAG,CAAC,MAAM,GAAG;AAMtG,SAAS,oBAAoB,UAA2B;AAC3D,SAAO,CAAC,CAAC,SAAS,MAAM,cAAc,KAAK,CAAC,CAAC,SAAS,MAAM,gBAAgB;AAChF;AAFgB;AAOhB,eAAsB,sBAAyB,UAA+C;AAC1F,MAAI,CAAC,SAAS,OAAQ,QAAO,CAAC;AAC9B,QAAM,UAAe,CAAC;AAEtB,aAAW,iBAAiB,UAAU;AAClC,UAAM,UAAU,yBAAyB,WAAW,cAAc,IAAI;AACtE,YAAQ,KAAK,MAAM,OAAO;AAAA,EAC9B;AAEA,SAAO;AACX;AAVsB;AAef,SAAS,2BAA2B,UAAe,WAAmB,YAAiB,eAAuB,aAAa,OAAO;AACrI,MAAI,eAAe,aAAa,UAAa,aAAa,MAAO;AAEjE,QAAM,sBAAsB,sBAAsB,QAC5C,WAAW,KAAK,CAAC,cAAc,oBAAoB,SAAS,IAC5D,oBAAoB;AAE1B,MAAI,CAAC,oBAAqB,OAAM,IAAI,MAAM,cAAc,SAAS,4BAA4B,aAAa,EAAE;AAChH;AARgB;AAuBT,SAAS,sBAAwC,QAAW;AAC/D,SAAO,OAAO,SAAiB;AAC3B,WAAO,IAAI,QAAc,CAAC,SAAS,WAAW;AAC1C,YAAM,UAAU,wBAAC,QAAe;AAC5B,wBAAgB;AAChB,eAAO,GAAG;AAAA,MACd,GAHgB;AAIhB,YAAM,cAAc,6BAAM;AACtB,wBAAgB;AAChB,gBAAQ;AAAA,MACZ,GAHoB;AAIpB,YAAM,kBAAkB,6BAAM;AAC1B,eAAO,eAAe,SAAS,OAAO;AACtC,eAAO,eAAe,aAAa,WAAW;AAAA,MAClD,GAHwB;AAKxB,aAAO,GAAG,SAAS,OAAO;AAC1B,aAAO,GAAG,aAAa,WAAW;AAClC,aAAO,OAAO,IAAI;AAAA,IACtB,CAAC;AAAA,EACL;AACJ;AArBgB;AAuBT,SAAS,gBAAgB,UAAe,cAAwB;AACnE,MAAI,cAAc;AACd,aAAS,WAAW;AAAA,MAChB,OAAO,SAAS;AAAA,MAChB,QAAQ,IAAI,WAAW;AAAA,IAC3B,CAAC;AAAA,EACL,OAAO;AACH,aAAS,WAAW,EAAE,OAAO,SAAS,MAAM,CAAC;AAAA,EACjD;AACJ;AATgB;AAchB,eAAsB,eAAkB,SAAqB,eAAuB,eAAe,yBAAyB;AACxH,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,QAAI;AACJ,QAAI,eAAe;AAEnB,UAAM,WAAW,wBAAC,KAAmB,WAAe;AAChD,UAAI,aAAc;AAClB,mBAAa,OAAO;AACpB,qBAAe;AACf,UAAI,KAAK;AACL,eAAO,GAAG;AACV;AAAA,MACJ;AACA,cAAQ,MAAM;AAAA,IAClB,GATiB;AAWjB,YAAQ,KAAiB,CAAC,WAAc,SAAS,MAAM,MAAM,GAAG,QAAQ;AACxE,cAAU,WAAW,MAAM,SAAS,IAAI,MAAM,YAAY,CAAC,GAAG,aAAa;AAAA,EAC/E,CAAC;AACL;AAnBsB;;;ACnZtB,SAAS,oBAAoB,wBAAwB,8BAA8B;AAK5E,SAAS,kBAAkB,KAAuB;AACrD,SAAO,OAAO;AAClB;AAFgB;AAIT,SAAS,SAAS,KAAmB;AACxC,SAAO,OAAO,QAAQ,IAAI,eAAe,QAAQ,OAAO,IAAI,YAAY,aAAa,cAAc,IAAI,YAAY,SAAS,GAAG;AACnI;AAFgB;AAOT,SAAS,aAAa,MAAY,SAA0B;AAC/D,MAAI,EAAE,gBAAgB,OAAO;AAAE,WAAO;AAAA,EAAI;AAC1C,QAAM,OAAO,KAAK,YAAY;AAC9B,QAAM,QAAQ,KAAK,SAAS,IAAI;AAChC,QAAM,MAAM,KAAK,QAAQ;AACzB,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,UAAU,KAAK,WAAW;AAChC,QAAM,UAAU,KAAK,WAAW;AAChC,QAAM,SAAS,KAAK,gBAAgB;AAEpC,QAAM,MAAM,wBAAC,QAAiB,MAAM,KAAK,IAAI,GAAG,KAAK,KAAzC;AACZ,QAAM,WAAW,GAAG,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC;AAElD,QAAM,aAAa,SAAS,KAAK,KAAK,MAAM,KAAM,SAAS,MAAM,IAAI,MAAM,KAAK;AAChF,QAAM,WAAW,GAAG,IAAI,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,UAAU;AAE5E,SAAO,GAAG,QAAQ,GAAG,UAAU,MAAM,GAAG,GAAG,QAAQ;AACvD;AAjBgB;AA0BT,SAAS,SAAS,KAAa,WAAmB,SAAS,kBAA0B;AACxF,cAAY,KAAK,MAAM,SAAS;AAGhC,MAAI,OAAO,SAAS,WAAW;AAC3B,UAAM,IAAI,MAAM,+CAA+C;AAAA,EACnE;AAEA,MAAI,OAAO,QAAQ,YAAY,IAAI,SAAS,WAAW;AACnD,UAAM,IAAI,OAAO,GAAG,YAAY,OAAO,MAAM,IAAI;AAAA,EACrD;AAEA,SAAO;AACX;AAbgB;AAkBT,SAAS,iBAAiB,KAAa;AAE1C,QAAM,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI;AACjC,QAAM,IAAI,MAAM;AAChB,SAAO,GAAG,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC;AAC1C;AALgB;AAmBT,SAAS,SAAS,KAAkB;AACvC,MAAI,OAAO,QAAQ,SAAU,QAAO,CAAC;AACrC,QAAM,IAAI;AAAA,IACN,YAAY;AAAA,IACZ,KAAK;AAAA,MAAC;AAAA,MAAU;AAAA,MAAY;AAAA,MAAa;AAAA,MAAY;AAAA,MAAQ;AAAA,MAAY;AAAA,MAAQ;AAAA,MAC7E;AAAA,MAAY;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAQ;AAAA,MAAS;AAAA,IAAU;AAAA,IAChE,GAAG;AAAA,MACC,MAAM;AAAA,MACN,QAAQ;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACJ,QAAQ;AAAA;AAAA,MACR,OAAO;AAAA;AAAA,IACX;AAAA,EACJ;AAEA,QAAM,IAAI,EAAE,OAAO,EAAE,aAAa,WAAW,OAAO,EAAE,KAAK,GAAG;AAC9D,QAAM,MAA2B,CAAC;AAClC,MAAI,IAAI,EAAE,IAAI;AAEd,SAAO,IAAK,KAAI,EAAE,IAAI,CAAC,CAAC,IAAI,EAAG,CAAC,KAAK;AAErC,MAAI,EAAE,EAAE,IAAI,IAAI,CAAC;AACjB,MAAI,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,IAAS,IAAS,OAAY;AAC9D,QAAI,GAAI,KAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI;AAAA,EAChC,CAAC;AAID,MAAI,cAAc,CAAC;AACnB,MAAI,IAAI,UAAU;AAEd,QAAI,SAAS,QAAQ,EAAE,EAAE,QAAS,CAAC,IAAS,IAAS,OAAY;AAC7D,UAAI,GAAI,KAAI,YAAa,EAAE,IAAI;AAAA,IACnC,CAAS;AAAA,EACb;AAEA,SAAO;AACX;AAtCgB;AAwCT,SAAS,aAAa,KAAa,cAAwB;AAC9D,MAAI,OAAO,QAAQ,YAAY,CAAC,IAAI,QAAQ;AACxC,WAAO;AAAA,EACX;AAEA,MAAI;AAEJ,MAAI;AACA,aAAS,IAAI,IAAI,IAAI,KAAK,CAAC;AAAA,EAC/B,QAAQ;AACJ,WAAO;AAAA,EACX;AAEA,QAAM,EAAE,aAAa,IAAI;AAEzB,aAAW,OAAO,CAAC,GAAG,aAAa,KAAK,CAAC,GAAG;AACxC,QAAI,IAAI,WAAW,MAAM,GAAG;AACxB,mBAAa,OAAO,GAAG;AAAA,IAC3B;AAAA,EACJ;AAEA,eAAa,KAAK;AAElB,QAAM,WAAW,OAAO,SAAS,YAAY;AAC7C,QAAM,OAAO,OAAO,KAAK,YAAY;AACrC,QAAM,OAAO,OAAO,SAAS,QAAQ,OAAO,EAAE;AAC9C,QAAM,SAAS,aAAa,SAAS,IAAI,IAAI,YAAY,KAAK;AAC9D,QAAM,OAAO,eAAe,OAAO,OAAO;AAE1C,SAAO,GAAG,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI;AACtD;AA9BgB;AAmCT,SAAS,uBAAuB,MAAc,OAAe,MAAc,kBAA2B;AACzG,MAAI;AACJ,MAAI;AACA,gBAAY,IAAI,IAAI,IAAI;AAAA,EAC5B,QAAQ;AAAA,EAER;AACA,QAAM,cAAe,aAAc,qBAAqB,KAAK,UAAU,QAAQ;AAC/E,QAAM,iBAAiB,CAAC,oBAAqB,aAAc,UAAU,aAAa;AAElF,MAAI,eAAe,gBAAgB;AAC/B,WAAO,YAAY,IAAI,KAAK,SAAS,IAAI;AAAA,EAC7C;AAAE,MAAI,aAAa;AACf,WAAO,sDAAsD,IAAI,KAAK,SAAS,IAAI;AAAA,EACvF;AAEA,SAAO,+DAA+D,IAAI,KAAK,SAAS,IAAI;AAChG;AAjBgB;AAqBT,SAAS,yBAAyB,MAAc,OAAe;AAClE,WAAS;AACT,SAAO,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK;AACxC;AAHgB;AAST,SAAS,6BAA6B,KAA4B;AACrE,MAAI,OAAO,QAAQ,YAAY,EAAE,OAAO,GAAI,QAAO;AAEnD,QAAM,QAAQ,KAAK,MAAM,MAAM,sBAAsB;AACrD,QAAM,YAAY,MAAM;AACxB,QAAM,QAAQ,KAAK,MAAM,YAAY,sBAAsB;AAC3D,QAAM,QAAQ,YAAY;AAE1B,MAAI,MAAM,GAAG,KAAK,IAAI,KAAK;AAC3B,MAAI,QAAQ,EAAG,QAAO,IAAI,KAAK;AAE/B,SAAO;AACX;AAZgB;AAehB,IAAM,aAAa;AACnB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AACzB,IAAM,cAAc;AAEpB,IAAM,oBAAoB,IAAI,OAAO,IAAI,UAAU,KAAK,aAAa,KAAK,cAAc,MAAM,gBAAgB,KAAK,gBAAgB,IAAI;AAEvI,IAAM,aAAa,IAAI,OAAO,YAAY,GAAG;AAC7C,IAAM,gBAAgB,IAAI,OAAO,IAAI,aAAa,EAAE;AACpD,IAAM,iBAAiB,IAAI,OAAO,IAAI,cAAc,GAAG;AACvD,IAAM,mBAAmB,IAAI,OAAO,IAAI,gBAAgB,GAAG;AAC3D,IAAM,mBAAmB,IAAI,OAAO,IAAI,gBAAgB,GAAG;AAQpD,SAAS,mBAAmB,MAAc;AAU7C,MAAI,8CAA8C,KAAK,IAAI,GAAG;AAC1D,WAAO,KAAK,QAAQ,OAAO,UAAU;AACrC,WAAO,KAAK,QAAQ,OAAO,aAAa;AACxC,WAAO,KAAK,QAAQ,YAAY,cAAc;AAC9C,WAAO,KAAK,QAAQ,cAAc,gBAAgB;AAClD,WAAO,KAAK,QAAQ,eAAe,gBAAgB;AACnD,WAAO,KAAK,QAAQ,OAAO,WAAW;AAAA,EAC1C;AAEA,SAAO;AACX;AApBgB;AA4BT,SAAS,qBAAqB,MAAc;AAE/C,MAAI,kBAAkB,KAAK,IAAI,GAAG;AAC9B,WAAO,KAAK,QAAQ,YAAY,GAAG;AACnC,WAAO,KAAK,QAAQ,eAAe,GAAG;AACtC,WAAO,KAAK,QAAQ,gBAAgB,QAAQ;AAC5C,WAAO,KAAK,QAAQ,kBAAkB,UAAU;AAChD,WAAO,KAAK,QAAQ,kBAAkB,WAAW;AAAA,EACrD;AAEA,SAAO;AACX;AAXgB;AAyBT,SAAS,eAAe,KAA0B,OAAgB,eAAmE;AAGxI,MACI,QAAQ,QACL,OAAO,QAAQ,YACf,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,mBACxC,SAAS,GAAG,EACjB,QAAO;AAET,MAAI;AAEJ,MAAI,MAAM,QAAQ,GAAG,GAAG;AAEpB,aAAS,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI;AACzC,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,YAAM,MAAM,eAAe,IAAI,CAAC,GAAG,OAAO,aAAa;AACvD,UAAI,MAAO,QAAO,CAAC,IAAI;AAAA,IAC3B;AAEA,WAAO;AAAA,EACX;AAGA,WAAS,QAAQ,CAAC,IAAI;AACtB,aAAW,OAAO,KAAK;AACnB,UAAM,MAAM,eAAe,IAAI,GAAG,GAAG,OAAO,aAAa;AACzD,UAAM,CAAC,gBAAgB,cAAc,IAAI,cAAc,KAAK,GAAG;AAC/D,QAAI,QAAQ,gBAAgB;AAExB,UAAI,SAAS,QAAQ,eAAgB,QAAO,GAAG,IAAI;AAAA,IACvD,OAAO;AAEH,aAAO,cAAc,IAAI;AACzB,UAAI,CAAC,MAAO,QAAO,IAAI,GAAG;AAAA,IAC9B;AAAA,EACJ;AAEA,SAAO;AACX;AAvCgB;AAkDT,SAAS,cAAc,KAA0B,QAAQ,OAAO;AACnE,SAAO,eAAe,KAAK,OAAO,CAAC,KAAK,UAAU,CAAC,mBAAmB,GAAG,GAAG,KAAK,CAAC;AACtF;AAFgB;AAYT,SAAS,iBAAiB,KAA0B,QAAQ,OAA4B;AAC3F,SAAO,eAAe,KAAK,OAAO,CAAC,KAAK,UAAU,CAAC,qBAAqB,GAAG,GAAG,KAAK,CAAC;AACxF;AAFgB;AAWT,SAAS,cAAc,KAAmC;AAC7D,MAAI,QAAQ;AACZ,MAAI;AACA,mBAAe,KAAK,OAAO,CAAC,KAAK,UAAU;AACvC,YAAM,aAAa,mBAAmB,GAAG;AACzC,UAAI,QAAQ,YAAY;AACpB,gBAAQ;AACR,cAAM,IAAI,MAAM;AAAA,MACpB;AACA,aAAO,CAAC,KAAK,KAAK;AAAA,IACtB,CAAC;AAAA,EACL,SAAS,GAAG;AACR,QAAI,CAAC,MAAO,OAAM;AAAA,EACtB;AACA,SAAO;AACX;AAfgB;AAiBT,IAAM,gBAAN,MAAM,cAAa;AAAA,EACtB,YAAqB,MAAc;AAAd;AAAA,EAAgB;AAAA,EAErC,WAAW;AACP,WAAO,KAAK,KAAK,IAAI;AAAA,EACzB;AACJ;AAN0B;AAAnB,IAAM,eAAN;AAeA,SAAS,sBAAsB,OAA4B,UAA0D,QAAQ,GAAW;AAC3I,MAAI,YAAY,EAAE,oBAAoB,UAAW,OAAM,IAAI,MAAM,qEAAqE;AAEtI,QAAM,eAAuC,CAAC;AAE9C,QAAM,mBAAmB,wBAAC,KAAa,QAAiB;AACpD,UAAM,WAAW,SAAS,KAAK,GAAG,IAAI;AAEtC,QAAI,eAAe,SAAU,QAAO,IAAI,SAAS;AACjD,QAAI,eAAe,cAAc;AAC7B,YAAM,cAAc,yBAAyB,KAAK,OAAO,CAAC;AAC1D,mBAAa,WAAW,IAAI,IAAI,SAAS;AACzC,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX,GAXyB;AAazB,MAAI,mBAAmB,KAAK,UAAU,OAAO,kBAAkB,KAAK;AACpE,SAAO,QAAQ,YAAY,EAAE,QAAQ,CAAC,CAAC,kBAAkB,gBAAgB,MAAM;AAC3E,uBAAmB,iBAAiB,QAAQ,IAAI,gBAAgB,KAAK,gBAAgB;AAAA,EACzF,CAAC;AAED,SAAO;AACX;AAxBgB;AA8BT,SAAS,cAAc,UAAkB;AAC5C,MAAI,OAAO,aAAa,SAAU,QAAO,CAAC,MAAM,IAAI;AAEpD,QAAM,SAAS,YAAY,IAAI,KAAK,EAAE,MAAM,GAAG;AAC/C,QAAM,gBAAgB,MAAM,OAAO,CAAC,QAAQ,GAAG;AAE/C,MAAI,cAAc,WAAW,GAAG;AAC5B,WAAO,CAAC,MAAM,IAAI;AAAA,EACtB;AAEA,MAAI,cAAc,WAAW,GAAG;AAC5B,WAAO,CAAC,MAAM,cAAc,CAAC,CAAC;AAAA,EAClC;AAEA,SAAO,CAAC,MAAM,CAAC,GAAG,cAAc,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC;AACtD;AAfgB;AAoBT,SAAS,cAAc,KAAsB;AAChD,SAAO,mBAAmB,KAAK,GAAG;AACtC;AAFgB;;;ACpbhB,OAAOA,UAAS;AAIT,IAAM,kBAAN,MAAM,wBAAuB,MAAM;AAAA,EAGtC,YAAY,UAAiB,MAAiB;AAC1C,UAAM,GAAG,IAAgB;AAH7B,wBAAS;AAIL,SAAK,QAAQ;AAAA,EACjB;AACJ;AAP0C;AAAnC,IAAM,iBAAN;AAYP,eAAsB,oBAClB,SAAsH,CAAC,GAC7G;AACV,QAAM,EAAE,MAAM,kBAAkB,qBAAqB,IAAI;AAEzD,MAAI,OAAO,SAAS,YAAY;AAC5B,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC5D;AAEA,MAAI,OAAO,qBAAqB,UAAU;AACtC,UAAM,IAAI,MAAM,kDAAkD;AAAA,EACtE;AAEA,MAAI,OAAO,yBAAyB,UAAU;AAC1C,UAAM,IAAI,MAAM,sDAAsD;AAAA,EAC1E;AAEA,WAAS,IAAI,KAAK,KAAK;AACnB,QAAI;AAEJ,QAAI;AACA,aAAO,MAAM,KAAK;AAAA,IACtB,SAAS,GAAG;AACR,cAAQ;AAAA,IACZ;AAEA,QAAI,EAAE,iBAAiB,iBAAiB;AACpC,YAAM;AAAA,IACV;AAEA,QAAI,KAAK,uBAAuB,GAAG;AAC/B,YAAM,MAAM;AAAA,IAChB;AAEA,UAAM,aAAa,mBAAoB,KAAK;AAC5C,UAAM,OAAO,wBAAC,MAAc,OAAe,OAAO,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,OAAO,EAAE,GAA/E;AACb,UAAM,uBAAuB,KAAK,YAAY,aAAa,CAAC;AAE5D,QAAI,MAAM,KAAK,MAAM,uBAAuB,CAAC,GAAG;AAC5C,MAAAC,KAAI,QAAQ,gBAAgB,CAAC,kCAAkC,oBAAoB,MAAM;AAAA,QACrF,eAAe,MAAM,MAAM;AAAA,QAC3B,cAAc,QAAQ,IAAI,MAAM,OAAO,SAAS;AAAA,MACpD,CAAC;AAAA,IACL;AAEA,UAAM,aAAa,oBAAoB;AAAA,EAC3C;AACJ;AA/CsB;;;ACdf,IAAK,cAAL,kBAAKC,iBAAL;AACH,EAAAA,aAAA,kBAAe;AACf,EAAAA,aAAA,kBAAe;AACf,EAAAA,aAAA,mBAAgB;AAChB,EAAAA,aAAA,WAAQ;AACR,EAAAA,aAAA,gBAAa;AACb,EAAAA,aAAA,iBAAc;AANN,SAAAA;AAAA,GAAA;AAyCL,IAAM,iBAAN,MAAM,eAAc;AAAA,EAevB,YAA6B,SAA+B;AAA/B;AAZ7B;AAEA;AAEA;AAEA;AAEA;AAEA;AAGI,UAAM;AAAA,MACF;AAAA,MACA,cAAc;AAAA,MACd,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,6BAA6B;AAAA,MAC7B,sCAAsC;AAAA,IAC1C,IAAI;AAEJ,QAAI,CAAC,MAAM,QAAQ,MAAM,EAAG,OAAM,IAAI,MAAM,oCAAoC;AAChF,WAAO,IAAI,CAAC,UAAU,KAAK,eAAe,KAAK,CAAC;AAEhD,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,6BAA6B;AAClC,SAAK,sCAAsC;AAAA,EAC/C;AAAA,EAEA,MAAM,kBAAkB;AACpB,eAAW,SAAS,KAAK,QAAQ;AAC7B,UAAI;AACA,cAAM,eAAe,KAAK,cAAc,KAAK;AAC7C,cAAM,eAAe,cAAc,KAAK,oBAAoB,qBAAqB;AAAA,MACrF,SAAS,MAAM;AACX,cAAM,MAAM;AACZ,cAAM,IAAI,MAAM,sBAAsB,MAAM,IAAI,2BAA2B,IAAI,OAAO,GAAG;AAAA,MAC7F;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,eAAe,OAAwB;AACnC,QAAI,EAAE,MAAM,QAAQ,aAAc,OAAM,IAAI,MAAM,eAAe,MAAM,IAAI,cAAc;AACzF,QAAI,OAAO,MAAM,WAAW,SAAU,OAAM,IAAI,MAAM,uCAAuC,OAAO,MAAM,MAAM,WAAW;AAAA,EAC/H;AAAA,EAEA,MAAM,cAAc,OAAiC;AACjD,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,eAAO,KAAK,iBAAiB,KAAK;AAAA,MACtC,KAAK;AACD,eAAO,KAAK,iBAAiB,KAAK;AAAA,MACtC,KAAK;AACD,eAAO,KAAK,kBAAkB,KAAK;AAAA,MACvC,KAAK;AACD,eAAO,KAAK,eAAe,KAAK;AAAA,MACpC,KAAK;AAAA,MACL,KAAK;AACD,eAAO,KAAK,gBAAgB,KAAK;AAAA,MACrC;AACI,cAAM,IAAI,MAAM,oBAAoB;AAAA,IAC5C;AAAA,EACJ;AAAA,EAEA,MAAM,iBAAiB,EAAE,OAAO,GAAc;AAC1C,UAAM,WAAW,MAAM,OAAO,QAAQ,EAAE,MAAM,EAAE,CAAC;AACjD,QAAI,SAAS,OAAO,EAAG,OAAM,IAAI,MAAM,OAAO,SAAS,EAAE,gBAAgB;AAAA,EAC7E;AAAA,EAEA,MAAM,iBAAiB,EAAE,OAAO,GAAc;AAC1C,UAAM,WAAW,MAAM,OAAO,gBAAgB,EAAE,QAAQ;AACxD,QAAI,CAAC,MAAM,QAAQ,QAAQ,EAAG,OAAM,IAAI,MAAM,OAAO,OAAO,QAAQ,uBAAuB;AAAA,EAC/F;AAAA,EAEA,MAAM,kBAAkB,EAAE,OAAO,GAAc;AAC3C,UAAM,KAAK,qBAAqB;AAChC,UAAM,aAAa,OAAO,WAAW,KAAK,0BAA0B;AAGpE,UAAM,WAAW,WAAW;AAAA,MACxB,WAAW;AAAA,QACP,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,sCAAsC,GAAI;AAAA,MAC9E;AAAA,IACJ,CAAC;AAGD,UAAM,WAAW,UAAU;AAAA,MACvB,KAAK;AAAA,MACL,WAAW,oBAAI,KAAK;AAAA,IACxB,CAAC;AACD,UAAM,YAAY,MAAM,WAAW,QAAQ,EAAE,KAAK,GAAG,CAAC;AACtD,QAAI,CAAC,UAAW,OAAM,IAAI,MAAM,iBAAiB,EAAE,cAAc;AAAA,EACrE;AAAA,EAEA,MAAM,eAAe,EAAE,OAAO,GAAc;AACxC,UAAM,WAAW,MAAM,OAAO,KAAK;AACnC,QAAI,aAAa,OAAQ,OAAM,IAAI,MAAM,QAAQ,QAAQ,sBAAsB;AAAA,EACnF;AAAA,EAEA,MAAM,gBAAgB,EAAE,OAAO,GAAc;AACzC,UAAM,MAAM,GAAG,KAAK,WAAW,IAAI,qBAAqB,CAAC;AACzD,UAAM,WAAW;AAGjB,UAAM,OAAO,IAAI,KAAK,UAAU,MAAM,KAAK,YAAY;AACvD,UAAM,QAAQ,MAAM,OAAO,IAAI,GAAG;AAClC,QAAI,UAAU,SAAU,OAAM,IAAI,MAAM,mBAAmB,KAAK,sBAAsB,QAAQ,IAAI;AAAA,EACtG;AACJ;AAnH2B;AACvB,cADS,gBACO,eAAc;AAD3B,IAAM,gBAAN;;;AC1CP,SAAS,iBAAiB;AAuBnB,IAAM,oBAAN,MAAM,0BAAyB,UAAU;AAAA,EAAzC;AAAA;AACH,wBAAQ,gBAA8B;AAAA;AAAA,EAEtC,uBAAuB,MAAoB;AACvC,WAAO,KAAK,KAAK;AAEjB,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AAEA,QAAI;AACA,YAAM,MAAM,KAAK,MAAM,IAAI;AAC3B,WAAK,KAAK,UAAU,GAAG;AAAA,IAC3B,SAAS,GAAG;AACR,YAAM,IAAI,MAAM,mCAAmC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE;AAAA,IACrF;AAAA,EACJ;AAAA,EAEA,WAAW,OAAY,UAA0B,UAAmC;AAChF,QAAI;AACJ,QAAI,KAAK,cAAc;AACnB,gBAAU,KAAK,eAAe;AAC9B,WAAK,eAAe;AAAA,IACxB,OAAO;AACH,gBAAU;AAAA,IACd;AAEA,UAAM,QAAQ,QAAQ,SAAS,EAAE,MAAM,IAAI;AAI3C,QAAI,MAAM,MAAM,SAAS,CAAC,MAAM,IAAI;AAChC,WAAK,eAAe,MAAM,IAAI;AAAA,IAClC;AAEA,QAAI;AACA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,aAAK,uBAAuB,MAAM,CAAC,CAAC;AAAA,MACxC;AAAA,IACJ,SAAS,KAAK;AACV,eAAS,KAAc,IAAI;AAC3B;AAAA,IACJ;AAEA,aAAS,MAAM,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA,EAIA,OAAO,UAAmC;AACtC,QAAI,KAAK,cAAc;AACnB,UAAI;AACA,aAAK,uBAAuB,KAAK,YAAY;AAC7C,aAAK,eAAe;AAAA,MACxB,SAAS,KAAK;AACV,iBAAS,KAAc,IAAI;AAC3B;AAAA,MACJ;AAAA,IACJ;AACA,aAAS;AAAA,EACb;AACJ;AA7DgD;AAAzC,IAAM,mBAAN;;;ACnBP,eAAsB,qBAAqB,QAAiD;AACxF,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,SAAuB,CAAC;AAC9B,WACK,GAAG,QAAQ,CAAC,UAAU;AACnB,aAAO,KAAK,KAAK;AAAA,IACrB,CAAC,EACA,GAAG,SAAS,CAAC,MAAM,OAAO,CAAC,CAAC,EAC5B,GAAG,OAAO,MAAM;AACb,YAAM,SAAS,OAAO,OAAO,MAAM;AACnC,aAAO,QAAQ,MAAM;AAAA,IACzB,CAAC;AAAA,EACT,CAAC;AACL;AAbsB;AAmBtB,eAAsB,mBAAmB,QAAgC,UAA4C;AACjH,QAAM,SAAS,MAAM,qBAAqB,MAAM;AAChD,SAAO,OAAO,SAAS,QAAQ;AACnC;AAHsB;;;ACpBtB,IAAM,+BAAN,MAAM,qCAAoC,MAAM;AAAA,EAC5C,YAAY,SAAkB;AAC1B,UAAM,OAAO;AACb,SAAK,OAAO,KAAK,YAAY;AAC7B,QAAI,OAAO,MAAM,sBAAsB,YAAY;AAC/C,YAAM,kBAAkB,MAAM,KAAK,WAAW;AAAA,IAClD;AAAA,EACJ;AACJ;AARgD;AAAhD,IAAM,8BAAN;AAUO,IAAM,oBAAN,MAAM,0BAAyB,4BAA4B;AAAA,EAC9D,YAAY,eAAsB;AAC9B,UAAM,cAAc,OAAO;AAAA,EAC/B;AACJ;AAJkE;AAA3D,IAAM,mBAAN;AAKA,IAAM,wBAAN,MAAM,8BAA6B,MAAM;AAAA,EAC5C,YAAY,UAAmB;AAC3B,UAAM,sCAAsC,QAAQ,EAAE;AAAA,EAC1D;AACJ;AAJgD;AAAzC,IAAM,uBAAN;AAkDA,IAAM,0BAAN,MAAM,wBAAuB;AAAA,EAKhC,YAA6B,UACA,mBAAuC,MACvC,UAA+B,CAAC,GAAG;AAFnC;AACA;AACA;AAN7B,wBAAQ;AAER,wBAAS,qBAAqE,CAAC;AAK3E,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,MACH,iBACA,mBAAuC,MACvC,UAA+B,CAAC,GAChC,UAA4C,CAAC,GAC1B;AACnB,UAAM,OAAO,OAAO;AACpB,QAAI,SAAS,SAAU,OAAM,IAAI,MAAM,kBAAkB,IAAI,oBAAoB;AACjF,UAAM,WAAW,IAAI,wBAAuB,iBAAiB,kBAAkB,OAAO;AACtF,UAAM,OAAO,SAAS,OAAO;AAC7B,QAAI,QAAQ,oBAAoB;AAC5B,aAAO,SAAS,aAAa,IAAI;AAAA,IACrC;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU,gBAAqC,UAAwC,SAAS,GAAW;AAC9G,UAAM,OAAO,OAAO;AACpB,QAAI,CAAC,kBAAkB,SAAS,SAAU,OAAM,IAAI,MAAM,sBAAsB,IAAI,oBAAoB;AACxG,WAAO,sBAAsB,gBAAgB,UAAU,MAAM;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BA,OAAO,YAAY,cAAoC;AACnD,WAAO,IAAI,aAAa,YAAY;AAAA,EACxC;AAAA,EAEQ,SAAS;AACb,QAAI,eAAe;AAEnB,WAAO,MAAM;AACT,UAAI;AACA,eAAO,KAAK,MAAM,KAAK,OAAO;AAAA,MAClC,SAAS,KAAK;AACV,cAAM,WAAW,KAAK,4BAA4B,YAAY;AAE9D,YAAI,CAAC,UAAU;AACX,gBAAM,IAAI,iBAAiB,GAAY;AAAA,QAC3C;AACA,YAAI,CAAC,SAAS,gBAAgB;AAC1B,eAAK,iBAAiB,QAAQ;AAAA,QAClC;AACA,uBAAe,SAAS,iBAAiB;AAAA,MAC7C;AAAA,IACJ;AAAA,EACJ;AAAA,EAEQ,aAAa,OAAiB;AAClC,QAAI,OAAO,UAAU,UAAU;AAC3B,aAAO,KAAK,mBAAmB,KAAK;AAAA,IACxC;AAEA,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,aAAO,KAAK,kBAAkB,KAAK;AAAA,IACvC;AACA,QAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC7C,aAAO,KAAK,mBAAmB,KAAK;AAAA,IACxC;AAEA,WAAO;AAAA,EACX;AAAA,EAEQ,mBAAmB,OAAuB;AAE9C,QAAI,MAAM,MAAM,2BAA2B,GAAG;AAE1C,YAAM,eAAe,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC;AACxD,WAAK,sBAAsB,YAAY;AACvC,aAAO,KAAK,kBAAkB,YAAY;AAAA,IAC9C;AAEA,WAAO,MAAM,QAAQ,4BAA4B,CAAC,OAAO,iBAAiB;AACtE,WAAK,sBAAsB,YAAY;AACvC,YAAM,gBAAgB,KAAK,kBAAkB,YAAY;AACzD,aAAO,GAAG,aAAa;AAAA,IAC3B,CAAC;AAAA,EACL;AAAA,EAEQ,mBAAmB,OAAiD;AACxE,UAAM,SAAS,CAAC;AAChB,WAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,MAAM;AACxC,aAAO,GAAG,IAAI,KAAK,aAAa,CAAC;AAAA,IACrC,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EAEQ,kBAAkB,OAAqB;AAC3C,WAAO,MAAM,IAAI,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,EACjD;AAAA,EAEQ,4BAA4B,aAAa,GAAyB;AACtE,UAAM,iBAAiB,KAAK,QAAQ,QAAQ,MAAM,UAAU;AAC5D,UAAM,kBAAkB,KAAK,QAAQ,QAAQ,MAAM,cAAc,IAAI;AACrE,UAAM,0BAA2B,iBAAiB,MAAQ,kBAAkB;AAC5E,QAAI,CAAC,wBAAyB,QAAO;AACrC,UAAM,iBAAiB,KAAK,wBAAwB,cAAc;AAClE,WAAO,EAAE,gBAAgB,gBAAgB,gBAAgB;AAAA,EAC7D;AAAA,EAEQ,wBAAwB,gBAAiC;AAC7D,UAAM,sBAAsB,KAAK,qCAAqC,cAAc;AACpF,WAAO,sBAAsB,MAAM;AAAA,EACvC;AAAA,EAEQ,qCAAqC,OAAuB;AAChE,UAAM,iBAAiB,KAAK,QAAQ,UAAU,GAAG,KAAK;AACtD,QAAI,sBAAsB;AAC1B,aAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC5C,YAAM,OAAO,eAAe,CAAC;AAC7B,YAAM,WAAW,eAAe,IAAI,CAAC;AACrC,UAAI,SAAS,OAAO,aAAa,MAAM;AACnC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EAEQ,iBAAiB,EAAE,gBAAgB,gBAAgB,GAAwB;AAC/E,UAAM,eAAe,KAAK,QAAQ,UAAU,iBAAiB,GAAG,kBAAkB,CAAC;AACnF,SAAK,sBAAsB,YAAY;AACvC,UAAM,cAAc,KAAK,wBAAwB,YAAY;AAC7D,SAAK,kBAAkB,KAAK,EAAE,cAAc,YAAY,CAAC;AACzD,SAAK,UAAU,KAAK,QAAQ,UAAU,GAAG,cAAc,IAAI,cAAc,KAAK,QAAQ,UAAU,kBAAkB,CAAC;AAAA,EACvH;AAAA,EAEQ,sBAAsB,cAA4B;AACtD,QAAI,KAAK,qBAAqB,KAAM;AACpC,UAAM,CAAC,QAAQ,IAAI,aAAa,MAAM,GAAG;AAKzC,UAAM,kBAAkB,KAAK,iBAAiB,IAAI,QAAQ;AAE1D,QAAI,CAAC,gBAAiB,OAAM,IAAI,qBAAqB,YAAY;AAAA,EACrE;AAAA,EAEQ,kBAAkB,cAA2B;AACjD,UAAM,CAAC,UAAU,GAAG,UAAU,IAAI,aAAa,MAAM,GAAG;AACxD,UAAM,UAAU,KAAK,QAAQ,QAAQ;AACrC,UAAM,QAAQ,WAAW,OAAO,CAAC,KAAK,SAAS;AAC3C,UAAI,CAAC,OAAO,OAAO,QAAQ,SAAU,QAAO;AAC5C,aAAO,IAAI,IAAI;AAAA,IACnB,GAAG,OAAO;AACV,WAAO;AAAA,EACX;AAAA,EAEQ,wBAAwB,cAAqC;AACjE,UAAM,QAAQ,KAAK,kBAAkB,YAAY;AACjD,WAAO,QAAQ,KAAK,UAAU,KAAK,IAAI;AAAA,EAC3C;AACJ;AAtMoC;AAA7B,IAAM,yBAAN;;;ACpEP,OAAOC,aAAY;AAInB,IAAM,uBAAuB;AAC7B,IAAM,wBAAwB;AAC9B,IAAM,uBAAuB;AAC7B,IAAM,6BAA6B;AAsB5B,SAAS,cAAc,EAAE,WAAW,MAAM,GAAmB;AAChE,QAAM,MAAM,qBAAqB,qBAAqB;AACtD,QAAM,aAAa,qBAAqB,oBAAoB;AAC5D,QAAM,SAASC,QAAO,eAAe,sBAAsB,KAAK,UAAU;AAE1E,QAAM,kBAAkB,OAAO,KAAK,OAAO,OAAO;AAClD,QAAM,gBAAgB,OAAO,KAAK,KAAK,OAAO;AAC9C,QAAM,uBAAuB,OAAO,KAAK,YAAY,OAAO;AAC5D,QAAM,iBAAiB,OAAO,OAAO,CAAC,eAAe,oBAAoB,CAAC;AAG1E,QAAM,iBAAiB,OAAO,OAAO,CAAC,OAAO,OAAO,eAAe,GAAG,OAAO,MAAM,GAAG,OAAO,WAAW,CAAC,CAAC;AAC1G,QAAM,oBAAoBA,QAAO,cAAc,WAAW,cAAc;AAExE,SAAO;AAAA,IACH,mBAAmB,kBAAkB,SAAS,QAAQ;AAAA,IACtD,gBAAgB,eAAe,SAAS,QAAQ;AAAA,EACpD;AACJ;AAlBgB;AA8BT,SAAS,eAAe;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AACJ,GAA2B;AACvB,QAAM,uBAAuB,OAAO,KAAK,gBAAgB,QAAQ;AACjE,QAAM,0BAA0B,OAAO,KAAK,mBAAmB,QAAQ;AAEvE,QAAM,iBAAiBA,QAAO,eAAe,YAAY,uBAAuB;AAChF,MAAI,eAAe,WAAW,wBAAwB,sBAAsB;AACxE,UAAM,IAAI,MAAM,6DAA6D;AAAA,EACjF;AAGA,QAAM,gBAAgB,qBAAqB,MAAM,qBAAqB,SAAS,0BAA0B;AACzG,QAAM,sBAAsB,qBAAqB,MAAM,GAAG,qBAAqB,SAAS,0BAA0B;AAElH,QAAM,sBAAsB,eAAe,MAAM,GAAG,qBAAqB;AACzE,QAAM,mBAAmB,eAAe,MAAM,qBAAqB;AACnE,QAAM,WAAWA,QAAO,iBAAiB,sBAAsB,qBAAqB,gBAAgB;AACpG,WAAS,WAAW,aAAa;AAEjC,SAAO,OAAO,OAAO,CAAC,SAAS,OAAO,mBAAmB,GAAG,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,OAAO;AACnG;AAvBgB;;;ACzDT,SAAS,gBAAgB,MAAiD;AAC7E,QAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,SAAO;AAAA,IACH,MAAM,MAAM,OAAO,CAAC,SAAS,CAAC,KAAK,KAAK,EAAE,WAAW,QAAQ,CAAC,EAAE,KAAK,IAAI;AAAA,IACzE,SAAS,MAAM,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,WAAW,QAAQ,CAAC,EAAE,KAAK,IAAI;AAAA,EAC/E;AACJ;AANgB;;;ACHhB,SAAS,YAAY,uBAAuB;AAErC,IAAK,kBAAL,kBAAKC,qBAAL;AACH,EAAAA,iBAAA,aAAU;AACV,EAAAA,iBAAA,UAAO;AAFC,SAAAA;AAAA,GAAA;AAcL,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EAIzB,YAA6B,QAAgB;AAAhB;AAAA,EAAiB;AAAA;AAAA;AAAA;AAAA,EAK9C,OAAyB,MAAS,QAAgB;AAC9C,UAAM,OAAO;AAAA,MACT,CAAC,cAAoB,GAAG;AAAA,MACxB,CAAC,iBAAuB,GAAG,iBAAgB;AAAA,IAC/C;AACA,UAAM,aAAa,KAAK,SAAS,KAAK,UAAU,IAAI,CAAC;AACrD,UAAM,cAAc,KAAK,SAAS,KAAK,UAAU,IAAI,CAAC;AACtD,UAAM,aAAa,CAAC,YAAY,WAAW,EAAE,KAAK,iBAAgB,iBAAiB;AACnF,UAAM,YAAY,KAAK,kBAAkB,UAAU;AACnD,UAAM,kBAAkB,KAAK,SAAS,SAAS;AAC/C,UAAM,QAAQ,CAAC,YAAY,aAAa,eAAe;AAEvD,WAAO,MAAM,KAAK,iBAAgB,iBAAiB;AAAA,EACvD;AAAA,EAEA,OAAO,SAAiB;AACpB,UAAM,QAAQ,QAAQ,MAAM,iBAAgB,iBAAiB;AAC7D,UAAM,aAAa,MAAM,MAAM,GAAG,CAAC,EAAE,KAAK,iBAAgB,iBAAiB;AAC3E,UAAM,OAAO,KAAK,MAAM,KAAK,WAAW,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC;AAC5D,UAAM,OAAO,KAAK,MAAM,KAAK,WAAW,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC;AAC5D,UAAM,YAAY,KAAK,WAAW,MAAM,CAAC,CAAC;AAC1C,UAAM,oBAAoB,KAAK,kBAAkB,UAAU;AAC3D,UAAM,mBAAmB,gBAAgB,WAAW,iBAAiB;AAErE,WAAO;AAAA,MACH;AAAA,MACA,MAAM;AAAA,QACF,QAAQ,KAAK,cAAoB;AAAA,QACjC,SAAS,KAAK,iBAAuB;AAAA,QACrC;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EAEQ,SAAS,MAAuB;AACpC,WAAO,OAAO,KAAK,IAAc,EAAE,SAAS,WAAW;AAAA,EAC3D;AAAA,EAEQ,WAAW,SAAiB;AAChC,WAAO,OAAO,KAAK,SAAS,WAAW;AAAA,EAC3C;AAAA,EAEQ,kBAAkB,MAAc;AACpC,WAAO,WAAW,UAAU,KAAK,MAAM,EAAE,OAAO,IAAI,EAAE,OAAO;AAAA,EACjE;AACJ;AAtD6B;AACzB,cADS,kBACO,qBAAoB;AACpC,cAFS,kBAEO,WAAU;AAFvB,IAAM,kBAAN;;;AChBP,OAAOC,aAAY;AAEnB,IAAM,UAAU,iEAAiE,MAAM,EAAE;AAKzF,SAAS,aAAa,KAAa;AAC/B,MAAI,QAAQ,IAAI;AACZ,WAAO,QAAQ,CAAC;AAAA,EACpB;AAEA,MAAI,MAAM;AACV,SAAO,MAAM,IAAI;AACb,UAAM,QAAQ,OAAO,MAAM,GAAG,CAAC,IAAI;AACnC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAXS;AAqBF,SAAS,oBAAoB,WAAmB,SAAyB;AAC5E,QAAM,YAAYC,QAAO,WAAW,UAAU,SAAS,EAClD,OAAO,OAAO,EACd,OAAO,KAAK,EACZ,UAAU,GAAG,EAAE;AAEpB,SAAO,aAAa,OAAO,KAAK,SAAS,EAAE,CAAC;AAChD;AAPgB;;;ACnBT,SAAS,8BAA8B;AAAA,EAC1C;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AACd,GAKG;AACC,QAAM,YAAY,mBAAkB,oBAAI,KAAK,GAAE,QAAQ,IAAI,kBAAkB;AAC7E,QAAM,OAAO,oBAAoB,qBAAqB,GAAG,OAAO,IAAI,SAAS,IAAI,UAAU,EAAE;AAC7F,SAAO,OAAO,KAAK,GAAG,OAAO,IAAI,SAAS,IAAI,IAAI,EAAE,EAAE,SAAS,WAAW;AAC9E;AAdgB;", "names": ["log", "log", "CHECK_TYPES", "crypto", "crypto", "CodeHashMetaKey", "crypto", "crypto"]}