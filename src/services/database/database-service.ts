import { SupabaseClient } from '@supabase/supabase-js'
import { Database, Tables, TablesInsert } from './types'
import { getSupabaseAdmin, initializeSupabaseFromEnv } from './supabase-client'
import { ReviewScraperResult, BusinessScraperResult } from '../apify/types'
import { v4 as uuidv4 } from 'uuid'

export interface SaveReviewsOptions {
  sessionId?: string
  metadata?: Record<string, any>
}

export interface SaveBusinessOptions {
  sessionId?: string
  metadata?: Record<string, any>
}

export interface SaveUnifiedOptions {
  sessionId?: string
  metadata?: Record<string, any>
}

export interface ScrapeSessionData {
  sessionType: 'reviews' | 'business' | 'unified'
  urls: string[]
  parameters: Record<string, any>
  metadata?: Record<string, any>
}

/**
 * Database service for managing scraped data
 */
export class DatabaseService {
  private client: SupabaseClient<Database>

  constructor(client?: SupabaseClient<Database>) {
    if (client) {
      this.client = client
    } else {
      // Try to initialize Supabase if not already done
      try {
        this.client = getSupabaseAdmin()
      } catch (error) {
        // If factory not initialized, initialize it
        initializeSupabaseFromEnv()
        this.client = getSupabaseAdmin()
      }
    }
  }

  /**
   * Create a new scrape session
   */
  async createScrapeSession(data: ScrapeSessionData): Promise<string> {
    const sessionId = uuidv4()
    
    const { error } = await this.client
      .from('scrape_sessions')
      .insert({
        id: sessionId,
        session_type: data.sessionType,
        status: 'pending',
        urls: data.urls,
        parameters: data.parameters,
        results_count: 0,
        started_at: new Date().toISOString(),
        metadata: data.metadata,
      })

    if (error) {
      throw new Error(`Failed to create scrape session: ${error.message}`)
    }

    return sessionId
  }

  /**
   * Update scrape session status
   */
  async updateScrapeSession(
    sessionId: string,
    updates: {
      status?: 'pending' | 'running' | 'completed' | 'failed'
      resultsCount?: number
      errorMessage?: string
      completedAt?: string
      apifyRunId?: string
      apifyDatasetId?: string
      metadata?: Record<string, any>
    }
  ): Promise<void> {
    const updateData: any = {
      updated_at: new Date().toISOString(),
    }

    if (updates.status) updateData.status = updates.status
    if (updates.resultsCount !== undefined) updateData.results_count = updates.resultsCount
    if (updates.errorMessage) updateData.error_message = updates.errorMessage
    if (updates.completedAt) updateData.completed_at = updates.completedAt
    if (updates.apifyRunId) updateData.apify_run_id = updates.apifyRunId
    if (updates.apifyDatasetId) updateData.apify_dataset_id = updates.apifyDatasetId
    if (updates.metadata) updateData.metadata = updates.metadata

    const { error } = await this.client
      .from('scrape_sessions')
      .update(updateData)
      .eq('id', sessionId)

    if (error) {
      throw new Error(`Failed to update scrape session: ${error.message}`)
    }
  }

  /**
   * Save scraped reviews to database
   */
  async saveReviews(
    reviewResults: ReviewScraperResult[],
    options: SaveReviewsOptions = {}
  ): Promise<{ sessionId: string; savedCount: number }> {
    const sessionId = options.sessionId || uuidv4()
    
    // Flatten all reviews from all locations
    const allReviews: TablesInsert<'scraped_reviews'>[] = []
    
    reviewResults.forEach(location => {
      location.reviews.forEach(review => {
        allReviews.push({
          review_id: review.id,
          location_url: location.url,
          location_name: location.location?.name || null,
          author_name: review.author.name,
          author_id: review.author.id,
          author_avatar: review.author.avatar,
          rating: review.rating,
          review_text: review.review,
          language: review.language,
          likes: review.likes,
          published_at: review.publishedAt,
          updated_review_at: review.updatedAt,
          reply_text: review.reply,
          source: review.source,
          scrape_session_id: sessionId,
          metadata: {
            ...options.metadata,
            images: review.images,
            author_url: review.author.url,
          },
        })
      })
    })

    if (allReviews.length === 0) {
      return { sessionId, savedCount: 0 }
    }

    // Insert reviews in batches to avoid payload limits
    const batchSize = 100
    let savedCount = 0

    for (let i = 0; i < allReviews.length; i += batchSize) {
      const batch = allReviews.slice(i, i + batchSize)
      
      const { error } = await this.client
        .from('scraped_reviews')
        .insert(batch)

      if (error) {
        console.error(`Failed to save reviews batch ${i}-${i + batch.length}:`, error)
        throw new Error(`Failed to save reviews: ${error.message}`)
      }

      savedCount += batch.length
    }

    console.log(`💾 Saved ${savedCount} reviews to database with session ID: ${sessionId}`)
    return { sessionId, savedCount }
  }

  /**
   * Save scraped business data to database
   */
  async saveBusinesses(
    businessResults: BusinessScraperResult[],
    options: SaveBusinessOptions = {}
  ): Promise<{ sessionId: string; savedCount: number }> {
    const sessionId = options.sessionId || uuidv4()
    
    const businesses: TablesInsert<'scraped_businesses'>[] = businessResults.map(result => ({
      business_id: result.business.id,
      location_url: result.url,
      name: result.business.name,
      address: result.business.address,
      phone: result.business.phone,
      website: result.business.website,
      category: result.business.category,
      rating: result.business.rating,
      total_reviews: result.business.totalReviews,
      price_level: result.business.priceLevel,
      latitude: result.business.coordinates?.lat,
      longitude: result.business.coordinates?.lng,
      verified: result.business.verified || false,
      plus_code: result.business.plusCode,
      opening_hours: result.business.hours,
      photos: result.business.photos,
      scrape_session_id: sessionId,
      extracted_at: result.extractedAt,
      metadata: {
        ...options.metadata,
        original_url: result.business.url,
      },
    }))

    if (businesses.length === 0) {
      return { sessionId, savedCount: 0 }
    }

    const { error } = await this.client
      .from('scraped_businesses')
      .insert(businesses)

    if (error) {
      throw new Error(`Failed to save businesses: ${error.message}`)
    }

    console.log(`💾 Saved ${businesses.length} businesses to database with session ID: ${sessionId}`)
    return { sessionId, savedCount: businesses.length }
  }

  /**
   * Get scrape session by ID
   */
  async getScrapeSession(sessionId: string): Promise<Tables<'scrape_sessions'> | null> {
    const { data, error } = await this.client
      .from('scrape_sessions')
      .select('*')
      .eq('id', sessionId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to get scrape session: ${error.message}`)
    }

    return data
  }

  /**
   * Get recent scrape sessions
   */
  async getRecentSessions(limit: number = 10): Promise<Tables<'scrape_sessions'>[]> {
    const { data, error } = await this.client
      .from('scrape_sessions')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error(`Failed to get recent sessions: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get reviews by session ID
   */
  async getReviewsBySession(sessionId: string): Promise<Tables<'scraped_reviews'>[]> {
    const { data, error } = await this.client
      .from('scraped_reviews')
      .select('*')
      .eq('scrape_session_id', sessionId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to get reviews: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get businesses by session ID
   */
  async getBusinessesBySession(sessionId: string): Promise<Tables<'scraped_businesses'>[]> {
    const { data, error } = await this.client
      .from('scraped_businesses')
      .select('*')
      .eq('scrape_session_id', sessionId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to get businesses: ${error.message}`)
    }

    return data || []
  }

  /**
   * Save unified business + reviews data to database
   */
  async saveUnifiedData(
    unifiedResults: any[], // Will be UnifiedScraperResult[] when import is fixed
    options: SaveUnifiedOptions = {}
  ): Promise<{ sessionId: string; savedBusinesses: number; savedReviews: number }> {
    const sessionId = options.sessionId || uuidv4()

    let savedBusinesses = 0
    let savedReviews = 0

    for (const result of unifiedResults) {
      try {
        // 1. Save business
        const businessData = {
          google_place_id: result.business.googlePlaceId,
          name: result.business.name,
          address: result.business.address,
          phone: result.business.phone,
          website: result.business.website,
          category: result.business.category,
          rating: result.business.rating,
          total_reviews: result.business.totalReviews,
          price_level: result.business.priceLevel,
          latitude: result.business.coordinates?.lat,
          longitude: result.business.coordinates?.lng,
          verified: result.business.verified || false,
          plus_code: result.business.plusCode,
          opening_hours: result.business.openingHours || {},
          photos: result.business.photos || [],
          scrape_session_id: sessionId,
          extracted_at: result.extractedAt,
          metadata: {
            ...options.metadata,
            total_reviews_from_google: result.business.totalReviews,
            scraped_reviews_count: result.reviews.length,
          },
        }

        // Upsert business (update if exists, insert if new)
        const { data: business, error: businessError } = await this.client
          .from('businesses')
          .upsert(businessData, {
            onConflict: 'google_place_id',
            ignoreDuplicates: false
          })
          .select()
          .single()

        if (businessError) {
          console.error(`Failed to save business ${result.business.name}:`, businessError)
          continue
        }

        savedBusinesses++
        console.log(`💾 Saved business: ${result.business.name}`)

        // 2. Save reviews for this business
        if (result.reviews && result.reviews.length > 0) {
          const reviewsData = result.reviews.map((review: any) => ({
            business_id: business.id,
            google_review_id: review.googleReviewId,
            author_name: review.authorName,
            author_id: review.authorId,
            author_avatar: review.authorAvatar,
            author_url: review.authorUrl,
            rating: review.rating,
            review_text: review.reviewText,
            language: review.language,
            likes: review.likes,
            published_at: review.publishedAt && review.publishedAt !== '' ? review.publishedAt : null,
            updated_review_at: review.updatedAt && review.updatedAt !== '' ? review.updatedAt : null,
            reply_text: review.replyText,
            reply_published_at: review.replyPublishedAt && review.replyPublishedAt !== '' ? review.replyPublishedAt : null,
            scrape_session_id: sessionId,
            metadata: {
              ...options.metadata,
              images: review.images || [],
            },
          }))

          // Insert reviews in batches
          const batchSize = 50
          for (let i = 0; i < reviewsData.length; i += batchSize) {
            const batch = reviewsData.slice(i, i + batchSize)

            const { error: reviewsError } = await this.client
              .from('business_reviews')
              .upsert(batch, {
                onConflict: 'google_review_id,business_id',
                ignoreDuplicates: true
              })

            if (reviewsError) {
              console.error(`Failed to save reviews batch for ${result.business.name}:`, reviewsError)
            } else {
              savedReviews += batch.length
            }
          }

          console.log(`💾 Saved ${result.reviews.length} reviews for ${result.business.name}`)
        }

      } catch (error) {
        console.error(`Error processing business ${result.business.name}:`, error)
      }
    }

    console.log(`💾 Unified save complete: ${savedBusinesses} businesses, ${savedReviews} reviews`)
    return { sessionId, savedBusinesses, savedReviews }
  }

  /**
   * Get businesses with their reviews
   */
  async getBusinessesWithReviews(sessionId?: string, limit: number = 10) {
    let query = this.client
      .from('businesses')
      .select(`
        *,
        business_reviews (*)
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (sessionId) {
      query = query.eq('scrape_session_id', sessionId)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to get businesses with reviews: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get database statistics (updated for unified tables)
   */
  async getStats(): Promise<{
    totalSessions: number
    totalReviews: number
    totalBusinesses: number
    totalUnifiedBusinesses: number
    totalUnifiedReviews: number
    recentSessions: number
  }> {
    const [
      sessionsResult,
      reviewsResult,
      businessesResult,
      unifiedBusinessesResult,
      unifiedReviewsResult,
      recentSessionsResult
    ] = await Promise.all([
      this.client.from('scrape_sessions').select('count'),
      this.client.from('scraped_reviews').select('count'),
      this.client.from('scraped_businesses').select('count'),
      this.client.from('businesses').select('count'),
      this.client.from('business_reviews').select('count'),
      this.client.from('scrape_sessions').select('count').gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()),
    ])

    return {
      totalSessions: sessionsResult.count || 0,
      totalReviews: reviewsResult.count || 0,
      totalBusinesses: businessesResult.count || 0,
      totalUnifiedBusinesses: unifiedBusinessesResult.count || 0,
      totalUnifiedReviews: unifiedReviewsResult.count || 0,
      recentSessions: recentSessionsResult.count || 0,
    }
  }
}
