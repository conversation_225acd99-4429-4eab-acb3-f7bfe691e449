import { SupabaseClient } from '@supabase/supabase-js'
import { Database, Tables, TablesInsert } from './types'
import { getSupabaseAdmin, initializeSupabaseFromEnv } from './supabase-client'
import { UnifiedScraperResult } from '../apify/scrapers/unified-scraper'
import { v4 as uuidv4 } from 'uuid'

export interface SaveUnifiedOptions {
  scraperRunId?: string
  metadata?: Record<string, any>
}

export interface ScraperRunData {
  sourceId: string
  actorName: string
  parameters: Record<string, any>
  metadata?: Record<string, any>
}

export interface BusinessDeduplicationResult {
  isNewBusiness: boolean
  businessId: string
  existingBusiness?: Tables<'businesses'>
  similarBusinesses?: Array<{
    business_id: string
    name: string
    address: string
    similarity_score: number
    distance_km?: number
  }>
}

/**
 * Multi-Source Database Service for managing businesses and reviews from multiple sources
 */
export class MultiSourceDatabaseService {
  private client: SupabaseClient<Database>

  constructor(client?: SupabaseClient<Database>) {
    if (client) {
      this.client = client
    } else {
      // Try to initialize Supabase if not already done
      try {
        this.client = getSupabaseAdmin()
      } catch (error) {
        // If factory not initialized, initialize it
        initializeSupabaseFromEnv()
        this.client = getSupabaseAdmin()
      }
    }
  }

  /**
   * Get source by name (e.g., 'google', 'yelp')
   */
  async getSourceByName(sourceName: string): Promise<Tables<'sources'> | null> {
    const { data, error } = await this.client
      .from('sources')
      .select('*')
      .eq('name', sourceName)
      .single()

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return null
      }
      throw new Error(`Failed to get source: ${error.message}`)
    }

    return data
  }

  /**
   * Create a new scraper run
   */
  async createScraperRun(data: ScraperRunData): Promise<string> {
    const scraperRunId = uuidv4()
    
    const { error } = await this.client
      .from('scraper_runs')
      .insert({
        id: scraperRunId,
        source_id: data.sourceId,
        actor_name: data.actorName,
        status: 'pending',
        started_at: new Date().toISOString(),
        parameters: data.parameters,
        metadata: data.metadata,
      })

    if (error) {
      throw new Error(`Failed to create scraper run: ${error.message}`)
    }

    return scraperRunId
  }

  /**
   * Update scraper run status and metadata
   */
  async updateScraperRun(
    scraperRunId: string,
    updates: {
      status?: 'pending' | 'running' | 'completed' | 'failed'
      completedAt?: string
      apifyRunId?: string
      apifyDatasetId?: string
      storagePath?: string
      durationSeconds?: number
      cost?: number
      metadata?: Record<string, any>
    }
  ): Promise<void> {
    const updateData: any = {
      updated_at: new Date().toISOString(),
    }

    if (updates.status) updateData.status = updates.status
    if (updates.completedAt) updateData.completed_at = updates.completedAt
    if (updates.apifyRunId) updateData.apify_run_id = updates.apifyRunId
    if (updates.apifyDatasetId) updateData.apify_dataset_id = updates.apifyDatasetId
    if (updates.storagePath) updateData.storage_path = updates.storagePath
    if (updates.durationSeconds !== undefined) updateData.duration_seconds = updates.durationSeconds
    if (updates.cost !== undefined) updateData.cost = updates.cost
    if (updates.metadata) updateData.metadata = updates.metadata

    const { error } = await this.client
      .from('scraper_runs')
      .update(updateData)
      .eq('id', scraperRunId)

    if (error) {
      throw new Error(`Failed to update scraper run: ${error.message}`)
    }
  }

  /**
   * Find potential duplicate businesses using the database function
   */
  async findPotentialDuplicates(
    businessName: string,
    businessAddress: string,
    latitude?: number,
    longitude?: number,
    distanceThresholdKm: number = 0.1
  ): Promise<Array<{
    business_id: string
    name: string
    address: string
    similarity_score: number
    distance_km?: number
  }>> {
    const { data, error } = await this.client
      .rpc('find_potential_duplicates', {
        business_name: businessName,
        business_address: businessAddress,
        latitude_param: latitude,
        longitude_param: longitude,
        distance_threshold_km: distanceThresholdKm
      })

    if (error) {
      throw new Error(`Failed to find potential duplicates: ${error.message}`)
    }

    return data || []
  }

  /**
   * Check if a business already exists for a specific source
   */
  async getBusinessBySourceId(sourceId: string, sourceBusinessId: string): Promise<{
    business: Tables<'businesses'>
    businessSource: Tables<'business_sources'>
  } | null> {
    const { data, error } = await this.client
      .from('business_sources')
      .select(`
        *,
        businesses (*)
      `)
      .eq('source_id', sourceId)
      .eq('source_business_id', sourceBusinessId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return null
      }
      throw new Error(`Failed to get business by source ID: ${error.message}`)
    }

    return {
      business: (data as any).businesses,
      businessSource: data
    }
  }

  /**
   * Deduplicate business - find existing or create new
   */
  async deduplicateBusiness(
    businessData: {
      name: string
      address: string
      latitude?: number
      longitude?: number
    },
    sourceId: string,
    sourceBusinessId: string
  ): Promise<BusinessDeduplicationResult> {
    // First check if this exact source business ID already exists
    const existingSourceBusiness = await this.getBusinessBySourceId(sourceId, sourceBusinessId)
    
    if (existingSourceBusiness) {
      return {
        isNewBusiness: false,
        businessId: existingSourceBusiness.business.id,
        existingBusiness: existingSourceBusiness.business
      }
    }

    // Look for potential duplicates based on name, address, and location
    const potentialDuplicates = await this.findPotentialDuplicates(
      businessData.name,
      businessData.address,
      businessData.latitude,
      businessData.longitude
    )

    // If we find a high-confidence match (similarity > 0.8), use it
    const highConfidenceMatch = potentialDuplicates.find(dup => dup.similarity_score > 0.8)
    
    if (highConfidenceMatch) {
      const { data: existingBusiness, error } = await this.client
        .from('businesses')
        .select('*')
        .eq('id', highConfidenceMatch.business_id)
        .single()

      if (error) {
        throw new Error(`Failed to get existing business: ${error.message}`)
      }

      return {
        isNewBusiness: false,
        businessId: highConfidenceMatch.business_id,
        existingBusiness: existingBusiness,
        similarBusinesses: potentialDuplicates
      }
    }

    // No high-confidence match found, this is a new business
    return {
      isNewBusiness: true,
      businessId: uuidv4(), // Generate new ID for the business
      similarBusinesses: potentialDuplicates.length > 0 ? potentialDuplicates : undefined
    }
  }

  /**
   * Create or update a business
   */
  async upsertBusiness(
    businessData: {
      id?: string
      name: string
      address: string
      phone?: string
      website?: string
      category?: string
      rating?: number
      totalReviews?: number
      latitude?: number
      longitude?: number
      metadata?: Record<string, any>
    },
    scraperRunId: string
  ): Promise<{ business: Tables<'businesses'>; versionIncremented: boolean }> {
    const businessId = businessData.id || uuidv4()

    // Check if business already exists
    const { data: existingBusiness } = await this.client
      .from('businesses')
      .select('*')
      .eq('id', businessId)
      .single()

    const isUpdate = !!existingBusiness
    const currentVersion = existingBusiness?.version || 0
    const newVersion = currentVersion + 1

    const businessInsert: TablesInsert<'businesses'> = {
      id: businessId,
      name: businessData.name,
      address: businessData.address,
      phone: businessData.phone,
      website: businessData.website,
      category: businessData.category,
      rating: businessData.rating || 0,
      total_reviews: businessData.totalReviews || 0,
      latitude: businessData.latitude,
      longitude: businessData.longitude,
      version: newVersion,
      metadata: businessData.metadata || {}
    }

    const { data: business, error } = await this.client
      .from('businesses')
      .upsert(businessInsert, {
        onConflict: 'id',
        ignoreDuplicates: false
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to upsert business: ${error.message}`)
    }

    // Track the version change in business_scraper_runs
    if (isUpdate) {
      await this.client
        .from('business_scraper_runs')
        .insert({
          business_id: businessId,
          scraper_run_id: scraperRunId,
          version_before: currentVersion,
          version_after: newVersion,
          changes_made: {
            updated_fields: Object.keys(businessData),
            timestamp: new Date().toISOString()
          }
        })
    }

    return {
      business,
      versionIncremented: isUpdate
    }
  }

  /**
   * Create or update business source mapping
   */
  async upsertBusinessSource(
    businessId: string,
    sourceId: string,
    sourceBusinessId: string,
    sourceUrl?: string,
    sourceMetadata?: Record<string, any>
  ): Promise<Tables<'business_sources'>> {
    const businessSourceData: TablesInsert<'business_sources'> = {
      business_id: businessId,
      source_id: sourceId,
      source_business_id: sourceBusinessId,
      source_url: sourceUrl,
      source_metadata: sourceMetadata || {},
      last_scraped_at: new Date().toISOString()
    }

    const { data, error } = await this.client
      .from('business_sources')
      .upsert(businessSourceData, {
        onConflict: 'source_id,source_business_id',
        ignoreDuplicates: false
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to upsert business source: ${error.message}`)
    }

    return data
  }

  /**
   * Save reviews for a business
   */
  async saveReviews(
    businessId: string,
    sourceId: string,
    reviews: Array<{
      sourceReviewId: string
      authorName: string
      authorId?: string
      authorAvatar?: string
      rating: number
      reviewText?: string
      language?: string
      likes?: number
      publishedAt: string
      updatedReviewAt?: string
      replyText?: string
      replyPublishedAt?: string
      metadata?: Record<string, any>
    }>
  ): Promise<number> {
    if (reviews.length === 0) return 0

    const reviewsData: TablesInsert<'reviews'>[] = reviews.map(review => ({
      business_id: businessId,
      source_id: sourceId,
      source_review_id: review.sourceReviewId,
      author_name: review.authorName,
      author_id: review.authorId,
      author_avatar: review.authorAvatar,
      rating: review.rating,
      review_text: review.reviewText,
      language: review.language,
      likes: review.likes || 0,
      published_at: review.publishedAt,
      updated_review_at: review.updatedReviewAt,
      reply_text: review.replyText,
      reply_published_at: review.replyPublishedAt,
      metadata: review.metadata || {}
    }))

    // Insert reviews in batches to avoid payload limits
    const batchSize = 50
    let savedCount = 0

    for (let i = 0; i < reviewsData.length; i += batchSize) {
      const batch = reviewsData.slice(i, i + batchSize)

      const { error } = await this.client
        .from('reviews')
        .upsert(batch, {
          onConflict: 'source_id,source_review_id,business_id',
          ignoreDuplicates: true
        })

      if (error) {
        console.error(`Failed to save reviews batch ${i}-${i + batch.length}:`, error)
        throw new Error(`Failed to save reviews: ${error.message}`)
      }

      savedCount += batch.length
    }

    return savedCount
  }

  /**
   * Save unified business + reviews data from scraping (main method)
   */
  async saveUnifiedData(
    sourceName: string,
    unifiedResults: UnifiedScraperResult[],
    options: SaveUnifiedOptions = {}
  ): Promise<{
    scraperRunId: string
    savedBusinesses: number
    savedReviews: number
    newBusinesses: number
    updatedBusinesses: number
  }> {
    // Get source
    const source = await this.getSourceByName(sourceName)
    if (!source) {
      throw new Error(`Source '${sourceName}' not found`)
    }

    let savedBusinesses = 0
    let savedReviews = 0
    let newBusinesses = 0
    let updatedBusinesses = 0

    const scraperRunId = options.scraperRunId || uuidv4()

    console.log(`💾 Starting unified save for ${unifiedResults.length} businesses from ${sourceName}`)

    for (const result of unifiedResults) {
      try {
        // Deduplicate business
        const deduplicationResult = await this.deduplicateBusiness(
          {
            name: result.business.name,
            address: result.business.address,
            latitude: result.business.coordinates.lat,
            longitude: result.business.coordinates.lng
          },
          source.id,
          result.business.googlePlaceId
        )

        // Upsert business
        const { business, versionIncremented } = await this.upsertBusiness(
          {
            id: deduplicationResult.businessId,
            name: result.business.name,
            address: result.business.address,
            phone: result.business.phone,
            website: result.business.website,
            category: result.business.category,
            rating: result.business.rating,
            totalReviews: result.business.totalReviews,
            latitude: result.business.coordinates.lat,
            longitude: result.business.coordinates.lng,
            metadata: {
              verified: result.business.verified,
              plusCode: result.business.plusCode,
              openingHours: result.business.openingHours,
              photos: result.business.photos,
              priceLevel: result.business.priceLevel,
              extractedAt: result.extractedAt,
              ...options.metadata
            }
          },
          scraperRunId
        )

        // Create/update business source mapping
        await this.upsertBusinessSource(
          business.id,
          source.id,
          result.business.googlePlaceId,
          undefined, // We don't have the original URL in the current data
          {
            verified: result.business.verified,
            plusCode: result.business.plusCode,
            openingHours: result.business.openingHours,
            photos: result.business.photos,
            priceLevel: result.business.priceLevel
          }
        )

        savedBusinesses++
        if (deduplicationResult.isNewBusiness) {
          newBusinesses++
        } else if (versionIncremented) {
          updatedBusinesses++
        }

        console.log(`💾 Saved business: ${result.business.name} (${deduplicationResult.isNewBusiness ? 'new' : 'existing'})`)

        // Save reviews if any
        if (result.reviews && result.reviews.length > 0) {
          const reviewsSaved = await this.saveReviews(
            business.id,
            source.id,
            result.reviews.map(review => ({
              sourceReviewId: review.googleReviewId,
              authorName: review.authorName,
              authorId: review.authorId,
              authorAvatar: review.authorAvatar,
              rating: review.rating,
              reviewText: review.reviewText,
              language: review.language,
              likes: review.likes,
              publishedAt: review.publishedAt,
              updatedReviewAt: review.updatedAt,
              replyText: review.replyText,
              replyPublishedAt: review.replyPublishedAt,
              metadata: {
                images: review.images || [],
                ...options.metadata
              }
            }))
          )

          savedReviews += reviewsSaved
          console.log(`💾 Saved ${reviewsSaved} reviews for ${result.business.name}`)
        }

      } catch (error) {
        console.error(`Error processing business ${result.business.name}:`, error)
        // Continue with other businesses instead of failing completely
      }
    }

    console.log(`💾 Unified save complete: ${savedBusinesses} businesses (${newBusinesses} new, ${updatedBusinesses} updated), ${savedReviews} reviews`)

    return {
      scraperRunId,
      savedBusinesses,
      savedReviews,
      newBusinesses,
      updatedBusinesses
    }
  }

  /**
   * Get businesses with their sources and reviews
   */
  async getBusinessesWithSourcesAndReviews(limit: number = 10): Promise<any[]> {
    const { data, error } = await this.client
      .from('businesses')
      .select(`
        *,
        business_sources (
          *,
          sources (*)
        ),
        reviews (*)
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error(`Failed to get businesses with sources and reviews: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get scraper run details
   */
  async getScraperRun(scraperRunId: string): Promise<Tables<'scraper_runs'> | null> {
    const { data, error } = await this.client
      .from('scraper_runs')
      .select('*')
      .eq('id', scraperRunId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return null
      }
      throw new Error(`Failed to get scraper run: ${error.message}`)
    }

    return data
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<{
    totalSources: number
    totalBusinesses: number
    totalReviews: number
    totalScraperRuns: number
  }> {
    const [
      sourcesResult,
      businessesResult,
      reviewsResult,
      scraperRunsResult
    ] = await Promise.all([
      this.client.from('sources').select('count'),
      this.client.from('businesses').select('count'),
      this.client.from('reviews').select('count'),
      this.client.from('scraper_runs').select('count')
    ])

    return {
      totalSources: (sourcesResult.data as any)?.[0]?.count || 0,
      totalBusinesses: (businessesResult.data as any)?.[0]?.count || 0,
      totalReviews: (reviewsResult.data as any)?.[0]?.count || 0,
      totalScraperRuns: (scraperRunsResult.data as any)?.[0]?.count || 0
    }
  }
}
