import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { Database } from './types'

export interface SupabaseConfig {
  url: string
  anonKey?: string
  serviceRoleKey?: string
}

/**
 * Supabase client factory for different access levels
 */
export class SupabaseClientFactory {
  private config: SupabaseConfig
  private _anonClient?: SupabaseClient<Database>
  private _adminClient?: SupabaseClient<Database>

  constructor(config: SupabaseConfig) {
    this.config = config
    this.validateConfig()
  }

  /**
   * Get client for user-facing operations (respects RLS)
   */
  getAnonClient(): SupabaseClient<Database> {
    if (!this._anonClient) {
      if (!this.config.anonKey) {
        throw new Error('Anonymous key is required for anon client')
      }
      this._anonClient = createClient<Database>(
        this.config.url,
        this.config.anonKey
      )
    }
    return this._anonClient
  }

  /**
   * Get client for admin operations (bypasses RLS)
   */
  getAdminClient(): SupabaseClient<Database> {
    if (!this._adminClient) {
      if (!this.config.serviceRoleKey) {
        throw new Error('Service role key is required for admin client')
      }
      this._adminClient = createClient<Database>(
        this.config.url,
        this.config.serviceRoleKey,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      )
    }
    return this._adminClient
  }

  /**
   * Test connection to Supabase
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const client = this.getAnonClient()
      const { error } = await client.from('scrape_sessions').select('count').limit(1)
      
      if (error) {
        return {
          success: false,
          message: `Connection failed: ${error.message}`
        }
      }

      return {
        success: true,
        message: 'Successfully connected to Supabase'
      }
    } catch (error) {
      return {
        success: false,
        message: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  private validateConfig(): void {
    if (!this.config.url) {
      throw new Error('Supabase URL is required')
    }
    
    if (!this.config.anonKey && !this.config.serviceRoleKey) {
      throw new Error('At least one of anonKey or serviceRoleKey is required')
    }
  }
}

/**
 * Default factory instance
 */
let defaultFactory: SupabaseClientFactory | null = null

/**
 * Initialize the default Supabase factory
 */
export function initializeSupabase(config: SupabaseConfig): SupabaseClientFactory {
  defaultFactory = new SupabaseClientFactory(config)
  return defaultFactory
}

/**
 * Get the default Supabase factory
 */
export function getSupabaseFactory(): SupabaseClientFactory {
  if (!defaultFactory) {
    throw new Error('Supabase factory not initialized. Call initializeSupabase() first.')
  }
  return defaultFactory
}

/**
 * Get admin client (bypasses RLS)
 */
export function getSupabaseAdmin(): SupabaseClient<Database> {
  return getSupabaseFactory().getAdminClient()
}

/**
 * Get anon client (respects RLS)
 */
export function getSupabaseAnon(): SupabaseClient<Database> {
  return getSupabaseFactory().getAnonClient()
}

/**
 * Initialize Supabase from environment variables
 */
export function initializeSupabaseFromEnv(): SupabaseClientFactory {
  const url = process.env.SUPABASE_URL
  const anonKey = process.env.SUPABASE_ANON_KEY
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!url) {
    throw new Error('SUPABASE_URL environment variable is required')
  }

  return initializeSupabase({
    url,
    anonKey,
    serviceRoleKey,
  })
}
