// Database types for Supabase integration

export interface Database {
  public: {
    Tables: {
      scraped_reviews: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          review_id: string
          location_url: string
          location_name?: string
          author_name: string
          author_id: string
          author_avatar?: string
          rating: number
          review_text?: string
          language?: string
          likes: number
          published_at: string
          updated_review_at: string
          reply_text?: string
          source: string
          scrape_session_id: string
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          review_id: string
          location_url: string
          location_name?: string
          author_name: string
          author_id: string
          author_avatar?: string
          rating: number
          review_text?: string
          language?: string
          likes: number
          published_at: string
          updated_review_at: string
          reply_text?: string
          source: string
          scrape_session_id: string
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          review_id?: string
          location_url?: string
          location_name?: string
          author_name?: string
          author_id?: string
          author_avatar?: string
          rating?: number
          review_text?: string
          language?: string
          likes?: number
          published_at?: string
          updated_review_at?: string
          reply_text?: string
          source?: string
          scrape_session_id?: string
          metadata?: Record<string, any>
        }
      }
      scraped_businesses: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          business_id: string
          location_url: string
          name: string
          address: string
          phone?: string
          website?: string
          category: string
          rating: number
          total_reviews: number
          price_level?: string
          latitude?: number
          longitude?: number
          verified: boolean
          plus_code?: string
          opening_hours?: Record<string, string>
          photos?: Array<{ url: string; caption?: string }>
          scrape_session_id: string
          extracted_at: string
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          business_id: string
          location_url: string
          name: string
          address: string
          phone?: string
          website?: string
          category: string
          rating: number
          total_reviews: number
          price_level?: string
          latitude?: number
          longitude?: number
          verified?: boolean
          plus_code?: string
          opening_hours?: Record<string, string>
          photos?: Array<{ url: string; caption?: string }>
          scrape_session_id: string
          extracted_at: string
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          business_id?: string
          location_url?: string
          name?: string
          address?: string
          phone?: string
          website?: string
          category?: string
          rating?: number
          total_reviews?: number
          price_level?: string
          latitude?: number
          longitude?: number
          verified?: boolean
          plus_code?: string
          opening_hours?: Record<string, string>
          photos?: Array<{ url: string; caption?: string }>
          scrape_session_id?: string
          extracted_at?: string
          metadata?: Record<string, any>
        }
      }
      businesses: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          google_place_id: string
          name: string
          address: string
          phone?: string
          website?: string
          category: string
          rating: number
          total_reviews: number
          price_level?: string
          latitude?: number
          longitude?: number
          verified: boolean
          plus_code?: string
          opening_hours?: Record<string, string>
          photos?: Array<{ url: string; caption?: string }>
          scrape_session_id: string
          extracted_at: string
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          google_place_id: string
          name: string
          address: string
          phone?: string
          website?: string
          category: string
          rating: number
          total_reviews: number
          price_level?: string
          latitude?: number
          longitude?: number
          verified?: boolean
          plus_code?: string
          opening_hours?: Record<string, string>
          photos?: Array<{ url: string; caption?: string }>
          scrape_session_id: string
          extracted_at: string
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          google_place_id?: string
          name?: string
          address?: string
          phone?: string
          website?: string
          category?: string
          rating?: number
          total_reviews?: number
          price_level?: string
          latitude?: number
          longitude?: number
          verified?: boolean
          plus_code?: string
          opening_hours?: Record<string, string>
          photos?: Array<{ url: string; caption?: string }>
          scrape_session_id?: string
          extracted_at?: string
          metadata?: Record<string, any>
        }
      }
      business_reviews: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          business_id: string
          google_review_id: string
          author_name: string
          author_id?: string
          author_avatar?: string
          author_url?: string
          rating: number
          review_text?: string
          language?: string
          likes: number
          published_at: string
          updated_review_at?: string
          reply_text?: string
          reply_published_at?: string
          images?: Array<{ url: string; caption?: string }>
          scrape_session_id: string
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          business_id: string
          google_review_id: string
          author_name: string
          author_id?: string
          author_avatar?: string
          author_url?: string
          rating: number
          review_text?: string
          language?: string
          likes?: number
          published_at: string
          updated_review_at?: string
          reply_text?: string
          reply_published_at?: string
          images?: Array<{ url: string; caption?: string }>
          scrape_session_id: string
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          business_id?: string
          google_review_id?: string
          author_name?: string
          author_id?: string
          author_avatar?: string
          author_url?: string
          rating?: number
          review_text?: string
          language?: string
          likes?: number
          published_at?: string
          updated_review_at?: string
          reply_text?: string
          reply_published_at?: string
          images?: Array<{ url: string; caption?: string }>
          scrape_session_id?: string
          metadata?: Record<string, any>
        }
      }
      scrape_sessions: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          session_type: 'reviews' | 'business' | 'unified'
          status: 'pending' | 'running' | 'completed' | 'failed'
          urls: string[]
          parameters: Record<string, any>
          results_count: number
          error_message?: string
          started_at?: string
          completed_at?: string
          apify_run_id?: string
          apify_dataset_id?: string
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          session_type: 'reviews' | 'business' | 'unified'
          status?: 'pending' | 'running' | 'completed' | 'failed'
          urls: string[]
          parameters: Record<string, any>
          results_count?: number
          error_message?: string
          started_at?: string
          completed_at?: string
          apify_run_id?: string
          apify_dataset_id?: string
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          session_type?: 'reviews' | 'business' | 'unified'
          status?: 'pending' | 'running' | 'completed' | 'failed'
          urls?: string[]
          parameters?: Record<string, any>
          results_count?: number
          error_message?: string
          started_at?: string
          completed_at?: string
          apify_run_id?: string
          apify_dataset_id?: string
          metadata?: Record<string, any>
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
