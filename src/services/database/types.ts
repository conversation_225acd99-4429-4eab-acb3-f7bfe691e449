// Database types for Multi-Source Business & Review Tracking

export interface Database {
  public: {
    Tables: {
      sources: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          display_name: string
          base_url?: string
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          display_name: string
          base_url?: string
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          display_name?: string
          base_url?: string
          metadata?: Record<string, any>
        }
      }
      businesses: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          address: string
          phone?: string
          website?: string
          category?: string
          rating: number
          total_reviews: number
          latitude?: number
          longitude?: number
          version: number
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          address: string
          phone?: string
          website?: string
          category?: string
          rating?: number
          total_reviews?: number
          latitude?: number
          longitude?: number
          version?: number
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          address?: string
          phone?: string
          website?: string
          category?: string
          rating?: number
          total_reviews?: number
          latitude?: number
          longitude?: number
          version?: number
          metadata?: Record<string, any>
        }
      }
      business_sources: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          business_id: string
          source_id: string
          source_business_id: string
          source_url?: string
          source_metadata?: Record<string, any>
          last_scraped_at?: string
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          business_id: string
          source_id: string
          source_business_id: string
          source_url?: string
          source_metadata?: Record<string, any>
          last_scraped_at?: string
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          business_id?: string
          source_id?: string
          source_business_id?: string
          source_url?: string
          source_metadata?: Record<string, any>
          last_scraped_at?: string
        }
      }
      reviews: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          business_id: string
          source_id: string
          source_review_id: string
          author_name: string
          author_id?: string
          author_avatar?: string
          rating: number
          review_text?: string
          language?: string
          likes: number
          published_at: string
          updated_review_at?: string
          reply_text?: string
          reply_published_at?: string
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          business_id: string
          source_id: string
          source_review_id: string
          author_name: string
          author_id?: string
          author_avatar?: string
          rating: number
          review_text?: string
          language?: string
          likes?: number
          published_at: string
          updated_review_at?: string
          reply_text?: string
          reply_published_at?: string
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          business_id?: string
          source_id?: string
          source_review_id?: string
          author_name?: string
          author_id?: string
          author_avatar?: string
          rating?: number
          review_text?: string
          language?: string
          likes?: number
          published_at?: string
          updated_review_at?: string
          reply_text?: string
          reply_published_at?: string
          metadata?: Record<string, any>
        }
      }
      scraper_runs: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          source_id: string
          actor_name: string
          status: 'pending' | 'running' | 'completed' | 'failed'
          started_at?: string
          completed_at?: string
          apify_run_id?: string
          apify_dataset_id?: string
          storage_path?: string
          duration_seconds?: number
          cost?: number
          parameters?: Record<string, any>
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          source_id: string
          actor_name: string
          status?: 'pending' | 'running' | 'completed' | 'failed'
          started_at?: string
          completed_at?: string
          apify_run_id?: string
          apify_dataset_id?: string
          storage_path?: string
          duration_seconds?: number
          cost?: number
          parameters?: Record<string, any>
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          source_id?: string
          actor_name?: string
          status?: 'pending' | 'running' | 'completed' | 'failed'
          started_at?: string
          completed_at?: string
          apify_run_id?: string
          apify_dataset_id?: string
          storage_path?: string
          duration_seconds?: number
          cost?: number
          parameters?: Record<string, any>
          metadata?: Record<string, any>
        }
      }
      business_scraper_runs: {
        Row: {
          business_id: string
          scraper_run_id: string
          created_at: string
          version_before: number
          version_after: number
          changes_made?: Record<string, any>
        }
        Insert: {
          business_id: string
          scraper_run_id: string
          created_at?: string
          version_before: number
          version_after: number
          changes_made?: Record<string, any>
        }
        Update: {
          business_id?: string
          scraper_run_id?: string
          created_at?: string
          version_before?: number
          version_after?: number
          changes_made?: Record<string, any>
        }
      }
      user_businesses: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          business_id: string
          relationship_type: 'owner' | 'manager' | 'employee' | 'follower'
          metadata?: Record<string, any>
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          business_id: string
          relationship_type?: 'owner' | 'manager' | 'employee' | 'follower'
          metadata?: Record<string, any>
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          business_id?: string
          relationship_type?: 'owner' | 'manager' | 'employee' | 'follower'
          metadata?: Record<string, any>
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
