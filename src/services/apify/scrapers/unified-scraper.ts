import { BaseScraper } from '../base-scraper'
import { 
  ScraperR<PERSON>ult,
  ApifyConfig 
} from '../types'

export interface UnifiedScraperInput {
  urls: string[]
  maxReviews?: number
  reviewsSort?: 'newest' | 'mostRelevant' | 'highestRanking' | 'lowestRanking'
  language?: string
  extractContacts?: boolean
  extractHours?: boolean
  extractPhotos?: boolean
  maxPhotos?: number
}

export interface UnifiedBusinessData {
  googlePlaceId: string
  name: string
  address: string
  phone?: string
  website?: string
  category: string
  rating: number
  totalReviews: number
  priceLevel?: string
  coordinates: {
    lat: number
    lng: number
  }
  verified?: boolean
  plusCode?: string
  openingHours?: Record<string, string>
  photos?: Array<{
    url: string
    caption?: string
  }>
}

export interface UnifiedReviewData {
  googleReviewId: string
  authorName: string
  authorId?: string
  authorAvatar?: string
  authorUrl?: string
  rating: number
  reviewText?: string
  language?: string
  likes: number
  publishedAt: string
  updatedAt?: string
  replyText?: string
  replyPublishedAt?: string
  images?: Array<{
    url: string
    caption?: string
  }>
}

export interface UnifiedScraperResult {
  business: UnifiedBusinessData
  reviews: UnifiedReviewData[]
  extractedAt: string
}

export class UnifiedScraper extends BaseScraper {
  constructor(config: ApifyConfig) {
    super(config)
    this.validateConfig()
  }

  getActorId(): string {
    return 'compass/crawler-google-places'
  }

  prepareInput(input: UnifiedScraperInput): any {
    // Validate input
    if (!input.urls || !Array.isArray(input.urls) || input.urls.length === 0) {
      throw new Error('URLs array is required and must not be empty')
    }

    return {
      startUrls: input.urls.map(url => ({ url })),
      maxReviews: input.maxReviews || 10,
      reviewsSort: input.reviewsSort || 'newest',
      language: input.language || 'en',
      scrapePlaceDetailPage: true, // Required for reviews and detailed info
      scrapeReviewsPersonalData: true, // Include reviewer data
      includeOpeningHours: input.extractHours !== false,
      maxImages: input.extractPhotos !== false ? (input.maxPhotos || 10) : 0,
      scrapeContacts: input.extractContacts !== false,
      reviewsOrigin: 'google', // Only Google reviews
    }
  }

  processResults(items: any[]): UnifiedScraperResult[] {
    return items.map(item => ({
      business: this.extractBusinessData(item),
      reviews: this.extractReviewsData(item.reviews || []),
      extractedAt: new Date().toISOString(),
    }))
  }

  private extractBusinessData(item: any): UnifiedBusinessData {
    return {
      googlePlaceId: item.placeId || '',
      name: item.title || item.name || '',
      address: item.address || '',
      phone: item.phone || item.phoneNumber || '',
      website: item.website || item.websiteUrl || '',
      category: item.categoryName || item.category || '',
      rating: item.totalScore || item.rating || 0,
      totalReviews: item.reviewsCount || item.totalReviews || 0,
      priceLevel: item.priceLevel || '',
      coordinates: {
        lat: item.location?.lat || item.latitude || 0,
        lng: item.location?.lng || item.longitude || 0,
      },
      verified: item.isVerified || false,
      plusCode: item.plusCode || '',
      openingHours: this.parseOpeningHours(item.openingHours || item.hours),
      photos: this.parsePhotos(item.images || item.photos || []),
    }
  }

  private extractReviewsData(reviews: any[]): UnifiedReviewData[] {
    if (!Array.isArray(reviews)) return []
    
    return reviews.map(review => ({
      googleReviewId: review.id || review.reviewId || '',
      authorName: review.name || review.author?.name || '',
      authorId: review.reviewerId || review.author?.id || '',
      authorAvatar: review.profilePhotoUrl || review.author?.avatar || '',
      authorUrl: review.reviewerUrl || review.author?.url || '',
      rating: review.stars || review.rating || 0,
      reviewText: review.text || review.review || '',
      language: review.language || '',
      likes: review.likesCount || review.likes || 0,
      publishedAt: review.publishedAtDate || review.publishedAt || new Date().toISOString(),
      updatedAt: review.updatedAtDate || review.updatedAt || '',
      replyText: review.responseFromOwnerText || review.reply || '',
      replyPublishedAt: review.responseFromOwnerDate || review.replyPublishedAt || '',
      images: this.parseReviewImages(review.reviewImageUrls || review.images || []),
    }))
  }

  private parseOpeningHours(hours: any): Record<string, string> {
    if (!hours) return {}
    
    if (Array.isArray(hours)) {
      const hoursObj: Record<string, string> = {}
      hours.forEach(hour => {
        if (typeof hour === 'string') {
          // Parse "Monday: 9:00 AM – 5:00 PM" format
          const match = hour.match(/^(\w+):\s*(.+)$/)
          if (match) {
            hoursObj[match[1].toLowerCase()] = match[2]
          }
        } else if (hour.day && hour.hours) {
          hoursObj[hour.day.toLowerCase()] = hour.hours
        }
      })
      return hoursObj
    }
    
    if (typeof hours === 'object') {
      return hours
    }
    
    return {}
  }

  private parsePhotos(photos: any[]): Array<{ url: string; caption?: string }> {
    if (!Array.isArray(photos)) return []
    
    return photos.slice(0, 10).map(photo => {
      if (typeof photo === 'string') {
        return { url: photo }
      }
      
      return {
        url: photo.url || photo.src || photo.image || '',
        caption: photo.caption || photo.alt || photo.title || undefined,
      }
    }).filter(photo => photo.url)
  }

  private parseReviewImages(images: any[]): Array<{ url: string; caption?: string }> {
    if (!Array.isArray(images)) return []
    
    return images.map(image => {
      if (typeof image === 'string') {
        return { url: image }
      }
      
      return {
        url: image.url || image.src || image,
        caption: image.caption || image.alt || undefined,
      }
    }).filter(image => image.url)
  }

  /**
   * Scrape Google Maps business data with reviews
   */
  async fetchBusiness(input: UnifiedScraperInput): Promise<ScraperResult<UnifiedScraperResult>> {
    console.log('🏢 Starting unified business + reviews scraping...')
    console.log(`📍 URLs to scrape: ${input.urls.length}`)
    console.log(`📝 Max reviews per business: ${input.maxReviews || 10}`)
    console.log(`🔄 Review sort: ${input.reviewsSort || 'newest'}`)
    console.log(`📞 Extract contacts: ${input.extractContacts !== false}`)
    console.log(`🕒 Extract hours: ${input.extractHours !== false}`)
    console.log(`📸 Extract photos: ${input.extractPhotos !== false}`)

    return this.executeScraper<UnifiedScraperInput, UnifiedScraperResult>(input)
  }

  /**
   * Get statistics from scraped data
   */
  getStats(results: UnifiedScraperResult[]) {
    const stats = {
      totalBusinesses: results.length,
      totalReviews: 0,
      averageBusinessRating: 0,
      averageReviewRating: 0,
      categoryDistribution: {} as Record<string, number>,
      languageDistribution: {} as Record<string, number>,
      businessesWithReviews: 0,
      businessesWithPhone: 0,
      businessesWithWebsite: 0,
      totalLikes: 0,
    }

    let businessRatingSum = 0
    let businessesWithRating = 0
    let reviewRatingSum = 0

    results.forEach(result => {
      const business = result.business
      
      // Business statistics
      if (business.rating > 0) {
        businessRatingSum += business.rating
        businessesWithRating++
      }
      
      if (business.category) {
        stats.categoryDistribution[business.category] = (stats.categoryDistribution[business.category] || 0) + 1
      }
      
      if (business.phone) stats.businessesWithPhone++
      if (business.website) stats.businessesWithWebsite++
      
      // Review statistics
      if (result.reviews.length > 0) {
        stats.businessesWithReviews++
        stats.totalReviews += result.reviews.length
        
        result.reviews.forEach(review => {
          reviewRatingSum += review.rating
          stats.totalLikes += review.likes
          
          if (review.language) {
            stats.languageDistribution[review.language] = (stats.languageDistribution[review.language] || 0) + 1
          }
        })
      }
    })

    stats.averageBusinessRating = businessesWithRating > 0 ? businessRatingSum / businessesWithRating : 0
    stats.averageReviewRating = stats.totalReviews > 0 ? reviewRatingSum / stats.totalReviews : 0

    return stats
  }
}
