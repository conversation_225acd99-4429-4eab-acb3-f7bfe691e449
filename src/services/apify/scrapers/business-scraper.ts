import { BaseScraper } from '../base-scraper'
import { 
  BusinessScraperInput, 
  BusinessScraperResult, 
  ScraperResult,
  ApifyConfig 
} from '../types'

export class BusinessScraper extends BaseScraper {
  constructor(config: ApifyConfig) {
    super(config)
    this.validateConfig()
  }

  getActorId(): string {
    // Using a different actor for business data extraction
    // This could be 'compass/crawler-google-places' or another suitable actor
    return 'compass/crawler-google-places'
  }

  prepareInput(input: BusinessScraperInput): any {
    // Validate input
    if (!input.urls || !Array.isArray(input.urls) || input.urls.length === 0) {
      throw new Error('URLs array is required and must not be empty')
    }

    return {
      startUrls: input.urls.map(url => ({ url })),
      includeHistogram: false,
      includeOpeningHours: input.extractHours !== false,
      includePeopleAlsoSearch: false,
      includeReviews: false, // We only want business data, not reviews
      includeImages: input.extractPhotos !== false,
      maxImages: input.maxPhotos || 10,
      reviewsSort: 'newest',
      language: 'en',
      maxReviews: 0, // No reviews needed for business scraper
      exportPlaceUrls: false,
      additionalInfo: input.extractContacts !== false,
    }
  }

  processResults(items: any[]): BusinessScraperResult[] {
    return items.map(item => ({
      url: item.url || item.searchPageUrl || '',
      business: {
        id: item.placeId || item.id || '',
        name: item.title || item.name || '',
        address: item.address || item.location?.address || '',
        phone: item.phone || item.phoneNumber || '',
        website: item.website || item.websiteUrl || '',
        category: item.categoryName || item.category || '',
        rating: item.totalScore || item.rating || 0,
        totalReviews: item.reviewsCount || item.totalReviews || 0,
        priceLevel: item.priceLevel || '',
        hours: this.parseOpeningHours(item.openingHours || item.hours),
        coordinates: {
          lat: item.location?.lat || item.latitude || 0,
          lng: item.location?.lng || item.longitude || 0,
        },
        photos: this.parsePhotos(item.images || item.photos || []),
        verified: item.isVerified || false,
        plusCode: item.plusCode || '',
        url: item.url || item.searchPageUrl || '',
      },
      extractedAt: new Date().toISOString(),
    }))
  }

  /**
   * Parse opening hours from various formats
   */
  private parseOpeningHours(hours: any): Record<string, string> {
    if (!hours) return {}
    
    if (Array.isArray(hours)) {
      const hoursObj: Record<string, string> = {}
      hours.forEach(hour => {
        if (typeof hour === 'string') {
          // Parse "Monday: 9:00 AM – 5:00 PM" format
          const match = hour.match(/^(\w+):\s*(.+)$/)
          if (match) {
            hoursObj[match[1].toLowerCase()] = match[2]
          }
        } else if (hour.day && hour.hours) {
          hoursObj[hour.day.toLowerCase()] = hour.hours
        }
      })
      return hoursObj
    }
    
    if (typeof hours === 'object') {
      return hours
    }
    
    return {}
  }

  /**
   * Parse photos from various formats
   */
  private parsePhotos(photos: any[]): Array<{ url: string; caption?: string }> {
    if (!Array.isArray(photos)) return []
    
    return photos.slice(0, 10).map(photo => {
      if (typeof photo === 'string') {
        return { url: photo }
      }
      
      return {
        url: photo.url || photo.src || photo.image || '',
        caption: photo.caption || photo.alt || photo.title || undefined,
      }
    }).filter(photo => photo.url)
  }

  /**
   * Scrape Google Maps business data
   */
  async scrapeBusiness(input: BusinessScraperInput): Promise<ScraperResult<BusinessScraperResult>> {
    console.log('🏢 Starting Google Maps business scraping...')
    console.log(`📍 URLs to scrape: ${input.urls.length}`)
    console.log(`📞 Extract contacts: ${input.extractContacts !== false}`)
    console.log(`🕒 Extract hours: ${input.extractHours !== false}`)
    console.log(`📸 Extract photos: ${input.extractPhotos !== false}`)

    return this.executeScraper<BusinessScraperInput, BusinessScraperResult>(input)
  }

  /**
   * Get business statistics from scraped data
   */
  getBusinessStats(results: BusinessScraperResult[]) {
    const stats = {
      totalBusinesses: results.length,
      averageRating: 0,
      categoryDistribution: {} as Record<string, number>,
      ratingDistribution: {} as Record<number, number>,
      businessesWithPhone: 0,
      businessesWithWebsite: 0,
      businessesWithHours: 0,
      businessesWithPhotos: 0,
      verifiedBusinesses: 0,
    }

    let totalRatingSum = 0
    let businessesWithRating = 0

    results.forEach(result => {
      const business = result.business
      
      // Rating statistics
      if (business.rating > 0) {
        totalRatingSum += business.rating
        businessesWithRating++
        const roundedRating = Math.round(business.rating)
        stats.ratingDistribution[roundedRating] = (stats.ratingDistribution[roundedRating] || 0) + 1
      }
      
      // Category statistics
      if (business.category) {
        stats.categoryDistribution[business.category] = (stats.categoryDistribution[business.category] || 0) + 1
      }
      
      // Contact information
      if (business.phone) stats.businessesWithPhone++
      if (business.website) stats.businessesWithWebsite++
      if (business.hours && Object.keys(business.hours).length > 0) stats.businessesWithHours++
      if (business.photos && business.photos.length > 0) stats.businessesWithPhotos++
      if (business.verified) stats.verifiedBusinesses++
    })

    stats.averageRating = businessesWithRating > 0 ? totalRatingSum / businessesWithRating : 0

    return stats
  }

  /**
   * Filter businesses by criteria
   */
  filterBusinesses(
    results: BusinessScraperResult[], 
    filters: {
      minRating?: number
      maxRating?: number
      category?: string
      hasPhone?: boolean
      hasWebsite?: boolean
      hasHours?: boolean
      verified?: boolean
      minReviews?: number
    }
  ): BusinessScraperResult[] {
    return results.filter(result => {
      const business = result.business
      
      // Rating filter
      if (filters.minRating && business.rating < filters.minRating) return false
      if (filters.maxRating && business.rating > filters.maxRating) return false
      
      // Category filter
      if (filters.category && business.category !== filters.category) return false
      
      // Contact filters
      if (filters.hasPhone && !business.phone) return false
      if (filters.hasWebsite && !business.website) return false
      if (filters.hasHours && (!business.hours || Object.keys(business.hours).length === 0)) return false
      
      // Verification filter
      if (filters.verified !== undefined && business.verified !== filters.verified) return false
      
      // Reviews filter
      if (filters.minReviews && business.totalReviews < filters.minReviews) return false
      
      return true
    })
  }
}
