import { BaseScraper } from '../base-scraper'
import { 
  ReviewScraperInput, 
  ReviewScraperResult, 
  ScraperResult,
  ApifyConfig 
} from '../types'

export class ReviewScraper extends BaseScraper {
  constructor(config: ApifyConfig) {
    super(config)
    this.validateConfig()
  }

  getActorId(): string {
    return 'canadesk/google-places-reviews'
  }

  prepareInput(input: ReviewScraperInput): any {
    // Validate input
    if (!input.urls || !Array.isArray(input.urls) || input.urls.length === 0) {
      throw new Error('URLs array is required and must not be empty')
    }

    // Convert review sort to the format expected by canadesk/google-places-reviews
    const sortMapping: Record<string, '1' | '2' | '3' | '4' | '5'> = {
      'Most relevant': '1',
      'Newest': '2',
      'Highest rating': '3',
      'Lowest rating': '4',
      'Most likes': '5'
    }

    return {
      urls: input.urls,
      category: input.category || 'reviews',
      maximum: input.maximum || 500,
      sorting: input.sorting || '2', // Default to Newest
      lang: input.lang || 'all',
    }
  }

  processResults(items: any[]): ReviewScraperResult[] {
    return items.map(item => ({
      url: item.url || '',
      location: item.location || null,
      reviews: item.reviews || [],
      count: item.count || 0,
    }))
  }

  /**
   * Scrape Google Maps reviews
   */
  async scrapeReviews(input: ReviewScraperInput): Promise<ScraperResult<ReviewScraperResult>> {
    console.log('🔍 Starting Google Maps review scraping...')
    console.log(`📍 URLs to scrape: ${input.urls.length}`)
    console.log(`📊 Max reviews per location: ${input.maximum || 500}`)
    console.log(`🔄 Sort type: ${input.sorting || '2'} (2=Newest)`)

    return this.executeScraper<ReviewScraperInput, ReviewScraperResult>(input)
  }

  /**
   * Get review statistics from scraped data
   */
  getReviewStats(results: ReviewScraperResult[]) {
    const stats = {
      totalLocations: results.length,
      totalReviews: 0,
      averageRating: 0,
      languageDistribution: {} as Record<string, number>,
      ratingDistribution: {} as Record<number, number>,
      totalLikes: 0,
    }

    let totalRatingSum = 0
    let totalReviewCount = 0

    results.forEach(location => {
      stats.totalReviews += location.reviews.length
      
      location.reviews.forEach(review => {
        // Rating statistics
        totalRatingSum += review.rating
        totalReviewCount++
        stats.ratingDistribution[review.rating] = (stats.ratingDistribution[review.rating] || 0) + 1
        
        // Language statistics
        const lang = review.language || 'unknown'
        stats.languageDistribution[lang] = (stats.languageDistribution[lang] || 0) + 1
        
        // Likes
        stats.totalLikes += review.likes || 0
      })
    })

    stats.averageRating = totalReviewCount > 0 ? totalRatingSum / totalReviewCount : 0

    return stats
  }

  /**
   * Filter reviews by criteria
   */
  filterReviews(
    results: ReviewScraperResult[], 
    filters: {
      minRating?: number
      maxRating?: number
      language?: string
      hasText?: boolean
      minLikes?: number
      dateFrom?: Date
      dateTo?: Date
    }
  ): ReviewScraperResult[] {
    return results.map(location => ({
      ...location,
      reviews: location.reviews.filter(review => {
        // Rating filter
        if (filters.minRating && review.rating < filters.minRating) return false
        if (filters.maxRating && review.rating > filters.maxRating) return false
        
        // Language filter
        if (filters.language && review.language !== filters.language) return false
        
        // Text filter
        if (filters.hasText && (!review.review || review.review.trim().length === 0)) return false
        
        // Likes filter
        if (filters.minLikes && review.likes < filters.minLikes) return false
        
        // Date filters
        if (filters.dateFrom || filters.dateTo) {
          const reviewDate = new Date(review.publishedAt)
          if (filters.dateFrom && reviewDate < filters.dateFrom) return false
          if (filters.dateTo && reviewDate > filters.dateTo) return false
        }
        
        return true
      })
    })).filter(location => location.reviews.length > 0) // Remove locations with no matching reviews
  }
}
