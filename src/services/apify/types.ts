// Apify service types

export interface ApifyConfig {
  token: string
  timeout?: number
  retries?: number
}

export interface ScraperResult<T = any> {
  success: boolean
  data?: T[]
  error?: string
  message?: string
  metadata?: {
    runId?: string
    datasetId?: string
    itemCount?: number
    executionTime?: number
  }
}

// Review Scraper Types
export interface ReviewScraperInput {
  urls: string[]
  category: 'reviews' | 'preview'
  maximum?: number
  sorting?: '1' | '2' | '3' | '4' | '5' // 1=Most relevant, 2=Newest, 3=Highest, 4=Lowest, 5=Most likes
  lang?: string
}

export interface ReviewData {
  id: string
  publishedAt: string
  updatedAt: string
  rating: number
  review: string
  language: string
  likes: number
  reply?: string
  author: {
    name: string
    avatar: string
    url: string
    id: string
  }
  images?: Array<{
    id: string
    url: string
    size: {
      width: number
      height: number
    }
    location: string
    caption: string | null
  }>
  source: string
}

export interface ReviewScraperResult {
  url: string
  location: any
  reviews: ReviewData[]
  count: number
}

// Business Scraper Types
export interface BusinessScraperInput {
  urls: string[]
  extractContacts?: boolean
  extractHours?: boolean
  extractPhotos?: boolean
  maxPhotos?: number
}

export interface BusinessData {
  id: string
  name: string
  address: string
  phone?: string
  website?: string
  category: string
  rating: number
  totalReviews: number
  priceLevel?: string
  hours?: {
    [day: string]: string
  }
  coordinates?: {
    lat: number
    lng: number
  }
  photos?: Array<{
    url: string
    caption?: string
  }>
  verified?: boolean
  plusCode?: string
  url: string
}

export interface BusinessScraperResult {
  url: string
  business: BusinessData
  extractedAt: string
}
