import { ApifyConfig } from './types'
import { BusinessScraper } from './scrapers/business-scraper'
import { UnifiedScraper } from './scrapers/unified-scraper'

/**
 * Main Apify service that provides access to all scrapers
 */
export class ApifyService {
  private config: ApifyConfig
  private _businessScraper?: BusinessScraper
  private _unifiedScraper?: UnifiedScraper

  constructor(config: ApifyConfig) {
    this.config = config
    this.validateConfig()
  }

  /**
   * Get the business scraper instance (lazy initialization)
   */
  get businessScraper(): BusinessScraper {
    if (!this._businessScraper) {
      this._businessScraper = new BusinessScraper(this.config)
    }
    return this._businessScraper
  }

  /**
   * Get the unified scraper instance (lazy initialization)
   */
  get unifiedScraper(): UnifiedScraper {
    if (!this._unifiedScraper) {
      this._unifiedScraper = new UnifiedScraper(this.config)
    }
    return this._unifiedScraper
  }

  /**
   * Validate the service configuration
   */
  private validateConfig(): void {
    if (!this.config.token) {
      throw new Error('Apify token is required')
    }
  }

  /**
   * Get service configuration
   */
  getConfig(): ApifyConfig {
    return { ...this.config }
  }

  /**
   * Update service configuration
   */
  updateConfig(newConfig: Partial<ApifyConfig>): void {
    this.config = { ...this.config, ...newConfig }

    // Reset scrapers to pick up new config
    this._businessScraper = undefined
    this._unifiedScraper = undefined
  }

  /**
   * Get information about all available scrapers
   */
  async getScrapersInfo() {
    const scrapers = [
      {
        name: 'unifiedScraper',
        description: 'Scrapes Google Maps business information and reviews in one request',
        actorId: 'compass/crawler-google-places',
        instance: this.unifiedScraper,
      },
      {
        name: 'businessScraper',
        description: 'Scrapes Google Maps business information only',
        actorId: 'compass/crawler-google-places',
        instance: this.businessScraper,
      },
    ]

    const scrapersInfo = await Promise.all(
      scrapers.map(async scraper => {
        try {
          const actorInfo = await scraper.instance.getActorInfo()
          return {
            ...scraper,
            actorInfo,
            available: true,
          }
        } catch (error) {
          return {
            ...scraper,
            actorInfo: null,
            available: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          }
        }
      })
    )

    return scrapersInfo
  }

  /**
   * Test the service connection
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      // Try to get info about the unified scraper actor
      const actorInfo = await this.unifiedScraper.getActorInfo()

      if (actorInfo) {
        return {
          success: true,
          message: `Successfully connected to Apify. Actor "${actorInfo.name}" is available.`,
        }
      } else {
        return {
          success: false,
          message: 'Connected to Apify but could not retrieve actor information.',
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to connect to Apify: ${error instanceof Error ? error.message : 'Unknown error'}`,
      }
    }
  }
}

/**
 * Factory function to create ApifyService instance
 */
export function createApifyService(token: string, options?: Partial<ApifyConfig>): ApifyService {
  const config: ApifyConfig = {
    token,
    timeout: 300000, // 5 minutes default
    retries: 3,
    ...options,
  }

  return new ApifyService(config)
}

/**
 * Singleton instance for the default service
 */
let defaultService: ApifyService | null = null

/**
 * Get or create the default Apify service instance
 */
export function getApifyService(token?: string): ApifyService {
  if (!defaultService) {
    if (!token) {
      throw new Error('Apify token is required to initialize the service')
    }
    defaultService = createApifyService(token)
  }
  
  return defaultService
}

/**
 * Reset the default service instance (useful for testing)
 */
export function resetApifyService(): void {
  defaultService = null
}
