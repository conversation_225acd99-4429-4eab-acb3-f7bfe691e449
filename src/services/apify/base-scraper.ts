import { ApifyClient } from 'apify-client'
import { ApifyConfig, ScraperR<PERSON>ult } from './types'

export abstract class BaseScraper {
  protected client: ApifyClient
  protected config: ApifyConfig

  constructor(config: ApifyConfig) {
    this.config = config
    this.client = new ApifyClient({
      token: config.token,
    })
  }

  /**
   * Abstract method that each scraper must implement
   */
  abstract getActorId(): string

  /**
   * Abstract method to prepare input for the specific actor
   */
  abstract prepareInput(input: any): any

  /**
   * Abstract method to process the scraped data
   */
  abstract processResults(items: any[]): any[]

  /**
   * Common scraping logic that all scrapers can use
   */
  protected async executeScraper<TInput, TOutput>(
    input: TInput,
    options?: {
      timeout?: number
      memory?: number
      build?: string
    }
  ): Promise<ScraperResult<TOutput>> {
    const startTime = Date.now()
    
    try {
      console.log(`🚀 Starting ${this.constructor.name} with actor: ${this.getActorId()}`)
      
      // Prepare the input for the specific actor
      const actorInput = this.prepareInput(input)
      console.log('📋 Actor input prepared:', JSON.stringify(actorInput, null, 2))

      // Run the Apify actor
      const run = await this.client.actor(this.getActorId()).call(actorInput, {
        timeout: options?.timeout || this.config.timeout,
        memory: options?.memory,
        build: options?.build,
      })

      if (!run.defaultDatasetId) {
        throw new Error('No dataset ID returned from the actor run')
      }

      console.log(`✅ Actor run completed. Run ID: ${run.id}, Dataset ID: ${run.defaultDatasetId}`)

      // Fetch all items from the dataset
      const { items } = await this.client.dataset(run.defaultDatasetId).listItems()
      console.log(`📋 Retrieved ${items.length} item(s) from dataset`)

      // Process the results using the specific scraper's logic
      const processedData = this.processResults(items)

      const executionTime = Date.now() - startTime

      return {
        success: true,
        data: processedData,
        message: `Successfully scraped ${processedData.length} item(s)`,
        metadata: {
          runId: run.id,
          datasetId: run.defaultDatasetId,
          itemCount: items.length,
          executionTime,
        },
      }
    } catch (error) {
      const executionTime = Date.now() - startTime
      console.error(`❌ ${this.constructor.name} failed:`, error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: {
          executionTime,
        },
      }
    }
  }

  /**
   * Validate that required configuration is present
   */
  protected validateConfig(): void {
    if (!this.config.token) {
      throw new Error('Apify token is required')
    }
  }

  /**
   * Get actor information
   */
  async getActorInfo() {
    try {
      const actor = await this.client.actor(this.getActorId()).get()

      if(!actor) {
        return null
      }
      return {
        name: actor.name,
        description: actor.description,
        version: actor.defaultRunOptions?.build,
        isPublic: actor.isPublic,
        stats: actor.stats,
      }
    } catch (error) {
      console.error('Failed to get actor info:', error)
      return null
    }
  }
}
