import { NextRequest, NextResponse } from 'next/server'
import { UnifiedScraper, UnifiedScraperInput } from '@/services/apify/scrapers/unified-scraper'
import { DatabaseService } from '@/services/database/database-service'
import { ApifyService } from '@/services/apify/apify-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate request body
    if (!body.urls || !Array.isArray(body.urls) || body.urls.length === 0) {
      return NextResponse.json(
        { error: 'URLs array is required and must not be empty' },
        { status: 400 }
      )
    }

    // Validate URLs
    const validUrls = body.urls.filter((url: string) => {
      try {
        const urlObj = new URL(url)
        return urlObj.hostname.includes('google.com') && urlObj.pathname.includes('/maps/')
      } catch {
        return false
      }
    })

    if (validUrls.length === 0) {
      return NextResponse.json(
        { error: 'No valid Google Maps URLs provided' },
        { status: 400 }
      )
    }

    if (validUrls.length !== body.urls.length) {
      console.warn(`⚠️ Filtered out ${body.urls.length - validUrls.length} invalid URLs`)
    }

    // Prepare scraper input
    const scraperInput: UnifiedScraperInput = {
      urls: validUrls,
      maxReviews: Math.min(body.maxReviews || 10, 100), // Cap at 100 reviews
      reviewsSort: body.reviewsSort || 'newest',
      language: body.language || 'en',
      extractContacts: body.extractContacts !== false,
      extractHours: body.extractHours !== false,
      extractPhotos: body.extractPhotos !== false,
      maxPhotos: Math.min(body.maxPhotos || 10, 20), // Cap at 20 photos
    }

    console.log('🚀 Starting unified business + reviews scraping...')
    console.log(`📍 URLs: ${validUrls.length}`)
    console.log(`📝 Max reviews per business: ${scraperInput.maxReviews}`)
    console.log(`🔄 Review sort: ${scraperInput.reviewsSort}`)

    // Initialize services
    const apifyToken = process.env.APIFY_TOKEN
    if (!apifyToken) {
      return NextResponse.json(
        { error: 'Apify token not configured' },
        { status: 500 }
      )
    }

    const apifyService = new ApifyService({ token: apifyToken })
    const unifiedScraper = new UnifiedScraper(apifyService.getConfig())
    const databaseService = new DatabaseService()

    // Create scrape session
    const sessionId = await databaseService.createScrapeSession({
      sessionType: 'unified',
      urls: validUrls,
      parameters: {
        maxReviews: scraperInput.maxReviews,
        reviewsSort: scraperInput.reviewsSort,
        language: scraperInput.language,
        extractContacts: scraperInput.extractContacts,
        extractHours: scraperInput.extractHours,
        extractPhotos: scraperInput.extractPhotos,
        maxPhotos: scraperInput.maxPhotos,
      },
    })

    // Update session status to running
    await databaseService.updateScrapeSession(sessionId, {
      status: 'running',
    })

    try {
      // Execute scraping
      const result = await unifiedScraper.fetchBusiness(scraperInput)

      if (!result.success) {
        await databaseService.updateScrapeSession(sessionId, {
          status: 'failed',
          errorMessage: result.error,
        })

        return NextResponse.json(
          {
            error: 'Scraping failed',
            details: result.error,
            sessionId: sessionId
          },
          { status: 500 }
        )
      }

      console.log(`✅ Scraping completed: ${result.data?.length || 0} businesses found`)

      // Save data to database
      const saveResult = await databaseService.saveUnifiedData(result.data || [], {
        sessionId: sessionId,
        metadata: {
          scraperType: 'unified',
          apifyRunId: result.metadata?.runId,
          executionTime: result.metadata?.executionTime,
        },
      })

      // Get statistics
      const stats = unifiedScraper.getStats(result.data || [])

      // Update session with final results
      await databaseService.updateScrapeSession(sessionId, {
        status: 'completed',
        resultsCount: result.data?.length || 0,
      })

      console.log('💾 Data saved successfully')
      console.log(`📊 Stats: ${stats.totalBusinesses} businesses, ${stats.totalReviews} reviews`)

      // Return success response
      return NextResponse.json({
        success: true,
        sessionId: sessionId,
        data: {
          businesses: result.data || [],
          stats,
          saveResult,
        },
        meta: {
          runId: result.metadata?.runId,
          executionTime: result.metadata?.executionTime,
          urlsProcessed: validUrls.length,
          urlsFiltered: body.urls.length - validUrls.length,
        },
      })

    } catch (scrapingError) {
      console.error('❌ Scraping error:', scrapingError)

      await databaseService.updateScrapeSession(sessionId, {
        status: 'failed',
        errorMessage: scrapingError instanceof Error ? scrapingError.message : 'Unknown scraping error',
      })

      return NextResponse.json(
        {
          error: 'Scraping failed',
          details: scrapingError instanceof Error ? scrapingError.message : 'Unknown error',
          sessionId: sessionId
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')
    const limit = parseInt(searchParams.get('limit') || '10')

    const databaseService = new DatabaseService()

    if (sessionId) {
      // Get specific session results
      const businesses = await databaseService.getBusinessesWithReviews(sessionId, limit)
      const session = await databaseService.getScrapeSession(sessionId)

      return NextResponse.json({
        success: true,
        session,
        businesses,
        count: businesses.length,
      })
    } else {
      // Get recent businesses
      const businesses = await databaseService.getBusinessesWithReviews(undefined, limit)
      const stats = await databaseService.getStats()

      return NextResponse.json({
        success: true,
        businesses,
        stats,
        count: businesses.length,
      })
    }

  } catch (error) {
    console.error('❌ GET API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch data', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}
