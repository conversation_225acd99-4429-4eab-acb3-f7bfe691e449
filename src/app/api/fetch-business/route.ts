import { NextRequest, NextResponse } from 'next/server'
import { UnifiedScraper, UnifiedScraperInput } from '@/services/apify/scrapers/unified-scraper'
import { MultiSourceDatabaseService } from '@/services/database/multi-source-database-service'
import { ApifyService } from '@/services/apify/apify-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate request body
    if (!body.urls || !Array.isArray(body.urls) || body.urls.length === 0) {
      return NextResponse.json(
        { error: 'URLs array is required and must not be empty' },
        { status: 400 }
      )
    }

    // Validate URLs
    const validUrls = body.urls.filter((url: string) => {
      try {
        const urlObj = new URL(url)
        return urlObj.hostname.includes('google.com') && urlObj.pathname.includes('/maps/')
      } catch {
        return false
      }
    })

    if (validUrls.length === 0) {
      return NextResponse.json(
        { error: 'No valid Google Maps URLs provided' },
        { status: 400 }
      )
    }

    if (validUrls.length !== body.urls.length) {
      console.warn(`⚠️ Filtered out ${body.urls.length - validUrls.length} invalid URLs`)
    }

    // Prepare scraper input
    const scraperInput: UnifiedScraperInput = {
      urls: validUrls,
      maxReviews: Math.min(body.maxReviews || 10, 100), // Cap at 100 reviews
      reviewsSort: body.reviewsSort || 'newest',
      language: body.language || 'en',
      extractContacts: body.extractContacts !== false,
      extractHours: body.extractHours !== false,
      extractPhotos: body.extractPhotos !== false,
      maxPhotos: Math.min(body.maxPhotos || 10, 20), // Cap at 20 photos
    }

    console.log('🚀 Starting unified business + reviews scraping...')
    console.log(`📍 URLs: ${validUrls.length}`)
    console.log(`📝 Max reviews per business: ${scraperInput.maxReviews}`)
    console.log(`🔄 Review sort: ${scraperInput.reviewsSort}`)

    // Initialize services
    const apifyToken = process.env.APIFY_TOKEN
    if (!apifyToken) {
      return NextResponse.json(
        { error: 'Apify token not configured' },
        { status: 500 }
      )
    }

    const apifyService = new ApifyService({ token: apifyToken })
    const unifiedScraper = new UnifiedScraper(apifyService.getConfig())
    const databaseService = new MultiSourceDatabaseService()

    // Get Google source (assuming we're scraping Google Maps)
    const googleSource = await databaseService.getSourceByName('google')
    if (!googleSource) {
      return NextResponse.json(
        { error: 'Google source not found in database. Please run the schema setup first.' },
        { status: 500 }
      )
    }

    // Create scraper run
    const scraperRunId = await databaseService.createScraperRun({
      sourceId: googleSource.id,
      actorName: 'compass/crawler-google-places',
      parameters: {
        maxReviews: scraperInput.maxReviews,
        reviewsSort: scraperInput.reviewsSort,
        language: scraperInput.language,
        extractContacts: scraperInput.extractContacts,
        extractHours: scraperInput.extractHours,
        extractPhotos: scraperInput.extractPhotos,
        maxPhotos: scraperInput.maxPhotos,
        urls: validUrls,
      },
      metadata: {
        urlsCount: validUrls.length,
        requestTimestamp: new Date().toISOString()
      }
    })

    // Update scraper run status to running
    await databaseService.updateScraperRun(scraperRunId, {
      status: 'running',
    })

    try {
      // Execute scraping
      const result = await unifiedScraper.fetchBusiness(scraperInput)

      if (!result.success) {
        await databaseService.updateScraperRun(scraperRunId, {
          status: 'failed',
          completedAt: new Date().toISOString(),
          metadata: {
            error: result.error,
            executionTime: result.metadata?.executionTime
          }
        })

        return NextResponse.json(
          {
            error: 'Scraping failed',
            details: result.error,
            scraperRunId: scraperRunId
          },
          { status: 500 }
        )
      }

      console.log(`✅ Scraping completed: ${result.data?.length || 0} businesses found`)

      // Save data to database using multi-source logic
      const saveResult = await databaseService.saveUnifiedData('google', result.data || [], {
        scraperRunId: scraperRunId,
        metadata: {
          scraperType: 'unified',
          apifyRunId: result.metadata?.runId,
          executionTime: result.metadata?.executionTime,
        },
      })

      // Get statistics
      const stats = unifiedScraper.getStats(result.data || [])

      // Update scraper run with final results
      await databaseService.updateScraperRun(scraperRunId, {
        status: 'completed',
        completedAt: new Date().toISOString(),
        apifyRunId: result.metadata?.runId,
        durationSeconds: result.metadata?.executionTime ? Math.round(result.metadata.executionTime / 1000) : undefined,
        metadata: {
          businessesProcessed: result.data?.length || 0,
          businessesSaved: saveResult.savedBusinesses,
          reviewsSaved: saveResult.savedReviews,
          newBusinesses: saveResult.newBusinesses,
          updatedBusinesses: saveResult.updatedBusinesses
        }
      })

      console.log('💾 Data saved successfully')
      console.log(`📊 Stats: ${stats.totalBusinesses} businesses, ${stats.totalReviews} reviews`)
      console.log(`📊 Save Results: ${saveResult.savedBusinesses} businesses (${saveResult.newBusinesses} new, ${saveResult.updatedBusinesses} updated), ${saveResult.savedReviews} reviews`)

      // Return success response
      return NextResponse.json({
        success: true,
        scraperRunId: scraperRunId,
        data: {
          businesses: result.data || [],
          stats,
          saveResult,
        },
        meta: {
          runId: result.metadata?.runId,
          executionTime: result.metadata?.executionTime,
          urlsProcessed: validUrls.length,
          urlsFiltered: body.urls.length - validUrls.length,
        },
      })

    } catch (scrapingError) {
      console.error('❌ Scraping error:', scrapingError)

      await databaseService.updateScraperRun(scraperRunId, {
        status: 'failed',
        completedAt: new Date().toISOString(),
        metadata: {
          error: scrapingError instanceof Error ? scrapingError.message : 'Unknown scraping error'
        }
      })

      return NextResponse.json(
        {
          error: 'Scraping failed',
          details: scrapingError instanceof Error ? scrapingError.message : 'Unknown error',
          scraperRunId: scraperRunId
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const scraperRunId = searchParams.get('scraperRunId')
    const limit = parseInt(searchParams.get('limit') || '10')

    const databaseService = new MultiSourceDatabaseService()

    if (scraperRunId) {
      // Get specific scraper run results
      const scraperRun = await databaseService.getScraperRun(scraperRunId)
      const businesses = await databaseService.getBusinessesWithSourcesAndReviews(limit)

      return NextResponse.json({
        success: true,
        scraperRun,
        businesses,
        count: businesses.length,
      })
    } else {
      // Get recent businesses with sources and reviews
      const businesses = await databaseService.getBusinessesWithSourcesAndReviews(limit)
      const stats = await databaseService.getStats()

      return NextResponse.json({
        success: true,
        businesses,
        stats,
        count: businesses.length,
      })
    }

  } catch (error) {
    console.error('❌ GET API error:', error)

    return NextResponse.json(
      {
        error: 'Failed to fetch data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
