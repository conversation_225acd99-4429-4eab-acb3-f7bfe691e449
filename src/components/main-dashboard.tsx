'use client'

import { useState } from 'react'

export function MainDashboard() {
  const [urls, setUrls] = useState('')
  const [maxReviews, setMaxReviews] = useState(10)
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      const urlList = urls.split('\n').filter(url => url.trim())

      if (urlList.length === 0) {
        throw new Error('Please provide at least one Google Maps URL')
      }

      const response = await fetch('/api/fetch-business', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          urls: urlList,
          maxReviews,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch business data')
      }

      setResult(data)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Unified Business + Reviews Scraper</h1>

      <form onSubmit={handleSubmit} style={{ marginBottom: '20px' }}>
        <div style={{ marginBottom: '10px' }}>
          <label>Google Maps URLs (one per line):</label>
          <br />
          <textarea
            value={urls}
            onChange={(e) => setUrls(e.target.value)}
            rows={4}
            cols={80}
            placeholder="https://www.google.com/maps/place/..."
            required
            style={{ width: '100%', padding: '8px' }}
          />
        </div>

        <div style={{ marginBottom: '10px' }}>
          <label>Max Reviews per Business:</label>
          <br />
          <input
            type="number"
            min="1"
            max="100"
            value={maxReviews}
            onChange={(e) => setMaxReviews(parseInt(e.target.value))}
            style={{ padding: '8px' }}
          />
        </div>

        <button
          type="submit"
          disabled={isLoading}
          style={{
            padding: '10px 20px',
            backgroundColor: isLoading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? 'Fetching...' : 'Fetch Business Data'}
        </button>
      </form>

      {error && (
        <div style={{
          color: 'red',
          backgroundColor: '#ffe6e6',
          padding: '10px',
          marginBottom: '20px',
          border: '1px solid red'
        }}>
          Error: {error}
        </div>
      )}

      {result && (
        <div>
          <h2>Results:</h2>
          <pre style={{
            backgroundColor: '#f5f5f5',
            padding: '20px',
            overflow: 'auto',
            border: '1px solid #ddd',
            whiteSpace: 'pre-wrap'
          }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
