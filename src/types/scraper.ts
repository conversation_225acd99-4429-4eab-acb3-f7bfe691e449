// Types for the scraper API

export interface ScrapeRequest {
  urls: string[]
  maxReviews?: number
  sorting?: '1' | '2' | '3' | '4' | '5' // 1=Most relevant, 2=Newest, 3=Highest, 4=Lowest, 5=Most likes
  lang?: string
}

export interface ScrapeResponse {
  success: boolean
  data?: ScrapedLocationData[]
  error?: string
  message?: string
}

export interface BusinessScrapeRequest {
  urls: string[]
  extractContacts?: boolean
  extractHours?: boolean
  extractPhotos?: boolean
  maxPhotos?: number
}

export interface BusinessScrapeResponse {
  success: boolean
  data?: ScrapedBusinessData[]
  error?: string
  message?: string
}

export interface ScrapedReview {
  id: string
  publishedAt: string
  updatedAt: string
  rating: number
  review: string
  language: string
  likes: number
  reply?: string
  author: {
    name: string
    avatar: string
    url: string
    id: string
  }
  images?: Array<{
    id: string
    url: string
    size: {
      width: number
      height: number
    }
    location: string
    caption: string | null
  }>
  source: string
}

export interface ScrapedLocationData {
  url: string
  location: any
  reviews: ScrapedReview[]
  count: number
}

export interface ScrapedBusinessData {
  url: string
  business: {
    id: string
    name: string
    address: string
    phone?: string
    website?: string
    category: string
    rating: number
    totalReviews: number
    priceLevel?: string
    hours?: Record<string, string>
    coordinates?: {
      lat: number
      lng: number
    }
    photos?: Array<{
      url: string
      caption?: string
    }>
    verified?: boolean
    plusCode?: string
    url: string
  }
  extractedAt: string
}
