-- Multi-Source Business & Review Tracking Database Schema
-- Complete schema for new database supporting multiple sources (Google, Yelp, TripAdvisor, etc.)

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create trigger function for auto-updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ============================================================================
-- CORE TABLES
-- ============================================================================

-- 1. Sources table - defines available data sources
CREATE TABLE public.sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    name TEXT UNIQUE NOT NULL, -- 'google', 'yelp', 'tripadvisor', etc.
    display_name TEXT NOT NULL, -- 'Google Maps', 'Yelp', 'TripAdvisor', etc.
    base_url TEXT, -- Base URL for the source
    metadata JSONB DEFAULT '{}'
);

-- 2. Businesses table - central business entities
CREATE TABLE public.businesses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    phone TEXT,
    website TEXT,
    category TEXT,
    rating DECIMAL(3,2) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
    total_reviews INTEGER DEFAULT 0,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    version INTEGER DEFAULT 1, -- Version counter for business data
    metadata JSONB DEFAULT '{}'
);

-- 3. Business sources mapping table
CREATE TABLE public.business_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    source_id UUID NOT NULL REFERENCES public.sources(id) ON DELETE CASCADE,
    source_business_id TEXT NOT NULL, -- Source-specific business ID (e.g., Google Place ID)
    source_url TEXT, -- Source-specific URL
    source_metadata JSONB DEFAULT '{}', -- Source-specific metadata
    last_scraped_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(source_id, source_business_id) -- Prevent duplicate source entries
);

-- 4. Reviews table - all reviews linked to main business but tagged with source
CREATE TABLE public.reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    source_id UUID NOT NULL REFERENCES public.sources(id) ON DELETE CASCADE,
    source_review_id TEXT NOT NULL, -- Source-specific review ID
    author_name TEXT NOT NULL,
    author_id TEXT,
    author_avatar TEXT,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    language TEXT,
    likes INTEGER DEFAULT 0,
    published_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_review_at TIMESTAMP WITH TIME ZONE,
    reply_text TEXT,
    reply_published_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    UNIQUE(source_id, source_review_id, business_id) -- Prevent duplicate reviews
);

-- 5. Scraper runs table - tracks each scraping operation
CREATE TABLE public.scraper_runs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source_id UUID NOT NULL REFERENCES public.sources(id) ON DELETE CASCADE,
    actor_name TEXT NOT NULL, -- Apify actor name
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    apify_run_id TEXT,
    apify_dataset_id TEXT,
    storage_path TEXT, -- Path to stored data
    duration_seconds INTEGER,
    cost DECIMAL(10,4), -- Cost in USD
    parameters JSONB DEFAULT '{}', -- Scraper parameters
    metadata JSONB DEFAULT '{}'
);

-- 6. Business scraper runs junction table (tracks which runs updated which businesses)
CREATE TABLE public.business_scraper_runs (
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    scraper_run_id UUID NOT NULL REFERENCES public.scraper_runs(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    version_before INTEGER NOT NULL,
    version_after INTEGER NOT NULL,
    changes_made JSONB DEFAULT '{}', -- What changed in this update
    PRIMARY KEY (business_id, scraper_run_id)
);

-- 7. User businesses table (for future user linking)
CREATE TABLE public.user_businesses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID NOT NULL, -- Will reference users table when created
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    relationship_type TEXT DEFAULT 'owner' CHECK (relationship_type IN ('owner', 'manager', 'employee', 'follower')),
    metadata JSONB DEFAULT '{}'
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Sources indexes
CREATE INDEX idx_sources_name ON public.sources(name);

-- Businesses indexes
CREATE INDEX idx_businesses_name ON public.businesses(name);
CREATE INDEX idx_businesses_category ON public.businesses(category);
CREATE INDEX idx_businesses_rating ON public.businesses(rating DESC);
CREATE INDEX idx_businesses_location ON public.businesses(latitude, longitude);
CREATE INDEX idx_businesses_version ON public.businesses(version);
CREATE INDEX idx_businesses_created_at ON public.businesses(created_at DESC);

-- Business sources indexes
CREATE INDEX idx_business_sources_business_id ON public.business_sources(business_id);
CREATE INDEX idx_business_sources_source_id ON public.business_sources(source_id);
CREATE INDEX idx_business_sources_source_business_id ON public.business_sources(source_business_id);
CREATE INDEX idx_business_sources_last_scraped ON public.business_sources(last_scraped_at DESC);

-- Reviews indexes
CREATE INDEX idx_reviews_business_id ON public.reviews(business_id);
CREATE INDEX idx_reviews_source_id ON public.reviews(source_id);
CREATE INDEX idx_reviews_rating ON public.reviews(rating);
CREATE INDEX idx_reviews_published_at ON public.reviews(published_at DESC);
CREATE INDEX idx_reviews_author_id ON public.reviews(author_id);

-- Scraper runs indexes
CREATE INDEX idx_scraper_runs_source_id ON public.scraper_runs(source_id);
CREATE INDEX idx_scraper_runs_status ON public.scraper_runs(status);
CREATE INDEX idx_scraper_runs_started_at ON public.scraper_runs(started_at DESC);
CREATE INDEX idx_scraper_runs_actor_name ON public.scraper_runs(actor_name);

-- Business scraper runs indexes
CREATE INDEX idx_business_scraper_runs_business_id ON public.business_scraper_runs(business_id);
CREATE INDEX idx_business_scraper_runs_scraper_run_id ON public.business_scraper_runs(scraper_run_id);
CREATE INDEX idx_business_scraper_runs_created_at ON public.business_scraper_runs(created_at DESC);

-- User businesses indexes
CREATE INDEX idx_user_businesses_user_id ON public.user_businesses(user_id);
CREATE INDEX idx_user_businesses_business_id ON public.user_businesses(business_id);
CREATE INDEX idx_user_businesses_relationship_type ON public.user_businesses(relationship_type);

-- ============================================================================
-- TRIGGERS FOR AUTO-UPDATING TIMESTAMPS
-- ============================================================================

CREATE TRIGGER update_sources_updated_at 
    BEFORE UPDATE ON public.sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_businesses_updated_at 
    BEFORE UPDATE ON public.businesses 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_sources_updated_at 
    BEFORE UPDATE ON public.business_sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at 
    BEFORE UPDATE ON public.reviews 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scraper_runs_updated_at 
    BEFORE UPDATE ON public.scraper_runs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_businesses_updated_at
    BEFORE UPDATE ON public.user_businesses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- ROW LEVEL SECURITY
-- ============================================================================

-- Enable RLS
ALTER TABLE public.sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scraper_runs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_scraper_runs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_businesses ENABLE ROW LEVEL SECURITY;

-- Service role policies (full access)
CREATE POLICY "Allow service role all operations on sources"
ON public.sources FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on businesses"
ON public.businesses FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on business_sources"
ON public.business_sources FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on reviews"
ON public.reviews FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on scraper_runs"
ON public.scraper_runs FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on business_scraper_runs"
ON public.business_scraper_runs FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Allow service role all operations on user_businesses"
ON public.user_businesses FOR ALL TO service_role USING (true) WITH CHECK (true);

-- Anon role policies (read access)
CREATE POLICY "Allow anon read access to sources"
ON public.sources FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to businesses"
ON public.businesses FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to business_sources"
ON public.business_sources FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to reviews"
ON public.reviews FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to scraper_runs"
ON public.scraper_runs FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to business_scraper_runs"
ON public.business_scraper_runs FOR SELECT TO anon USING (true);

CREATE POLICY "Allow anon read access to user_businesses"
ON public.user_businesses FOR SELECT TO anon USING (true);

-- ============================================================================
-- INITIAL DATA - DEFAULT SOURCES
-- ============================================================================

-- Insert default sources
INSERT INTO public.sources (name, display_name, base_url, metadata) VALUES
('google', 'Google Maps', 'https://maps.google.com', '{"actor": "compass/crawler-google-places", "free_tier": true}'),
('yelp', 'Yelp', 'https://www.yelp.com', '{"actor": "TBD", "free_tier": false}'),
('tripadvisor', 'TripAdvisor', 'https://www.tripadvisor.com', '{"actor": "TBD", "free_tier": false}'),
('foursquare', 'Foursquare', 'https://foursquare.com', '{"actor": "TBD", "free_tier": false}');

-- ============================================================================
-- HELPFUL VIEWS
-- ============================================================================

-- View to get businesses with all their source information
CREATE VIEW public.businesses_with_sources AS
SELECT
    b.*,
    json_agg(
        json_build_object(
            'source_name', s.name,
            'source_display_name', s.display_name,
            'source_business_id', bs.source_business_id,
            'source_url', bs.source_url,
            'last_scraped_at', bs.last_scraped_at,
            'source_metadata', bs.source_metadata
        )
    ) FILTER (WHERE s.id IS NOT NULL) as sources
FROM public.businesses b
LEFT JOIN public.business_sources bs ON b.id = bs.business_id
LEFT JOIN public.sources s ON bs.source_id = s.id
GROUP BY b.id, b.created_at, b.updated_at, b.name, b.address, b.phone,
         b.website, b.category, b.rating, b.total_reviews, b.latitude,
         b.longitude, b.version, b.metadata;

-- View to get businesses with review counts by source
CREATE VIEW public.businesses_with_review_stats AS
SELECT
    b.*,
    COALESCE(review_stats.total_reviews_scraped, 0) as total_reviews_scraped,
    COALESCE(review_stats.avg_rating_scraped, 0) as avg_rating_scraped,
    review_stats.reviews_by_source
FROM public.businesses b
LEFT JOIN (
    SELECT
        r.business_id,
        COUNT(*) as total_reviews_scraped,
        AVG(r.rating::decimal) as avg_rating_scraped,
        json_agg(
            json_build_object(
                'source_name', s.name,
                'review_count', source_counts.review_count,
                'avg_rating', source_counts.avg_rating
            )
        ) as reviews_by_source
    FROM public.reviews r
    JOIN public.sources s ON r.source_id = s.id
    JOIN (
        SELECT
            business_id,
            source_id,
            COUNT(*) as review_count,
            AVG(rating::decimal) as avg_rating
        FROM public.reviews
        GROUP BY business_id, source_id
    ) source_counts ON r.business_id = source_counts.business_id AND r.source_id = source_counts.source_id
    GROUP BY r.business_id
) review_stats ON b.id = review_stats.business_id;

-- ============================================================================
-- BUSINESS DEDUPLICATION FUNCTION
-- ============================================================================

-- Function to find potential duplicate businesses based on name and location
CREATE OR REPLACE FUNCTION find_potential_duplicates(
    business_name TEXT,
    business_address TEXT,
    latitude_param DECIMAL DEFAULT NULL,
    longitude_param DECIMAL DEFAULT NULL,
    distance_threshold_km DECIMAL DEFAULT 0.1
)
RETURNS TABLE(
    business_id UUID,
    name TEXT,
    address TEXT,
    similarity_score DECIMAL,
    distance_km DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        b.id as business_id,
        b.name,
        b.address,
        -- Simple similarity score based on name and address similarity
        (
            CASE
                WHEN LOWER(b.name) = LOWER(business_name) THEN 1.0
                WHEN LOWER(b.name) LIKE '%' || LOWER(business_name) || '%' OR LOWER(business_name) LIKE '%' || LOWER(b.name) || '%' THEN 0.8
                ELSE 0.0
            END +
            CASE
                WHEN LOWER(b.address) = LOWER(business_address) THEN 1.0
                WHEN LOWER(b.address) LIKE '%' || LOWER(business_address) || '%' OR LOWER(business_address) LIKE '%' || LOWER(b.address) || '%' THEN 0.6
                ELSE 0.0
            END
        ) / 2.0 as similarity_score,
        -- Calculate distance if coordinates are provided
        CASE
            WHEN latitude_param IS NOT NULL AND longitude_param IS NOT NULL AND b.latitude IS NOT NULL AND b.longitude IS NOT NULL THEN
                -- Haversine formula approximation for small distances
                6371 * acos(
                    cos(radians(latitude_param)) * cos(radians(b.latitude)) *
                    cos(radians(b.longitude) - radians(longitude_param)) +
                    sin(radians(latitude_param)) * sin(radians(b.latitude))
                )
            ELSE NULL
        END as distance_km
    FROM public.businesses b
    WHERE
        -- Name similarity filter
        (LOWER(b.name) LIKE '%' || LOWER(business_name) || '%' OR LOWER(business_name) LIKE '%' || LOWER(b.name) || '%')
        AND
        -- Distance filter (if coordinates provided)
        (
            latitude_param IS NULL OR longitude_param IS NULL OR b.latitude IS NULL OR b.longitude IS NULL
            OR
            6371 * acos(
                cos(radians(latitude_param)) * cos(radians(b.latitude)) *
                cos(radians(b.longitude) - radians(longitude_param)) +
                sin(radians(latitude_param)) * sin(radians(b.latitude))
            ) <= distance_threshold_km
        )
    ORDER BY similarity_score DESC, distance_km ASC NULLS LAST;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- SCHEMA COMPLETE
-- ============================================================================

RAISE NOTICE '=================================================================';
RAISE NOTICE 'MULTI-SOURCE BUSINESS & REVIEW TRACKING SCHEMA CREATED!';
RAISE NOTICE '=================================================================';
RAISE NOTICE 'Tables created:';
RAISE NOTICE '- sources (4 default sources inserted)';
RAISE NOTICE '- businesses (main business entities)';
RAISE NOTICE '- business_sources (source mappings)';
RAISE NOTICE '- reviews (multi-source reviews)';
RAISE NOTICE '- scraper_runs (scraping operations)';
RAISE NOTICE '- business_scraper_runs (version tracking)';
RAISE NOTICE '- user_businesses (future user linking)';
RAISE NOTICE '';
RAISE NOTICE 'Views created:';
RAISE NOTICE '- businesses_with_sources';
RAISE NOTICE '- businesses_with_review_stats';
RAISE NOTICE '';
RAISE NOTICE 'Functions created:';
RAISE NOTICE '- find_potential_duplicates()';
RAISE NOTICE '=================================================================';
