# Multi-Source Business & Review Tracking System

A Next.js application that scrapes business information and reviews from multiple sources (Google Maps, Yelp, TripAdvisor, etc.) using Apify actors, with intelligent business deduplication and multi-source data persistence in Supabase.

## 🆕 New Multi-Source Features

- **Business Deduplication**: Automatically detects and merges same businesses from different sources
- **Version Tracking**: Tracks business data changes with version counters
- **Multi-Source Support**: Ready for Google Maps, Yelp, TripAdvisor, and more
- **Source Mapping**: Links businesses to their source-specific IDs and URLs
- **Unified Reviews**: Aggregates reviews from all sources under main business entities
- **Enhanced Analytics**: Statistics and insights across multiple data sources

## 🚀 Tech Stack

### **Frontend**
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling (minimal usage)
- **React** - UI components (simple form + JSON display)

### **Backend**
- **Next.js API Routes** - Server-side endpoints
- **Apify SDK** - Web scraping service integration
- **Supabase** - PostgreSQL database with real-time features
- **Node.js** - Runtime environment

### **External Services**
- **Apify** - Web scraping platform (`compass/crawler-google-places` actor)
- **Supabase** - Database hosting and management
- **Google Maps** - Data source for business information and reviews

### **Development Tools**
- **npm** - Package management
- **ESLint** - Code linting
- **TypeScript** - Static type checking

## 📁 Project Structure

```
/
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   └── fetch-business/
│   │   │       └── route.ts          # Main API endpoint
│   │   ├── globals.css               # Global styles
│   │   ├── layout.tsx                # Root layout
│   │   └── page.tsx                  # Home page
│   ├── components/
│   │   ├── main-dashboard.tsx        # Main UI component
│   │   └── ui/                       # Reusable UI components
│   └── services/
│       ├── apify/                    # Apify integration
│       │   ├── apify-service.ts      # Main service class
│       │   ├── base-scraper.ts       # Base scraper functionality
│       │   ├── types.ts              # Type definitions
│       │   └── scrapers/
│       │       ├── business-scraper.ts    # Business-only scraper
│       │       └── unified-scraper.ts     # Business + reviews scraper
│       └── database/                 # Database integration
│           ├── database-service.ts   # Main database service
│           ├── supabase-client.ts    # Supabase client factory
│           └── types.ts              # Database type definitions
├── schema.sql                        # Database schema
├── .env.local                        # Environment variables
├── package.json                      # Dependencies and scripts
└── README.md                         # This file
```

## 🎯 Frontend Guide

### **Simple UI Architecture**

The frontend is intentionally minimal and focuses on functionality over aesthetics:

**Main Component**: `src/components/main-dashboard.tsx`
- Simple form with textarea for Google Maps URLs
- Number input for max reviews (1-100)
- Submit button that triggers scraping
- JSON display of results using `JSON.stringify()`

**No Complex UI Framework**:
- No component libraries (Material-UI, Ant Design, etc.)
- No state management (Redux, Zustand, etc.)
- No routing beyond Next.js App Router
- No real-time updates or WebSockets

### **Frontend Flow**

1. **User Input**: Enter Google Maps URLs (one per line)
2. **Form Submission**: POST request to `/api/fetch-business`
3. **Loading State**: Button disabled, shows "Fetching..."
4. **Results Display**: Raw JSON response displayed in `<pre>` tag
5. **Error Handling**: Error messages shown in red box

### **Key Frontend Files**

- `src/app/page.tsx` - Renders the main dashboard
- `src/components/main-dashboard.tsx` - Form and results display
- `src/app/globals.css` - Minimal global styles

## 🔧 Backend Guide

### **API Architecture**

**Single Endpoint**: `/api/fetch-business`
- **POST**: Scrape Google Maps URLs and save to database
- **GET**: Retrieve saved business data (optional query params)

### **Service Layer Architecture**

#### **1. Apify Service (`src/services/apify/`)**

**Main Classes**:
- `ApifyService` - Factory for scraper instances
- `UnifiedScraper` - Scrapes business info + reviews in one request
- `BusinessScraper` - Scrapes business info only (legacy)

**Key Features**:
- Uses `compass/crawler-google-places` actor (free tier)
- Handles rate limiting and retries
- Processes raw Apify data into structured format
- Supports multiple URLs in single request

**Usage Example**:
```typescript
const apifyService = new ApifyService({ token: process.env.APIFY_TOKEN })
const unifiedScraper = new UnifiedScraper(apifyService.getConfig())

const result = await unifiedScraper.fetchBusiness({
  urls: ['https://www.google.com/maps/place/...'],
  maxReviews: 10,
  reviewsSort: 'newest',
  language: 'en'
})
```

#### **2. Database Service (`src/services/database/`)**

**Main Classes**:
- `DatabaseService` - Main database operations
- `SupabaseClientFactory` - Manages Supabase connections

**Key Features**:
- Auto-initializes Supabase connection
- Handles unified business + reviews data
- Supports batch operations for reviews
- Manages scrape sessions for tracking

**Usage Example**:
```typescript
const databaseService = new DatabaseService()

// Save unified data (business + reviews)
const result = await databaseService.saveUnifiedData(scrapedData, {
  sessionId: 'session-uuid',
  metadata: { scraperType: 'unified' }
})
```

### **Available Scrapers**

#### **UnifiedScraper** (Recommended)
- **Purpose**: Scrapes business information AND reviews in one request
- **Actor**: `compass/crawler-google-places`
- **Input**: URLs, maxReviews, reviewsSort, language
- **Output**: Business data with embedded reviews
- **Cost**: Free tier available

#### **BusinessScraper** (Legacy)
- **Purpose**: Scrapes business information only
- **Actor**: `compass/crawler-google-places`
- **Input**: URLs, basic parameters
- **Output**: Business data without reviews
- **Use Case**: When you only need business info

### **Database Operations**

#### **Saving Records**

**Unified Data (Business + Reviews)**:
```typescript
await databaseService.saveUnifiedData(results, {
  sessionId: sessionId,
  metadata: { scraperType: 'unified' }
})
```

**Individual Business**:
```typescript
await databaseService.saveBusinesses(businessResults, {
  sessionId: sessionId
})
```

**Individual Reviews**:
```typescript
await databaseService.saveReviews(reviewResults, {
  sessionId: sessionId
})
```

#### **Retrieving Records**

**Get Businesses with Reviews**:
```typescript
const businesses = await databaseService.getBusinessesWithReviews(sessionId, limit)
```

**Get Recent Sessions**:
```typescript
const sessions = await databaseService.getRecentSessions(10)
```

**Get Database Stats**:
```typescript
const stats = await databaseService.getStats()
```

## 🗄️ Database Schema

### **Schema Files**
- **New Schema**: `db/schema.sql` - Complete multi-source schema for new databases
- **Migration Guide**: `MULTI-SOURCE-MIGRATION-GUIDE.md` - Detailed migration instructions

### **Core Tables**

#### **1. sources**
Defines available data sources:
```sql
- id (UUID, Primary Key)
- name (TEXT, Unique) - 'google', 'yelp', 'tripadvisor'
- display_name (TEXT) - 'Google Maps', 'Yelp', 'TripAdvisor'
- base_url (TEXT), metadata (JSONB)
- timestamps (created_at, updated_at)
```

#### **2. businesses**
Central business entities (deduplicated):
```sql
- id (UUID, Primary Key)
- name, address, phone, website, category
- rating (DECIMAL), total_reviews (INTEGER)
- coordinates (latitude, longitude)
- version (INTEGER) - Version tracking
- metadata (JSONB)
- timestamps (created_at, updated_at)
```

#### **3. business_sources**
Maps businesses to source-specific IDs:
```sql
- id (UUID, Primary Key)
- business_id (UUID, Foreign Key)
- source_id (UUID, Foreign Key)
- source_business_id (TEXT) - Google Place ID, Yelp ID, etc.
- source_url (TEXT), source_metadata (JSONB)
- last_scraped_at (TIMESTAMP)
- UNIQUE(source_id, source_business_id)
```

#### **4. reviews**
Multi-source reviews linked to businesses:
```sql
- id (UUID, Primary Key)
- business_id (UUID, Foreign Key)
- source_id (UUID, Foreign Key)
- source_review_id (TEXT)
- author_name, author_id, author_avatar
- rating (INTEGER), review_text, language
- likes (INTEGER)
- published_at, updated_review_at
- reply_text, reply_published_at
- metadata (JSONB)
- UNIQUE(source_id, source_review_id, business_id)
```

#### **5. scraper_runs**
Tracks scraping operations:
```sql
- id (UUID, Primary Key)
- source_id (UUID, Foreign Key)
- actor_name (TEXT), status (TEXT)
- started_at, completed_at
- apify_run_id, apify_dataset_id, storage_path
- duration_seconds (INTEGER), cost (DECIMAL)
- parameters (JSONB), metadata (JSONB)
```

#### **6. business_scraper_runs**
Version tracking for business updates:
```sql
- business_id (UUID, Foreign Key)
- scraper_run_id (UUID, Foreign Key)
- version_before, version_after (INTEGER)
- changes_made (JSONB)
- created_at (TIMESTAMP)
```

#### **7. user_businesses**
Future user-to-business relationships:
```sql
- id (UUID, Primary Key)
- user_id (UUID), business_id (UUID, Foreign Key)
- relationship_type ('owner' | 'manager' | 'employee' | 'follower')
- metadata (JSONB)
- timestamps (created_at, updated_at)
```

### **Advanced Features**
- **Business Deduplication**: `find_potential_duplicates()` function
- **Helpful Views**: `businesses_with_sources`, `businesses_with_review_stats`
- **Version Tracking**: Automatic business version management
- **Multi-Source Support**: Ready for new sources without schema changes

### **Setting Up New Database**

1. **Create Supabase Project**
2. **Run Schema**:
   ```sql
   -- Copy and run multi-source-schema.sql in Supabase SQL Editor
   ```
3. **Verify Setup**:
   - Check that 7 tables are created
   - Verify 4 default sources are inserted
   - Test deduplication function

## 🚀 Setup & Installation

### **Prerequisites**
- **Node.js 18+**
- **npm** or **yarn**
- **Apify Account** (free tier available)
- **Supabase Account** (free tier available)

### **Environment Setup**

1. **Clone and Install**:
```bash
git clone <repository-url>
cd apify
npm install
```

2. **Environment Variables**:
Create `.env.local` with:
```env
# Apify Configuration
APIFY_TOKEN=your_apify_token_here

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

3. **Database Setup**:
   - Create Supabase project
   - Run `schema.sql` in SQL Editor
   - Verify tables are created

4. **Start Development**:
```bash
npm run dev
```

5. **Test the Application**:
   - Visit `http://localhost:3000`
   - Enter a Google Maps URL
   - Click "Fetch Business Data"
   - Check results and database

### **Getting API Keys**

#### **Apify Token**:
1. Sign up at [apify.com](https://apify.com)
2. Go to Account → Integrations
3. Copy your API token

#### **Supabase Keys**:
1. Create project at [supabase.com](https://supabase.com)
2. Go to Settings → API
3. Copy URL, anon key, and service_role key

## 📋 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - TypeScript checking

## 🎯 Current Features

- ✅ **Unified Scraping**: Business info + reviews in one request
- ✅ **Database Persistence**: All data saved to Supabase
- ✅ **Session Tracking**: Track scraping operations
- ✅ **Error Handling**: Robust error management
- ✅ **Type Safety**: Full TypeScript support
- ✅ **Free Tier**: Uses free Apify actor
- ✅ **Simple UI**: Minimal, functional interface

## 🔍 Troubleshooting

### **Common Issues**

1. **"Supabase factory not initialized"**:
   - Check environment variables are set
   - Verify Supabase keys are correct

2. **"Actor is not rented"**:
   - Ensure using `compass/crawler-google-places` (free)
   - Check Apify token is valid

3. **"Column does not exist"**:
   - Run `schema.sql` to create tables
   - Verify database schema is applied

4. **Timestamp errors**:
   - Fixed in current version
   - Empty strings converted to null for timestamps

### **Debug Mode**
Check browser console and terminal for detailed error messages during scraping operations.